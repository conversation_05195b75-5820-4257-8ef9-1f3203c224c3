---
description:
globs:
alwaysApply: false
---
# Java RocketMQ 技术栈规范

## 消息队列设计规范
1. 架构分层
   - 基础配置层：继承`QueueBaseConfig`定义队列配置
   - 队列实现层：继承`MicroBaseQueue`实现消息生产
   - 消费者实现层：继承`BaseConsumer`实现消息消费
   - 业务处理层：继承`BaseDisposer`实现具体业务逻辑

2. 命名规范
   - 配置类命名：`Queue{业务名}Config`
   - 生产者类命名：`Queue{业务名}`
   - 消费者类命名：`{业务名}Consumer`
   - 处理器类命名：`{业务名}Disposer`

3. 配置管理
   - 使用`@ConfigurationProperties`注解配置属性
   - 配置必须包含：nameServerAddr、groupName、topic、queueName等基础字段
   - 配置前缀规范：`ets-config.rocketmq.{业务名}`

## 消息发送规范
1. 生产者配置
   ```java
   @Bean(name = "businessProducer")
   public DefaultMQProducer businessProducer() throws MQClientException {
       return super.createProducer(groupName, nameServerAddr, retryTimes);
   }
   ```

2. 消息发送方式
   ```java
   public void push(BaseDisposer job) throws BizException {
       DefaultMQProducer producer = (DefaultMQProducer) ApplicationContextHelper.getBean(ProducerBeanName);
       if (producer == null) {
           return;
       }
       JobDto jobDto = getJobDto(job);
       super.pushRocketMqJob(producer, config.getTopic(), config.getQueueName(), jobDto);
   }
   ```

3. 延时消息发送
   ```java
   public void push(BaseDisposer job, int delayLevel) {
       DefaultMQProducer producer = (DefaultMQProducer) ApplicationContextHelper.getBean(ProducerBeanName);
       if (producer != null) {
           JobDto jobDto = getJobDto(job);
           super.pushRocketMqJob(producer, config.getTopic(), config.getQueueName(), jobDto, delayLevel);
       }
   }
   ```

## 消息消费规范
1. 消费者配置
   ```java
   @Bean(name = "defaultConsumer")
   public DefaultMQPushConsumer defaultConsumer() throws MQClientException {
       return super.consumerStart(new BusinessConsumer(),
               groupName,
               nameServerAddr,
               topic,
               queueName,
               consumeThreadMin,
               consumeThreadMax,
               false);
   }
   ```

2. 消费者实现
   ```java
   @Component
   public class BusinessConsumer extends BaseConsumer implements MessageListenerConcurrently {
       @Override
       public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext context) {
           DefaultMQProducer producer = (DefaultMQProducer) ApplicationContextHelper.getBean(ProducerBeanName);
           if (producer == null) {
               return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
           }
           return super.handleRocketMq(producer, list, context);
       }
   }
   ```

## 业务处理器规范
1. 处理器实现
   ```java
   @NoArgsConstructor
   @Component(value = "BusinessJobBean")
   public class BusinessDisposer extends BaseDisposer {
       
       public BusinessDisposer(Object params) {
           super(params);
       }
       
       @Override
       public String getJobBeanName() {
           return "BusinessJobBean";
       }
       
       @Override
       public void execute(Object content) {
           BusinessDTO dto = super.getParamsObject(content, BusinessDTO.class);
           // 业务逻辑处理
       }
   }
   ```

2. 异常处理
   - 处理器必须捕获和处理业务异常，避免消息重复消费
   - 记录详细的错误日志，便于问题排查

## 最佳实践
1. 消息幂等性保证
   - 生成全局唯一ID，确保消息不会重复处理
   - 使用Redis缓存或数据库记录已处理的消息ID

2. 异步处理与事务一致性
   - 使用本地事务+异步消息的方式保证数据一致性
   - 对于重要业务，使用事务消息确保原子性

3. 监控与告警
   - 记录关键业务日志，包含业务标识和处理结果
   - 异常情况及时告警，避免消息堆积

4. 性能优化
   - 合理设置消费线程数，避免资源浪费
   - 消息处理逻辑尽量简单高效，避免长时间阻塞

5. 安全性
   - 生产环境禁止自动创建Topic
   - 敏感信息加密后再发送

## 实现示例

### 配置类示例
```java
@EqualsAndHashCode(callSuper = true)
@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "ets-config.rocketmq.business")
public class QueueBusinessConfig extends QueueBaseConfig {
    public final static String PRODUCER_BEAN_NAME = "businessProducer";
    private String nameServerAddr;
    private String groupName;
    private String topic;
    private String queueName;
    private Integer retryTimes;
    private Integer consumeThreadMin;
    private Integer consumeThreadMax;

    @Bean(name = PRODUCER_BEAN_NAME)
    public DefaultMQProducer businessProducer() throws MQClientException {
        return super.createProducer(groupName, nameServerAddr, retryTimes);
    }

    @Bean(name = "defaultBusinessConsumer")
    public DefaultMQPushConsumer defaultBusinessConsumer() throws MQClientException {
        return super.consumerStart(new BusinessConsumer(),
                groupName,
                nameServerAddr,
                topic,
                queueName,
                consumeThreadMin,
                consumeThreadMax,
                false);
    }
}
```

### 生产者示例
```java
@Component
public class QueueBusiness extends MicroBaseQueue {
    @Autowired
    QueueBusinessConfig businessConfig;

    @Override
    public void push(BaseDisposer job) throws BizException {
        DefaultMQProducer producer = (DefaultMQProducer) ApplicationContextHelper.getBean(QueueBusinessConfig.PRODUCER_BEAN_NAME);
        if (producer == null) {
            return;
        }
        JobDto jobDto = getJobDto(job);
        super.pushRocketMqJob(producer, businessConfig.getTopic(), businessConfig.getQueueName(), jobDto);
    }

    public void push(BaseDisposer job, int delayLevel) {
        DefaultMQProducer producer = (DefaultMQProducer) ApplicationContextHelper.getBean(QueueBusinessConfig.PRODUCER_BEAN_NAME);
        if (producer != null) {
            JobDto jobDto = getJobDto(job);
            super.pushRocketMqJob(producer, businessConfig.getTopic(), businessConfig.getQueueName(), jobDto, delayLevel);
        }
    }
}
```

### 业务使用示例
```java
@Component
public class BusinessExample {
    @Autowired
    private QueueBusiness queueBusiness;
    
    public void processAsyncTask(BusinessDTO dto) {
        // 创建处理器并传入参数
        BusinessDisposer disposer = new BusinessDisposer(dto);
        try {
            // 发送消息到队列
            queueBusiness.push(disposer);
        } catch (BizException e) {
            log.error("发送异步消息失败", e);
        }
    }
    
    public void processDelayTask(BusinessDTO dto, int delayMinutes) {
        // 创建处理器并传入参数
        BusinessDisposer disposer = new BusinessDisposer(dto);
        // 发送延时消息
        queueBusiness.push(disposer, calculateDelayLevel(delayMinutes));
    }
    
    private int calculateDelayLevel(int delayMinutes) {
        // 根据RocketMQ的延时级别计算
        if (delayMinutes <= 1) return 1; // 1s
        if (delayMinutes <= 5) return 2; // 5s
        if (delayMinutes <= 10) return 3; // 10s
        // 其他级别计算...
        return 4; // 30s
    }
}
```
