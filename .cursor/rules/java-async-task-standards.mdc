---
description:
globs:
alwaysApply: false
---
# Java 异步任务与定时任务规范

## 定时任务规范

1. 定时任务实现方式
   - 默认使用XXL-Job作为定时任务调度框架
   - 禁止使用Spring的`@Scheduled`注解
   - 所有定时任务必须可配置、可动态调整执行周期

2. 定时任务编码规范
   - 定时任务类命名：`{业务名}Job`
   - 定时任务方法命名：`execute{业务描述}`
   - 定时任务类要放在`app/job`目录下

3. 定时任务注解规范
   ```java
   @Component
   public class OrderCleanJob {
       
       @XxlJob("orderCleanHandler")
       public ReturnT<String> orderCleanHandler(String param) {
           try {
               // 业务逻辑
               return ReturnT.SUCCESS;
           } catch (Exception e) {
               log.error("订单清理任务异常", e);
               return ReturnT.FAIL;
           }
       }
   }
   ```

4. 定时任务最佳实践
   - 避免长时间执行，单次执行时间不应超过30分钟
   - 处理大批量数据时，应分批处理并记录处理位置
   - 每个定时任务应有详细的执行日志
   - 关键定时任务必须有监控告警机制
   - 定时任务执行失败应有重试机制

## 异步执行规范

1. 异步处理原则
   - 默认使用消息队列实现异步执行，不使用线程池
   - 只有在明确要求多线程并发处理时，才考虑使用线程池
   - 异步任务应该可追踪、可监控

2. 异步执行实现方式
   - 优先使用RocketMQ实现异步执行
   - 异步任务数据应序列化为DTO对象
   - 异步任务必须处理异常情况

3. 异步任务编码示例
   ```java
   @Component
   public class OrderService {
       
       @Autowired
       private QueueTask queueTask;
       
       public void processOrder(OrderDTO orderDTO) {
           // 同步处理部分
           
           // 异步处理部分
           OrderProcessDisposer disposer = new OrderProcessDisposer(orderDTO);
           try {
               queueTask.push(disposer);
           } catch (BizException e) {
               log.error("订单异步处理失败", e);
               // 处理失败逻辑
           }
       }
   }
   ```

4. 禁止使用线程池的情况
   - 不确定任务执行时长的场景
   - 需要保证数据一致性的场景
   - 需要记录执行历史的场景
   - 可能影响系统稳定性的场景

5. 允许使用线程池的情况
   - 明确要求高并发处理的场景
   - 计算密集型且无数据一致性要求的场景
   - 必须即时响应的场景

6. 线程池使用规范（仅适用于特殊场景）
   ```java
   @Configuration
   public class ThreadPoolConfig {
       
       @Bean
       public ThreadPoolExecutor threadPoolExecutor() {
           return new ThreadPoolExecutor(
               5,                       // 核心线程数
               10,                      // 最大线程数
               60L,                     // 空闲线程存活时间
               TimeUnit.SECONDS,        // 时间单位
               new LinkedBlockingQueue<>(100),  // 工作队列
               new ThreadFactoryBuilder()
                   .setNameFormat("business-pool-%d")
                   .build(),            // 线程工厂
               new ThreadPoolExecutor.CallerRunsPolicy()  // 拒绝策略
           );
       }
   }
   ```
