---
description: 
globs: 
alwaysApply: true
---
# Java 开发规范总览

本文档提供了Java开发的所有规范指南的概述，每个领域都有对应的详细规范文件。

## 规范文档列表

| 规范名称 | 文件路径 | 描述 |
|---------|---------|------|
| API设计规范 | `.cursor/rules/java-api-standards.mdc` | 包含API接口设计、命名规范、返回值规范、异常处理和实体类规范 |
| 架构设计规范 | `.cursor/rules/java-architecture-standards.mdc` | 包含分层架构、依赖注入和数据访问规范 |
| 异步任务与定时任务规范 | `.cursor/rules/java-async-task-standards.mdc` | 包含定时任务和异步执行的规范与最佳实践 |
| RocketMQ技术栈规范 | `.cursor/rules/java-rocketmq-standards.mdc` | 包含消息队列的设计、发送、消费与业务处理规范 |

## 如何使用规范文档

在开发过程中，可以通过以下方式引用规范：

1. API设计相关：`@java-api-standards.mdc`
2. 架构设计相关：`@java-architecture-standards.mdc`
3. 异步任务相关：`@java-async-task-standards.mdc`
4. RocketMQ相关：`@java-rocketmq-standards.mdc`

## 技术栈要求

1. 基于 Java 17 开发
2. 定时任务必须使用XXL-Job，禁止使用Spring的`@Scheduled`注解
3. 异步处理默认使用RocketMQ消息队列，不使用线程池（除非特殊场景）
4. 分层架构严格按照Controller-Business-Service模式实现

## 规范遵循原则

1. 所有新代码必须遵循规范要求
2. 对于历史代码，在修改时应当同时进行规范化调整
3. 代码审查时应当将规范遵循情况作为重要检查点
