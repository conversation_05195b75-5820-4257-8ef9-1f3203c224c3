---
description:
globs:
alwaysApply: false
---
# Java API 设计规范

## 接口设计原则
1. 不使用 Swagger API 文档注解
2. 请求方式选择：
   - POST 方式：适用于带有请求参数的接口，使用 `@RequestBody` 传参
   - GET 方式：仅适用于少量单一 ID 参数的数据获取接口，使用 `@RequestParams` 传参

## 命名规范
1. 接口路由：使用连字符（-）方式命名，禁止使用驼峰
   - 正确示例：`/api/user-profile`
   - 错误示例：`/api/userProfile`

2. DTO 命名：
   - 以 DTO 结尾
   - 存放位置：`common/dto` 目录下
   - 可按业务类型分类存放

3. VO 命名：
   - 以 VO 结尾
   - 存放位置：`common/vo` 目录下
   - 可按业务类型分类存放

## 接口返回值规范
1. 统一使用 `JsonResult<?>` 作为返回值包装类
2. 返回值结构：
```java
@Data
public class JsonResult<T> {
    private Integer code;    // 状态码：0-成功，-1-失败
    private String msg;      // 提示信息
    private T data;         // 数据
    
    // 成功静态方法
    public static <T> JsonResult<T> success(T data) {
        JsonResult<T> result = new JsonResult<>();
        result.setCode(0);
        result.setMsg("success");
        result.setData(data);
        return result;
    }
    
    // 成功静态方法 - 无数据
    public static <T> JsonResult<T> success() {
        return success(null);
    }
    
    // 失败静态方法
    public static <T> JsonResult<T> error(String msg) {
        JsonResult<T> result = new JsonResult<>();
        result.setCode(-1);
        result.setMsg(msg);
        return result;
    }
}
```

## 异常处理规范
1. 业务异常抛出规范：
   - 使用 `ToolsHelper.throwException()` 方法抛出业务异常
   - 异常信息应清晰明确，便于前端展示
   - 示例：
```java
// 抛出业务异常
if (condition) {
    ToolsHelper.throwException("用户名已存在");
}
```

2. 全局异常处理：
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public JsonResult<?> handleBusinessException(BusinessException e) {
        return JsonResult.error(e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public JsonResult<?> handleException(Exception e) {
        log.error("系统异常", e);
        return JsonResult.error("系统异常，请稍后重试");
    }
}
```

## 实体类规范
1. 注解使用：
   - 使用 `@Data` 注解自动生成 getter/setter/toString 等方法
   - 必要时可配合使用其他 Lombok 注解
   - 示例：
```java
@Data
public class UserDO {
    private Long id;
    private String username;
    private String password;
    private String email;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}

@Data
public class UserDTO {
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    private String email;
}

@Data
public class UserVO {
    private Long id;
    private String username;
    private String email;
    private String roleName;
}
```

## 代码示例

### 接口示例
```java
@RestController
@RequestMapping("/api/user")
public class UserController {
    // GET 示例 - 单一参数
    @GetMapping("/profile/{id}")
    public ResponseEntity<UserProfileVO> getProfile(@PathVariable Long id) {
        // 实现逻辑
    }

    // POST 示例 - 复杂参数
    @PostMapping("/update-profile")
    public ResponseEntity<Void> updateProfile(@RequestBody UserProfileUpdateDTO dto) {
        // 实现逻辑
    }
}
```

### DTO 示例
```java
public class UserProfileUpdateDTO {
    private Long userId;
    private String username;
    private String email;
    // getter 和 setter
}
```

### VO 示例
```java
public class UserProfileVO {
    private Long userId;
    private String username;
    private String email;
    // getter 和 setter
}
```
