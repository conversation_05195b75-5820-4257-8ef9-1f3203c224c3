---
description:
globs:
alwaysApply: false
---
# Java 架构设计规范

## 分层架构规范
1. Controller 层职责：
   - 仅负责参数接收和结果返回
   - 参数校验（可使用 @Valid 注解）
   - 不允许编写业务逻辑
   - 调用 Business 层处理业务

2. Business 层职责：
   - 存放具体的业务逻辑处理代码
   - 可以调用多个 Service 完成业务处理
   - 处理业务异常
   - 数据组装和转换
   - 命名以 Business 结尾

3. Service 层职责：
   - 负责数据库操作
   - 提供基础的数据访问服务
   - 不处理具体业务逻辑
   - 命名以 Service 结尾

## 依赖注入规范
1. 使用 `@Autowired` 注解进行依赖注入
2. 推荐在属性上直接使用 `@Autowired`
3. 建议将注入的属性声明为 final

## 数据访问规范
1. Service 层数据库操作规范：
   - 优先使用 MyBatis-Plus 提供的方法
   - 继承 ServiceImpl<Mapper, Entity> 获取基础 CRUD 能力
   - 复杂查询使用 LambdaQueryWrapper 构建
   - 特别复杂的 SQL 才使用自定义 Mapper 方法

2. Mapper 层规范：
   - 继承 BaseMapper<Entity>
   - 仅在必要时编写自定义方法
   - 复杂查询优先使用注解方式
   - 特别复杂的 SQL 才使用 XML

## 分层示例
```java
// Controller 层示例
@RestController
@RequestMapping("/api/user")
@Slf4j
public class UserController {
    @Autowired
    private UserBusiness userBusiness;

    @PostMapping("/register")
    public JsonResult<UserVO> register(@RequestBody @Valid UserRegisterDTO dto) {
        return JsonResult.success(userBusiness.registerUser(dto));
    }
    
    @GetMapping("/list")
    public JsonResult<List<UserVO>> list(@RequestParam(required = false) String keyword) {
        return JsonResult.success(userBusiness.listUsers(keyword));
    }
}

// Business 层示例
@Component
@Slf4j
public class UserBusiness {
    @Autowired
    private UserService userService;
    
    @Autowired
    private RoleService roleService;

    public UserVO registerUser(UserRegisterDTO dto) {
        // 业务逻辑处理
        if (userService.existsByUsername(dto.getUsername())) {
            ToolsHelper.throwException("用户名已存在");
        }
        
        UserDO user = new UserDO();
        BeanUtils.copyProperties(dto, user);
        userService.save(user);
        
        roleService.assignDefaultRole(user.getId());
        
        return convertToVO(user);
    }
}

// Service 层示例
@Service
@Slf4j
public class UserService extends ServiceImpl<UserMapper, UserDO> {
    
    public boolean existsByUsername(String username) {
        return count(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getUsername, username)) > 0;
    }
    
    public List<UserDO> listByKeyword(String keyword) {
        LambdaQueryWrapper<UserDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(keyword), UserDO::getUsername, keyword)
               .or()
               .like(StringUtils.isNotBlank(keyword), UserDO::getNickname, keyword);
        return list(wrapper);
    }
}

// 实体类示例
@Data
@TableName("t_user")
public class UserDO {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String username;
    private String password;
    private String email;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
```

## 技术要求
1. 基于 Java 17 开发
2. Controller 层日志使用规范：
   - 尽量避免在 Controller 层添加日志
   - 如需使用日志，必须使用 SLF4J 的注解方式
