<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ets</groupId>
        <artifactId>delivery</artifactId>
        <version>2.0.3-SNAPSHOT</version>
    </parent>

    <artifactId>risk-application</artifactId>
    <name>risk-application</name>
    <packaging>jar</packaging>
    <version>${risk.version}</version>
    <properties>
        <risk.version>2.0.3-SNAPSHOT</risk.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>ets-spring-boot-starter</artifactId>
            <version>1.2.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>ets-db-starter</artifactId>
            <version>1.2.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>ets-smooth-starter</artifactId>
            <version>1.2.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>4.0.2</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>4.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.234</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-pop-http-sign</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>ets-redisson-starter</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>delivery-feign</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>base-feign</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

