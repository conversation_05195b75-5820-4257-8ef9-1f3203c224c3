package com.ets.risk.application.common.vo.risk;

import com.ets.risk.application.common.consts.risk.RiskBusinessTypeEnum;
import com.ets.risk.application.common.consts.risk.RiskNotifyStatusEnum;
import com.ets.risk.application.common.consts.risk.RiskStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RiskAdminGetListVo {
    private String riskSn;
    private String businessSn;
    private Integer businessType;
    private String businessTypeStr;
    private String riskParams;
    private Integer riskStatus;
    private String riskStatusStr;
    private String riskRemark;
    private Integer notifyStatus;
    private String notifyStatusStr;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    public String getBusinessTypeStr() {
        return RiskBusinessTypeEnum.getDescByCode(this.businessType);
    }

    public String getRiskStatusStr() {
        return RiskStatusEnum.getDescByCode(this.riskStatus);
    }

    public String getNotifyStatusStr() {
        return RiskNotifyStatusEnum.getDescByCode(this.notifyStatus);
    }
}
