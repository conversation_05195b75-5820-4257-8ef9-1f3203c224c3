package com.ets.risk.application.app.job;
import com.ets.risk.application.app.disposer.RiskTaskDisposer;
import com.ets.risk.application.common.config.queue.task.RiskQueueTask;
import com.ets.risk.application.infra.entity.RiskTaskRecord;
import com.ets.risk.application.infra.service.RiskTaskRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class RiskTaskJob {
    @Autowired
    private RiskTaskRecordService riskTaskRecordService;
    @Autowired
    private RiskQueueTask riskQueueTask;
    /*
     *  轮询 未完成的任务管理
     */
    @XxlJob("reExecRiskHandler")
    public ReturnT<String> reExecRiskHandler(String params){
        //获取未完成的数据
        List<RiskTaskRecord> taskRecordList = riskTaskRecordService.getListByCreatedAt(7);
        if(taskRecordList != null){
            taskRecordList.forEach(v ->{
                //推送到队列
                riskQueueTask.push(new RiskTaskDisposer(v));
            });
        }
        return ReturnT.SUCCESS;
    }
}
