package com.ets.risk.application.common.consts.risk;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RiskStatusEnum {
    TASK_STATUS_WAIT(0, "待处理"),
    TASK_STATUS_PROCESS(1, "处理中"),
    TASK_STATUS_PASS(2, "通过"),
    TASK_STATUS_REJECT(3, "不通过"),
    ;

    private final Integer status;
    private final String description;

    public static String getDescByCode(int status) {
        for (RiskStatusEnum node : RiskStatusEnum.values()) {
            if (node.getStatus() == status) {
                return node.getDescription();
            }
        }
        return "";
    }
    public static RiskStatusEnum getNodeByCode(int status) {
        for (RiskStatusEnum node : RiskStatusEnum.values()) {
            if (node.getStatus() == status) {
                return node;
            }
        }
        return null;
    }
}
