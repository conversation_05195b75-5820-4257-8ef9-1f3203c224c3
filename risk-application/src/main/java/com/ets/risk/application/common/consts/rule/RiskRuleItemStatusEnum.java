package com.ets.risk.application.common.consts.rule;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RiskRuleItemStatusEnum {
    TASK_STATUS_WAIT(0, "待处理"),
    TASK_STATUS_PASS(1, "命中规则"),
    TASK_STATUS_REJECT(2, "不命中规则"),
    TASK_STATUS_CANCEL(3, "无效"),
    ;

    private final Integer status;
    private final String description;

    public static String getDescByCode(int status) {
        for (RiskRuleItemStatusEnum node : RiskRuleItemStatusEnum.values()) {
            if (node.getStatus() == status) {
                return node.getDescription();
            }
        }
        return "";
    }
    public static RiskRuleItemStatusEnum getNodeByCode(int status) {
        for (RiskRuleItemStatusEnum node : RiskRuleItemStatusEnum.values()) {
            if (node.getStatus() == status) {
                return node;
            }
        }
        return null;
    }
}
