package com.ets.risk.application.controller;

import com.ets.common.JsonResult;
import com.ets.risk.application.app.business.RiskBusiness;
import com.ets.risk.application.common.dto.risk.RiskAcceptDTO;
import com.ets.risk.application.common.dto.risk.RiskGetRuleByParamsDTO;
import com.ets.risk.application.common.vo.risk.RiskGetRuleByParamsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/risk")
public class RiskController {

    @Autowired
    private RiskBusiness riskBusiness;

    /*
     *  新商品订单使用， 新增发货单
     */
    @RequestMapping("/accept")
    public JsonResult<Boolean> accept(@RequestBody @Validated RiskAcceptDTO dto) {
        return JsonResult.ok(riskBusiness.accept(dto));
    }
    @RequestMapping("/getRuleByParams")
    public JsonResult<List<RiskGetRuleByParamsVo>> getRuleByParams(@RequestBody @Validated RiskGetRuleByParamsDTO dto) {
        return JsonResult.ok(riskBusiness.getRuleByParams(dto));
    }
}
