package com.ets.risk.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.risk.application.common.dto.risk.RiskAdminGetListDTO;
import com.ets.risk.application.common.vo.risk.RiskGetRuleByParamsVo;
import com.ets.risk.application.infra.entity.RiskEntity;
import com.ets.risk.application.infra.entity.RiskItemEntity;
import com.ets.risk.application.infra.entity.RiskTaskRecord;
import com.ets.risk.application.infra.mapper.RiskMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <p>
 * 风控记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@DS("db-issuer-admin")
public class RiskService extends BaseService<RiskMapper, RiskEntity> {
    /*
     *   更新risk的状态及原因
     */
    public void updateRiskStatus(String riskSn,Integer riskStatus,String riskRemark){
        LambdaUpdateWrapper<RiskEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(RiskEntity::getRiskSn, riskSn)
                           .set(RiskEntity::getRiskStatus, riskStatus)
                           .set(RiskEntity::getRiskRemark, riskRemark)
                           .set(RiskEntity::getUpdatedAt, LocalDateTime.now())
        ;
        updateByWrapper(lambdaUpdateWrapper);
    }
    /*
     *  通过业务单号+状态 获取risk的item数量
     */
    public Long getCountByBusinessSn(String businessSn,Integer riskStatus){
        Wrapper<RiskEntity> wrapper = Wrappers.<RiskEntity>lambdaQuery()
                .eq(RiskEntity::getBusinessSn, businessSn)
                .eq(RiskEntity::getRiskStatus, riskStatus);
        return super.baseMapper.selectCount(wrapper);
    }
    /*
     *   更新risk的通知状态
     */
    public void updateNotifyStatus(String riskSn,Integer notifyStatus){
        LambdaUpdateWrapper<RiskEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(RiskEntity::getRiskSn, riskSn)
                .set(RiskEntity::getNotifyStatus, notifyStatus)
                .set(RiskEntity::getUpdatedAt, LocalDateTime.now())
        ;
        updateByWrapper(lambdaUpdateWrapper);
    }

    /*
     *  admin获取列表
     */
    public IPage<RiskEntity> getList(RiskAdminGetListDTO dto) {
        // 构建查询条件
        LambdaQueryWrapper<RiskEntity> queryWrapper = Wrappers.<RiskEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(dto.getBusinessSn()), RiskEntity::getBusinessSn, dto.getBusinessSn())
                .orderByDesc(RiskEntity::getCreatedAt);

        if (ObjectUtils.isNotEmpty(dto.getStartTime())) {
            queryWrapper.ge(RiskEntity::getCreatedAt, dto.getStartTime().atStartOfDay());
        }
        if (ObjectUtils.isNotEmpty(dto.getEndTime())) {
            queryWrapper.le(RiskEntity::getCreatedAt, dto.getEndTime().atTime(LocalTime.MAX));
        }

        return this.page(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
    }


}
