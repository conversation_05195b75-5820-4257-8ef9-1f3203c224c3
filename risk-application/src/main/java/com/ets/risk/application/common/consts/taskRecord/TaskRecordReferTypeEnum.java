package com.ets.risk.application.common.consts.taskRecord;

import com.ets.risk.application.app.factory.task.impl.TaskBase;
import com.ets.risk.application.app.factory.task.impl.TaskRiskDeal;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Getter
public enum TaskRecordReferTypeEnum {

    TASK_RISK_DEAL(TaskRecordReferTypeConstant.RISK_DEAL, TaskRiskDeal.class, "风控处理"),
    ;

    private final String type;
    private final Class<? extends TaskBase> job;
    private final String desc;

    public static TaskRecordReferTypeEnum getByType(String type) {

        for (TaskRecordReferTypeEnum node : TaskRecordReferTypeEnum.values()) {
            if (node.getType().equals(type)) {
                return node;
            }
        }
        return null;
    }

    public static List<String> getTypeList() {

        List<String> list = new ArrayList<>();

        for (TaskRecordReferTypeEnum node : TaskRecordReferTypeEnum.values()) {
            if (node.getJob() != null) {
                list.add(node.getType());
            }
        }

        return list;
    }
}
