package com.ets.risk.application.infra.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 风控规则配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_risk_rule")
public class RiskRuleEntity extends BaseEntity<RiskRuleEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 规则类型：1总开关2满足条件3通用规则
     */
    private Integer ruleType;

    /**
     * 业务类型[1-申办]
     */
    private Integer businessType;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则信息查询方法
     */
    private String ruleFunc;

    /**
     * 规则参数名
     */
    private String ruleParams;

    /**
     * 规则条件
     */
    private String ruleCondition;
    /**
     * 忽略规则条件
     */
    private String ignoreCondition;
    /**
     * 规则状态[0-下架 1-正常]
     */
    private Byte ruleStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
