package com.ets.risk.application.app.factory.task.impl;

import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.risk.application.app.factory.rule.RuleFactory;
import com.ets.risk.application.app.thirdservice.feign.RiskNotifyFeign;
import com.ets.risk.application.app.thirdservice.request.RiskStatusNotifyDTO;
import com.ets.risk.application.common.bo.rule.RuleResultBo;
import com.ets.risk.application.common.consts.risk.RiskStatusEnum;
import com.ets.risk.application.common.consts.rule.RiskRuleItemStatusEnum;
import com.ets.risk.application.common.consts.rule.RiskRuleTypeEnum;
import com.ets.risk.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.risk.application.infra.entity.RiskEntity;
import com.ets.risk.application.infra.entity.RiskItemEntity;
import com.ets.risk.application.infra.entity.RiskRuleEntity;
import com.ets.risk.application.infra.entity.RiskTaskRecord;
import com.ets.risk.application.infra.relation.RiskItemBindRiskRuleRelation;
import com.ets.risk.application.infra.service.RiskItemService;
import com.ets.risk.application.infra.service.RiskRuleService;
import com.ets.risk.application.infra.service.RiskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class TaskRiskDeal extends TaskBase {
    @Autowired
    private RiskService riskService;
    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;
    @Autowired
    private RiskRuleService riskRuleService;
    @Autowired
    private RiskItemService riskItemService;
    @Autowired
    private RiskNotifyFeign riskNotifyFeign;

    /*
    * 风控处理
    *  类型1、总开关处理： 开/关
    *  类型2、进入的条件： 业务类型，发卡方
    *  类型3、item处理
    */
    @Override
    public void childExec(RiskTaskRecord riskTaskRecord) {
        //风控单的状态判断
        RiskEntity risk = riskService.getOneByColumn(riskTaskRecord.getReferSn(), RiskEntity::getRiskSn);
        if (risk == null) {
            ToolsHelper.throwException("风控单不存在："+riskTaskRecord.getReferSn(), TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }
        //待处理 类型1+类型2
        stepOne(risk);

        //处理中 item
        stepTwo(risk);
        //判断结果
        stepThree(risk.getRiskSn());
        //通知业务结果
        notify(risk.getRiskSn());

    }
    //待处理
    public void stepOne(RiskEntity risk){
        if(!Objects.equals(risk.getRiskStatus(), RiskStatusEnum.TASK_STATUS_WAIT.getStatus())){
            return;
        }
        //类型1 判断是否需要做风控
        List<RiskRuleEntity> riskRuleOneList = riskRuleService.getRiskRuleByRuleType(risk.getBusinessType(), 1);
        if(riskRuleOneList.size() > 0){
            for(RiskRuleEntity riskRule: riskRuleOneList){
                RiskRuleTypeEnum riskRuleTypeEnum = Objects.requireNonNull(RiskRuleTypeEnum.getByType(riskRule.getRuleFunc()));
                switch (riskRuleTypeEnum){
                    case RISK_RULE_ONE_MAIN_SWITCH:
                        RuleResultBo ruleResultBo = RuleFactory.create(riskRuleTypeEnum).getResult(risk,riskRule);
                        //log.info(ruleResultBo.toString());
                        //直接通过，结束任务
                        if(Objects.equals(ruleResultBo.getRiskStatus(), RiskStatusEnum.TASK_STATUS_PASS.getStatus())){
                            riskService.updateRiskStatus(risk.getRiskSn(), RiskStatusEnum.TASK_STATUS_PASS.getStatus(), "主开关直接通过");
                            return;
                        }
                        break;
                    default:
                        ToolsHelper.throwException("类型1校验方法不存在："+riskRule.getRuleFunc());
                        break;
                }
            }
        }

        //类型2 判断是否需要做item风控,全部通过则需要进入下一步，有一个不通过则风控通过，结束任务
        List<RiskRuleEntity> riskRuleTwoList = riskRuleService.getRiskRuleByRuleType(risk.getBusinessType(), 2);
        if(riskRuleTwoList.size() > 0){
            for(RiskRuleEntity riskRule: riskRuleTwoList){
                RiskRuleTypeEnum riskRuleTypeEnum = Objects.requireNonNull(RiskRuleTypeEnum.getByType(riskRule.getRuleFunc()));
                switch (riskRuleTypeEnum){
                    case RISK_RULE_SECOND_COMMON_MATCH_INT:
                    case RISK_RULE_SECOND_COMMON_NOT_MATCH_INT:
                        RuleResultBo ruleResultBo = RuleFactory.create(riskRuleTypeEnum).getResult(risk,riskRule);
                        //有一个不通过则风控通过，结束任务
                        if(Objects.equals(ruleResultBo.getItemStatus(), RiskRuleItemStatusEnum.TASK_STATUS_REJECT.getStatus())){
                            riskService.updateRiskStatus(risk.getRiskSn(), RiskStatusEnum.TASK_STATUS_PASS.getStatus(), "不满足类型2条件："+riskRule.getRuleName());
                            return;
                        }
                        break;
                    default:
                        ToolsHelper.throwException("类型2校验方法不存在："+riskRule.getRuleFunc());
                        break;
                }
            }
        }
        //生成item项
        createItems(risk);
        riskService.updateRiskStatus(risk.getRiskSn(), RiskStatusEnum.TASK_STATUS_PROCESS.getStatus(), "进入通用规则环节");
    }

    //进入通过规则，生成item项
    public void createItems(RiskEntity risk){
        //判断是否存在历史的item
        riskItemService.cancelByRiskSn(risk.getRiskSn(),"重试做废");
        //类型3
        List<RiskRuleEntity> riskRuleOneList = riskRuleService.getRiskRuleByRuleType(risk.getBusinessType(), 3);
        if(riskRuleOneList.size() > 0){
            riskRuleOneList.forEach(riskRule -> {
                RiskItemEntity riskItem = new RiskItemEntity();
                String itemSn = ToolsHelper.genNum(redisPermanentTemplate, "RiskItem", "prod", 8);
                riskItem.setRiskSn(risk.getRiskSn());
                riskItem.setRuleId(riskRule.getId());
                riskItem.setItemSn(itemSn);
                riskItem.setRuleParams(riskRule.getRuleParams());
                riskItem.setItemStatus(RiskRuleItemStatusEnum.TASK_STATUS_WAIT.getStatus());
                riskItemService.create(riskItem);
            });

        }

    }

    //通过规则，满足一个则风控不通过
    public void stepTwo(RiskEntity risk){
        //获取待处理的item
        List<RiskItemEntity> riskItemList = riskItemService.getListByRiskSn(risk.getRiskSn(),RiskRuleItemStatusEnum.TASK_STATUS_WAIT.getStatus());
        if (ObjectUtils.isNotEmpty(riskItemList)) {
            riskRuleService.bindToMasterEntityList(riskItemList, RiskItemBindRiskRuleRelation.class);
            riskItemList.forEach(riskItem -> {
                RiskRuleTypeEnum riskRuleTypeEnum = Objects.requireNonNull(RiskRuleTypeEnum.getByType(riskItem.getRiskRule().getRuleFunc()));
                switch (riskRuleTypeEnum) {
                    case RISK_RULE_CHECK_HIT_COUNTS, RISK_RULE_THIRD_APPLY_ORDER_NUMS -> {
                        RuleResultBo ruleResultBo = RuleFactory.create(riskRuleTypeEnum).getResult(risk, riskItem.getRiskRule());
                        riskItemService.updateRiskStatus(riskItem.getItemSn(), ruleResultBo.getItemStatus(), ruleResultBo.getRuleResult(), ruleResultBo.getRemark());
                    }
                    default ->
                            ToolsHelper.throwException("类型1校验方法不存在：" + riskItem.getRiskRule().getRuleFunc());
                }
            });
        }
    }

    //通过item的完成结果，判断风控的结果
    public void stepThree(String riskSn){
        RiskEntity risk = riskService.getOneByColumn(riskSn, RiskEntity::getRiskSn);
        if(risk == null){
            ToolsHelper.throwException("风控不存在");
        }
        if(!Objects.equals(risk.getRiskStatus(), RiskStatusEnum.TASK_STATUS_PROCESS.getStatus())){
          return ;
        }
        //是否存在
        Long riskItemWaitCount = riskItemService.getCountByRiskSn(risk.getRiskSn(),RiskRuleItemStatusEnum.TASK_STATUS_WAIT.getStatus());
        if(riskItemWaitCount > 0){
            ToolsHelper.throwException("风控item未全部处理");
        }
        Long riskItemPassCount = riskItemService.getCountByRiskSn(risk.getRiskSn(),RiskRuleItemStatusEnum.TASK_STATUS_PASS.getStatus());
        if(riskItemPassCount > 0){
            if(!Objects.equals(risk.getRiskStatus(), RiskStatusEnum.TASK_STATUS_REJECT.getStatus())){
                riskService.updateRiskStatus(risk.getRiskSn(), RiskStatusEnum.TASK_STATUS_REJECT.getStatus(), "风控命中规则不通过");
            }
        }else{
            if(!Objects.equals(risk.getRiskStatus(), RiskStatusEnum.TASK_STATUS_PASS.getStatus())){
                riskService.updateRiskStatus(risk.getRiskSn(), RiskStatusEnum.TASK_STATUS_PASS.getStatus(), "风控不命中规则通过");
            }
        }

    }

    /*
    *  风控有结果了，通知业务方
    */
    public void notify(String riskSn){
        RiskEntity risk = riskService.getOneByColumn(riskSn, RiskEntity::getRiskSn);
        if(risk == null){
            ToolsHelper.throwException("风控不存在");
        }
        if(risk.getNotifyUrl() == null){
            ToolsHelper.throwException("业务通知url为空");
        }
        if(Objects.equals(risk.getRiskStatus(), RiskStatusEnum.TASK_STATUS_WAIT.getStatus())){
            ToolsHelper.throwException("风控未有结果");
        }
        if(risk.getNotifyStatus() == 0){
            try {
                RiskStatusNotifyDTO riskStatusNotify = new RiskStatusNotifyDTO();
                riskStatusNotify.setRiskSn(risk.getRiskSn());
                riskStatusNotify.setBusinessSn(risk.getBusinessSn());
                riskStatusNotify.setBusinessType(risk.getBusinessType());
                riskStatusNotify.setRiskStatus(risk.getRiskStatus());
                riskStatusNotify.setRiskRemark(risk.getRiskRemark());
                //获取命中的item
                List<RiskStatusNotifyDTO.ruleItem> ruleItems = new ArrayList<>();
                List<RiskItemEntity> riskItemList = riskItemService.getListByRiskSn(risk.getRiskSn(),RiskRuleItemStatusEnum.TASK_STATUS_PASS.getStatus());
                if(ObjectUtils.isNotEmpty(riskItemList)){
                    riskRuleService.bindToMasterEntityList(riskItemList, RiskItemBindRiskRuleRelation.class);
                    riskItemList.forEach(riskItem -> {
                        RiskStatusNotifyDTO.ruleItem ruleItem = new RiskStatusNotifyDTO.ruleItem();
                        ruleItem.setRuleId(riskItem.getRuleId());
                        ruleItem.setRuleName(riskItem.getRiskRule().getRuleName());
                        ruleItem.setRuleResult(riskItem.getRuleResult());
                        ruleItems.add(ruleItem);
                    });
                }
                riskStatusNotify.setRuleItems(ruleItems);
                //通知业务方
                String jsonRet = riskNotifyFeign.riskStatusNotify(URI.create(risk.getNotifyUrl()), riskStatusNotify);
                if (StringUtils.isNotEmpty(jsonRet)) {
                    JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonRet, Object.class);
                    result.checkError();
                }
                // 通知成功
                riskService.updateNotifyStatus(risk.getRiskSn(), 1);
            } catch (Exception e) {
                // 通知失败
                ToolsHelper.throwException("通知业务方失败：" + e.getMessage());
            }
        }
    }
}
