package com.ets.risk.application.app.factory.task.impl;

import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.ets.risk.application.app.disposer.RiskTaskDisposer;
import com.ets.risk.application.app.factory.task.ITask;
import com.ets.risk.application.common.config.queue.task.RiskQueueTask;
import com.ets.risk.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.risk.application.common.consts.taskRecord.TaskRecordStatusEnum;
import com.ets.risk.application.common.dto.task.TaskRecordDTO;
import com.ets.risk.application.infra.entity.RiskTaskRecord;
import com.ets.risk.application.infra.service.RiskTaskRecordLogService;
import com.ets.risk.application.infra.service.RiskTaskRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;

@Slf4j
public abstract class TaskBase implements ITask {

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private RiskTaskRecordLogService riskTaskRecordLogService;

    @Autowired
    private RiskTaskRecordService riskTaskRecordService;
    @Qualifier("RiskQueueTask")
    @Autowired
    private RiskQueueTask queueTask;
    @Override
    public void addAndPush(TaskRecordDTO taskRecordDTO) {
        String taskSn = ToolsHelper.genNum(redisPermanentTemplate, "task_record", ACTIVE, 8);
        taskRecordDTO.setTaskSn(taskSn);
        taskRecordDTO.setStatus(TaskRecordStatusEnum.TASK_STATUS_WAIT.getCode());
        RiskTaskRecord riskTaskRecord = riskTaskRecordService.addNew(taskRecordDTO);
        queueTask.push(new RiskTaskDisposer(riskTaskRecord), taskRecordDTO.getDelayLevel());
    }

    @Override
    public void beforeExec(RiskTaskRecord riskTaskRecord, Boolean isDirect) {
        //任务是否已执行成功
        if (!isDirect && (riskTaskRecord.getStatus().equals(TaskRecordStatusEnum.TASK_STATUS_STOP.getCode()) ||
                riskTaskRecord.getNextExecTime() == null ||
                riskTaskRecord.getNextExecTime().isAfter(LocalDateTime.now())
        )) {
            String msg = "任务已暂停或者还没到执行时间";
            riskTaskRecordLogService.addLog(riskTaskRecord.getTaskSn(), msg);
            ToolsHelper.throwException(msg, TaskRecordErrorCodeConstant.ERROR_CODE_NO_NEED_DEAL);
        }
        //非待处理状态
        if (Arrays.asList(new Integer[]{
                TaskRecordStatusEnum.TASK_STATUS_PROCESS.getCode(),
                TaskRecordStatusEnum.TASK_STATUS_FINISH.getCode(),
                TaskRecordStatusEnum.TASK_STATUS_STOP.getCode(),
        }).contains(riskTaskRecord.getStatus())) {
            String msg = "任务为非待处理状态";
            riskTaskRecordLogService.addLog(riskTaskRecord.getTaskSn(), msg);
            ToolsHelper.throwException(msg, TaskRecordErrorCodeConstant.ERROR_CODE_NO_NEED_DEAL);
        }

        //执行超过20次的任务自动改成暂停状态，需要手动清零再处理
        if (riskTaskRecord.getExecTimes() > 20) {
            String msg = "执行超过20次的任务自动改成暂停状态，需要手动清零再处理";
            riskTaskRecordLogService.addLog(riskTaskRecord.getTaskSn(), msg);
            ToolsHelper.throwException(msg, TaskRecordErrorCodeConstant.ERROR_CODE_NEED_STOP);
        }
        //加锁
        if (!ToolsHelper.addLock(redisPermanentTemplate, "beforeExec:" + riskTaskRecord.getTaskSn(), 20)) {
            ToolsHelper.throwException("任务处理中，请稍后！", TaskRecordErrorCodeConstant.ERROR_CODE_NO_NEED_DEAL);
        }
        //次数加1
        riskTaskRecord.setStatus(TaskRecordStatusEnum.TASK_STATUS_PROCESS.getCode());
        riskTaskRecord.setExecTimes(riskTaskRecord.getExecTimes() + 1);
        riskTaskRecord.setUpdatedAt(LocalDateTime.now());
        riskTaskRecordService.saveOrUpdate(riskTaskRecord);
        riskTaskRecordLogService.addLog(riskTaskRecord.getTaskSn(), "开始执行任务");
    }

    @Override
    public void afterExec(RiskTaskRecord riskTaskRecord, Integer status, String msg) {
        if (msg.isEmpty()) {
            msg = "无报错";
        }
        //普通计算下次执行时间
        if (Arrays.asList(new Integer[]{
                        TaskRecordStatusEnum.TASK_STATUS_WAIT.getCode(),
                        TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode()
                }).contains(riskTaskRecord.getStatus())
        ) {
            if (riskTaskRecord.getExecTimes() < 5) {
                riskTaskRecord.setNextExecTime(LocalDateTime.now().plusMinutes(15));
            } else if (riskTaskRecord.getExecTimes() < 10) {
                riskTaskRecord.setNextExecTime(LocalDateTime.now().plusMinutes(30));
            } else if (riskTaskRecord.getExecTimes() < 15) {
                riskTaskRecord.setNextExecTime(LocalDateTime.now().plusMinutes(60));
            } else {
                riskTaskRecord.setNextExecTime(LocalDateTime.now().plusMinutes(90));
            }
        }
        riskTaskRecord.setStatus(status);
        riskTaskRecord.setExecError(msg.length() > 255 ? msg.substring(0, 255) : msg);
        riskTaskRecordService.saveOrUpdate(riskTaskRecord);
        riskTaskRecordLogService.addLog(riskTaskRecord.getTaskSn(), "结束执行任务：" + msg);
    }

    /*
     *  统一执行入口
     */
    public void execute(String taskSn,Boolean isDirect){
        RiskTaskRecord riskTaskRecord = riskTaskRecordService.getOneByTaskSn(taskSn);
        try{
            // 执行任务前校验
            beforeExec(riskTaskRecord,isDirect);
            // 执行任务
            childExec(riskTaskRecord);
            // 任务执行成功
            afterExec(riskTaskRecord, TaskRecordStatusEnum.TASK_STATUS_FINISH.getCode(), "任务执行处理无报错");
        } catch (BizException e) {
            switch (e.getErrorCode()) {
                case TaskRecordErrorCodeConstant.ERROR_CODE_NO_NEED_DEAL:
                    // 无需处理
                    break;
                case TaskRecordErrorCodeConstant.ERROR_CODE_NEED_STOP:
                    // 超过20次 暂停处理
                    afterExec(riskTaskRecord, TaskRecordStatusEnum.TASK_STATUS_STOP.getCode(), e.getMessage());
                    break;
                case TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH:
                    // 报错完结
                    afterExec(riskTaskRecord, TaskRecordStatusEnum.TASK_STATUS_FINISH.getCode(), e.getMessage());
                    break;
                default:
                    afterExec(riskTaskRecord, TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode(), "任务执行报错:" + e.getMessage());
                    break;
            }
        } catch (Throwable e) {
            log.error("任务{}执行报错：{}", taskSn, e.getStackTrace());
            afterExec(riskTaskRecord, TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode(), "任务执行报错:" + e.getLocalizedMessage());
        }
    }
}
