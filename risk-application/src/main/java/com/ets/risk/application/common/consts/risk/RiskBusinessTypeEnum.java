package com.ets.risk.application.common.consts.risk;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RiskBusinessTypeEnum {
    BUSINESS_TYPE_APPLY(1, "申办"),
    ;

    private final Integer type;
    private final String description;

    public static String getDescByCode(int type) {
        for (RiskBusinessTypeEnum node : RiskBusinessTypeEnum.values()) {
            if (node.getType() == type) {
                return node.getDescription();
            }
        }
        return "";
    }
}
