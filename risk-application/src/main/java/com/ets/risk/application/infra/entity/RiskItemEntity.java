package com.ets.risk.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 风控规则匹配明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_risk_item")
public class RiskItemEntity extends BaseEntity<RiskItemEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 风控规则匹配明细编号
     */
    @TableId("item_sn")
    private String itemSn;

    /**
     * 风控流水号
     */
    private String riskSn;

    /**
     * 规则id
     */
    private Integer ruleId;

    /**
     * 请求参数
     */
    private String ruleParams;

    /**
     * 返回结果
     */
    private String ruleResult;

    /**
     * 明细状态[0-待处理 1-命中规则 2-不命中规则]
     */
    private Integer  itemStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 更新时间
     */
    @TableField(exist = false)
    private RiskRuleEntity riskRule;
    /**
     * 备注
     */
    private String remark;

}
