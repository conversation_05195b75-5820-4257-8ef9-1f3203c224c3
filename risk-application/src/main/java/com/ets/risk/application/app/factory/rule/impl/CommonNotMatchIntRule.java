package com.ets.risk.application.app.factory.rule.impl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ets.risk.application.common.bo.rule.RuleResultBo;
import com.ets.risk.application.common.consts.rule.RiskRuleItemStatusEnum;
import com.ets.risk.application.infra.entity.RiskEntity;
import com.ets.risk.application.infra.entity.RiskRuleEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public  class CommonNotMatchIntRule extends RuleBase {
    @Override
    public RuleResultBo getResult(RiskEntity risk, RiskRuleEntity riskRule) {
        RuleResultBo ruleResultBo = new RuleResultBo();
        JSONObject riskRuleCondition = JSON.parseObject(riskRule.getRuleCondition());
        JSONArray switchCondition = riskRuleCondition.getJSONArray("value");
        JSONObject ruleParamsObject = JSON.parseObject(risk.getRiskParams());
        Integer matchValue = ruleParamsObject.getInteger(riskRule.getRuleParams());
        //参数是否满足
        if (switchCondition.contains(matchValue)) {
            ruleResultBo.setItemStatus(RiskRuleItemStatusEnum.TASK_STATUS_REJECT.getStatus());
        }else{
            ruleResultBo.setItemStatus(RiskRuleItemStatusEnum.TASK_STATUS_PASS.getStatus());
        }
        return ruleResultBo;

    }

}
