package com.ets.risk.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.net.URI;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 风控记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_risk")
public class RiskEntity extends BaseEntity<RiskEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 风控流水号
     */
    @TableId("risk_sn")
    private String riskSn;

    /**
     * 业务单号
     */
    private String businessSn;

    /**
     * 业务类型[1-申办]
     */
    private Integer businessType;

    /**
     * 风控资料参数
     */
    private String riskParams;

    /**
     * 风控匹配规则id组合 用，隔开
     */
    private String riskRuleIds;

    /**
     * 风控结果[0-待处理 1-通过 2-不通过]
     */
    private Integer riskStatus;

    /**
     * 风控备注
     */
    private String riskRemark;

    /**
     * 结果通知地址
     */
    private String notifyUrl;

    //是否已通知业务方：0未通知1已通知
    private Integer notifyStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
