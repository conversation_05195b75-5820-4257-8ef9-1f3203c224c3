package com.ets.risk.application.app.thirdservice.feign;
import com.ets.risk.application.app.thirdservice.request.RiskStatusNotifyDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import java.net.URI;

/**
 * 通知类
 */
@FeignClient(name = "RiskNotifyFeign", url = "uri")
public interface RiskNotifyFeign {

    @PostMapping
    String riskStatusNotify(URI uri, @RequestBody RiskStatusNotifyDTO dto);

}
