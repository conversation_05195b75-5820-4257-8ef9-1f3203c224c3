package com.ets.risk.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.risk.application.app.thirdservice.feign.RiskApplyFeign;
import com.ets.risk.application.app.thirdservice.request.GetOrderRiskCheckDTO;
import com.ets.risk.application.app.thirdservice.response.GetOrderRiskCheckVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class RiskApplyFallbackFactory implements FallbackFactory<RiskApplyFeign> {
    @Override
    public RiskApplyFeign create(Throwable throwable) {
        return new RiskApplyFeign() {
            @Override
            public JsonResult<GetOrderRiskCheckVO> getOrderRiskCheckInfo(GetOrderRiskCheckDTO dto) {
                return JsonResult.error("apply服务调用失败" + throwable.getMessage());
            }
        };
    }
}
