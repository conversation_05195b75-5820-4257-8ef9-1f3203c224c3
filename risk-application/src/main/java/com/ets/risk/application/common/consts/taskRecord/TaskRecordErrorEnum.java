package com.ets.risk.application.common.consts.taskRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum TaskRecordErrorEnum {
    TASK_ERROR_CODE_NO_NEED_DEAL(TaskRecordErrorCodeConstant.ERROR_CODE_NO_NEED_DEAL, "无需处理"),
    TASK_ERROR_CODE_NEED_STOP(TaskRecordErrorCodeConstant.ERROR_CODE_NEED_STOP, "执行超过20次，暂停处理");

    private final Integer code;
    private final String description;
    public static final Map<Integer, String> map;

    static {
        TaskRecordErrorEnum[] enums = TaskRecordErrorEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getCode(), enums[index].getDescription()),
                Map::putAll);
    }

    public static TaskRecordErrorEnum getByCode(int code) {
        for (TaskRecordErrorEnum node : TaskRecordErrorEnum.values()) {
            if (node.getCode() == code) {
                return node;
            }
        }
        return null;
    }
}
