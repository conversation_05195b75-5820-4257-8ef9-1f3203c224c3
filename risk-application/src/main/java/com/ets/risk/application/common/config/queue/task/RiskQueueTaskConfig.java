package com.ets.risk.application.common.config.queue.task;

import com.ets.common.queue.QueueBaseConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Repository;

@EqualsAndHashCode(callSuper = true)
@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "ets-config.rocketmq.risk")
@Repository(value = "RiskQueueTaskConfig")
public class RiskQueueTaskConfig extends QueueBaseConfig {
    public final static  String PRODUCER_BEAN_NAME = "RiskTaskProducer";
    private String nameServerAddr;
    private String groupName;
    private String topic;
    private String queueName;
    private Integer retryTimes;
    // 消费者线程数据量
    private Integer consumeThreadMin;
    private Integer consumeThreadMax;

    @Bean(name = PRODUCER_BEAN_NAME)
    public DefaultMQProducer taskProducer() throws MQClientException {
        return super.createProducer(groupName, nameServerAddr, retryTimes);
    }

    @Bean(name = "defaultRiskTaskConsumer")
    public DefaultMQPushConsumer defaultTaskConsumer() throws MQClientException {
        return super.consumerStart(new RiskTaskConsumer(),
            groupName,
            nameServerAddr,
            topic,
            queueName,
            consumeThreadMin,
            consumeThreadMax,
            false);
    }
}
