package com.ets.risk.application.app.factory.rule.impl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.risk.application.common.bo.rule.RuleResultBo;
import com.ets.risk.application.common.consts.risk.RiskStatusEnum;
import com.ets.risk.application.infra.entity.RiskEntity;
import com.ets.risk.application.infra.entity.RiskRuleEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public  class MainSwitchRule extends RuleBase {
    @Override
    public RuleResultBo getResult(RiskEntity risk, RiskRuleEntity riskRule) {
        RuleResultBo ruleResultBo = new RuleResultBo();
        //判断开关是打开还是关闭
        JSONObject riskRuleCondition = JSON.parseObject(riskRule.getRuleCondition());
        Integer switchCondition = riskRuleCondition.getInteger("switch");
        //开关关闭，直接通过
        if (switchCondition == 0) {
            ruleResultBo.setRiskStatus(RiskStatusEnum.TASK_STATUS_PASS.getStatus());
        }
        return new RuleResultBo();
    }

}
