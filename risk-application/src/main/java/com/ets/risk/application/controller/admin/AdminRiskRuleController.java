package com.ets.risk.application.controller.admin;
import com.ets.common.JsonResult;
import com.ets.risk.application.app.business.RiskRuleBusiness;
import com.ets.risk.application.common.dto.risk.RiskRuleUpOrDownDTO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 售后审核管理
 */
@RestController
@RequestMapping("/admin/riskRule")
public class AdminRiskRuleController {

    @Autowired
    private RiskRuleBusiness riskRuleBusiness;

    /**
     * 获取售后审核列表
     * @param dto 查询参数
     * @return 分页列表数据
     */
    @PostMapping("/upOrDown")
    public JsonResult<Boolean> upOrDown(@RequestBody @Valid RiskRuleUpOrDownDTO dto) {
        return JsonResult.ok(riskRuleBusiness.upOrDown(dto));
    }


}
