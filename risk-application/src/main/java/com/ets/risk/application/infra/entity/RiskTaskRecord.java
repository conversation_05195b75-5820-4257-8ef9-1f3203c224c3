package com.ets.risk.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 任务记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskTaskRecord extends BaseEntity<RiskTaskRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 用于后续数据同步，业务中禁止使用
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务流水号
     */
    private String taskSn;

    /**
     * 任务关联sn,对应order_sn之类
     */
    private String referSn;

    /**
     * 任务类型
     */
    private String referType;

    /**
     * 处理内容
     */
    private String notifyContent;

    /**
     * 任务状态：0待处理，1处理中，2处理完成3处理失败4暂停处理
     */
    private Integer status;

    /**
     * 是否处理中[0-非处理中 1-处理中]
     */
    private Integer isProcessing;

    /**
     * 执行次数
     */
    private Integer execTimes = 0;

    /**
     * 最后一次执行的错误结果
     */
    private String execError;

    /**
     * 下一次执行时间
     */
    private LocalDateTime nextExecTime;

    /**
     * 执行队列
     */
    private String queueName;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;


}
