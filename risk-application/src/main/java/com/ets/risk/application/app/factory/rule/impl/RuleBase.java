package com.ets.risk.application.app.factory.rule.impl;
import com.ets.risk.application.app.factory.rule.IRule;
import com.ets.risk.application.common.bo.rule.RuleResultBo;
import com.ets.risk.application.infra.entity.RiskEntity;
import com.ets.risk.application.infra.entity.RiskRuleEntity;
import lombok.extern.slf4j.Slf4j;
@Slf4j
public abstract class RuleBase implements IRule {
    @Override
    public RuleResultBo getResult(RiskEntity risk, RiskRuleEntity riskRule) {
        return new RuleResultBo();
    }

}
