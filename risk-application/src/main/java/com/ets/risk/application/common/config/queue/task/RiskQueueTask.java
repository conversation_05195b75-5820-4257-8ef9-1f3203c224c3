package com.ets.risk.application.common.config.queue.task;

import com.ets.common.BizException;
import com.ets.common.queue.BaseDisposer;
import com.ets.common.queue.JobDto;
import com.ets.common.queue.MicroBaseQueue;
import com.ets.starter.interceptor.ApplicationContextHelper;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

@Repository(value = "RiskQueueTask")
@Component
public class RiskQueueTask extends MicroBaseQueue {
    @Autowired
    RiskQueueTaskConfig riskQueueTaskConfig;

    @Override
    public void push(BaseDisposer job) throws BizException {

        DefaultMQProducer defaultMQProducer = (DefaultMQProducer) ApplicationContextHelper.getBean(RiskQueueTaskConfig.PRODUCER_BEAN_NAME);
        if (defaultMQProducer == null) {
            return;
        }

        JobDto jobDto = getJobDto(job);
        super.pushRocketMqJob(defaultMQProducer, riskQueueTaskConfig.getTopic(), riskQueueTaskConfig.getQueueName(), jobDto);
    }

    public void push(BaseDisposer job, int delayLevel) {
        DefaultMQProducer defaultMQProducer = (DefaultMQProducer)ApplicationContextHelper.getBean(RiskQueueTaskConfig.PRODUCER_BEAN_NAME);
        if (defaultMQProducer != null) {
            JobDto jobDto = this.getJobDto(job);
            super.pushRocketMqJob(defaultMQProducer, riskQueueTaskConfig.getTopic(), riskQueueTaskConfig.getQueueName(), jobDto, delayLevel);
        }
    }
}
