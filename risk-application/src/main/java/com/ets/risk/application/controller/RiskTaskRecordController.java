package com.ets.risk.application.controller;

import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.risk.application.app.factory.task.TaskFactory;
import com.ets.risk.application.infra.entity.RiskTaskRecord;
import com.ets.risk.application.infra.service.RiskTaskRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/riskTask")
public class RiskTaskRecordController {
    @Autowired
    private RiskTaskRecordService riskTaskRecordService;


    /*
        直接执行task
     */
    @RequestMapping("/exec")
    public JsonResult<Boolean> exec(@RequestParam(value = "taskSn") String taskSn) throws BizException {
        RiskTaskRecord task = riskTaskRecordService.getOneByTaskSn(taskSn);
        // 执行任务
        TaskFactory.create(task.getReferType()).execute(task.getTaskSn(),true);

        return JsonResult.ok();
    }


}
