package com.ets.risk.application.app.thirdservice.request;

import lombok.Data;

import java.util.List;

@Data
public class RiskStatusNotifyDTO {

    private String riskSn;
    private String businessSn;
    private Integer businessType;
    private Integer riskStatus;
    private String riskRemark;
    private List< ruleItem> ruleItems;
    @Data
    public static class ruleItem {
        private Integer ruleId;
        private String ruleName;
        private String ruleResult;
    }
}
