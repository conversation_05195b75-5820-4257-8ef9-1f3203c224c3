package com.ets.risk.application.app.business;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.ToolsHelper;
import com.ets.risk.application.app.factory.task.TaskFactory;
import com.ets.risk.application.common.consts.rule.RiskRuleItemStatusEnum;
import com.ets.risk.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.risk.application.common.dto.risk.RiskAcceptDTO;
import com.ets.risk.application.common.dto.risk.RiskAdminGetListDTO;
import com.ets.risk.application.common.dto.risk.RiskGetRuleByParamsDTO;
import com.ets.risk.application.common.dto.task.TaskRecordDTO;
import com.ets.risk.application.common.vo.risk.RiskAdminGetListVo;
import com.ets.risk.application.common.vo.risk.RiskGetRuleByParamsVo;
import com.ets.risk.application.infra.entity.RiskEntity;
import com.ets.risk.application.infra.entity.RiskRuleEntity;
import com.ets.risk.application.infra.service.RiskRuleService;
import com.ets.risk.application.infra.service.RiskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.util.List;

@Slf4j
@Component
public class RiskBusiness {
    @Autowired
    private RiskService riskService;
    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private RiskRuleService riskRuleService;
    /*
     *  接收要风控数据
     */
    public Boolean accept(RiskAcceptDTO dto) {
        //获取风控规则
        List<Integer> ruleIds = riskRuleService.getRiskRuleIds(dto.getBusinessType());
        RiskEntity risk = new RiskEntity();
        String riskSn = ToolsHelper.genNum(redisPermanentTemplate, "RiskAccept", "prod", 8);
        risk.setRiskSn(riskSn);
        risk.setBusinessSn(dto.getBusinessSn());
        risk.setBusinessType(dto.getBusinessType());
        risk.setRiskParams(dto.getRiskParams());
        risk.setNotifyUrl(dto.getNotifyUrl());
        risk.setRiskRuleIds(ruleIds.toString());
        riskService.create(risk);

        //塞队列进行发货操作
        TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
        taskRecordDTO.setReferSn(riskSn);
        taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_RISK_DEAL.getType());
        TaskFactory.create(TaskRecordReferTypeEnum.TASK_RISK_DEAL).addAndPush(taskRecordDTO);
        return true;
    }

    /*
     *  接收要风控数据
     */
    public List<RiskGetRuleByParamsVo> getRuleByParams(RiskGetRuleByParamsDTO dto) {
        //获取风控规则
        List<RiskRuleEntity> list = riskRuleService.getAllByRuleType(dto.getBusinessType(),dto.getRuleType());
        if(list.isEmpty()){
            return null;
        }
        return  list.stream().map(record -> {
            RiskGetRuleByParamsVo vo = new RiskGetRuleByParamsVo();
            vo.setRuleId(record.getId());
            vo.setRuleName(record.getRuleName());
            return vo;
        }).toList();

    }

    /*
     *  admin获取列表
     */
    public IPage<RiskAdminGetListVo> getList(RiskAdminGetListDTO dto) {
        // 分页查询
        IPage<RiskEntity> reviewsPage = riskService.getList(dto);

        // 转换为VO对象
        return reviewsPage.convert(reviews -> {
            RiskAdminGetListVo vo = new RiskAdminGetListVo();
            BeanUtil.copyProperties(reviews, vo);
            return vo;
        });
    }

    /*
     *  检查是否满足忽略条件
     */
    public Boolean checkIgnoreCondition(JSONObject riskRuleIgnoreCondition,String matchValue) {
        JSONArray ignoreCondition = riskRuleIgnoreCondition.getJSONArray("value");
        String exp = riskRuleIgnoreCondition.getString("exp");
        if(ignoreCondition == null || ignoreCondition.isEmpty()){
            return false;
        }
        switch (exp) {
            case "in" -> {
                if (ignoreCondition.contains(matchValue)) {
                    return true;
                }
            }
            case "like" -> {
                for (int i = 0; i < ignoreCondition.size(); i++) {
                    if (matchValue.contains(ignoreCondition.getString(i))) {
                        return true;
                    }
                }
            }
            default -> {
            }
        }

        return false;
    }
}
