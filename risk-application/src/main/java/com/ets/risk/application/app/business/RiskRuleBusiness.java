package com.ets.risk.application.app.business;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.risk.application.common.dto.risk.RiskRuleUpOrDownDTO;
import com.ets.risk.application.infra.entity.RiskRuleEntity;
import com.ets.risk.application.infra.service.RiskRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RiskRuleBusiness {
    @Autowired
    private RiskRuleService riskRuleService;

    /*
     *  上下架
     */
    public Boolean upOrDown(RiskRuleUpOrDownDTO dto) {
        riskRuleService.updateByWrapper(new LambdaUpdateWrapper<RiskRuleEntity>()
                .eq(RiskRuleEntity::getId, dto.getId())
                .set(RiskRuleEntity::getRuleStatus, dto.getRuleStatus()));
        return true;
    }


}
