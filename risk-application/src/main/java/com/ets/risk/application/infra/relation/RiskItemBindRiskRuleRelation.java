package com.ets.risk.application.infra.relation;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.ets.common.base.BaseEntityRelation;
import com.ets.risk.application.infra.entity.RiskItemEntity;
import com.ets.risk.application.infra.entity.RiskRuleEntity;

import java.util.function.BiConsumer;

public class RiskItemBindRiskRuleRelation extends BaseEntityRelation<RiskItemEntity, RiskRuleEntity> {
    @Override
    public BiConsumer<RiskItemEntity, RiskRuleEntity> getEntityColumn() {

        return RiskItemEntity::setRiskRule;
    }

    @Override
    public SFunction<RiskItemEntity, Object> getMasterColumn() {
        return RiskItemEntity::getRuleId;
    }

    @Override
    public SFunction<RiskRuleEntity, Object> getAffiliatedColumn() {
        return RiskRuleEntity::getId;
    }

}
