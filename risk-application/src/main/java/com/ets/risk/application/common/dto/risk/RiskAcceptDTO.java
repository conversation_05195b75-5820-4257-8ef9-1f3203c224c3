package com.ets.risk.application.common.dto.risk;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.net.URI;

@Data
public class RiskAcceptDTO {
    /**
     * 业务单号
     */
    @NotNull(message = "businessSn不可为空")
    private String businessSn;

    /**
     * 业务类型[1-申办]
     */
    @NotNull(message = "businessType不可为空")
    private Integer businessType;

    /**
     * 风控资料参数
     */
    @NotNull(message = "riskParams不可为空")
    private String riskParams;

    /**
     * 结果通知地址
     */
    @NotNull(message = "notifyUrl不可为空")
    private String notifyUrl;

}
