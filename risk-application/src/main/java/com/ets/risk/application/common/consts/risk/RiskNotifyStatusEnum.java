package com.ets.risk.application.common.consts.risk;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RiskNotifyStatusEnum {
    TASK_STATUS_WAIT(0, "待通知"),
    TASK_STATUS_FINISHED(1, "已通知"),
    ;

    private final Integer status;
    private final String description;

    public static String getDescByCode(int status) {
        for (RiskNotifyStatusEnum node : RiskNotifyStatusEnum.values()) {
            if (node.getStatus() == status) {
                return node.getDescription();
            }
        }
        return "";
    }

}
