package com.ets.risk.application.infra.service;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.risk.application.infra.entity.RiskRuleEntity;
import com.ets.risk.application.infra.mapper.RiskRuleMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 风控规则配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@DS("db-issuer-admin")
public class RiskRuleService extends BaseService<RiskRuleMapper, RiskRuleEntity> {
    public List<Integer> getRiskRuleIds(Integer businessType){
        Wrapper<RiskRuleEntity> wrapper = Wrappers.<RiskRuleEntity>lambdaQuery()
                .select(RiskRuleEntity::getId)
                .eq(RiskRuleEntity::getBusinessType, businessType)
                .eq(RiskRuleEntity::getRuleStatus, 1);
        return this.baseMapper.selectObjs(wrapper);
    }

    public List<RiskRuleEntity> getRiskRuleByRuleType(Integer businessType,Integer ruleType){
        Wrapper<RiskRuleEntity> wrapper = Wrappers.<RiskRuleEntity>lambdaQuery()
                .eq(RiskRuleEntity::getBusinessType, businessType)
                .eq(RiskRuleEntity::getRuleType, ruleType)
                .eq(RiskRuleEntity::getRuleStatus, 1);
        return this.baseMapper.selectList(wrapper);
    }

    /*
     * 返回全部Rule
     */
    public List<RiskRuleEntity> getAllByRuleType(Integer businessType,Integer ruleType){
        Wrapper<RiskRuleEntity> wrapper = Wrappers.<RiskRuleEntity>lambdaQuery()
                .eq(RiskRuleEntity::getBusinessType, businessType)
                .eq(RiskRuleEntity::getRuleType, ruleType);
        return this.baseMapper.selectList(wrapper);
    }
}
