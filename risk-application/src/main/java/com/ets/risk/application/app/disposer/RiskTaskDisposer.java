package com.ets.risk.application.app.disposer;

import com.ets.common.queue.BaseDisposer;
import com.ets.risk.application.app.factory.task.TaskFactory;
import com.ets.risk.application.infra.entity.RiskTaskRecord;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

@NoArgsConstructor
@Component(value = "RiskTaskJobBean")
public class RiskTaskDisposer extends BaseDisposer {
    public RiskTaskDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "RiskTaskJobBean";
    }

    @Override
    public void execute(Object content) {
        RiskTaskRecord riskTaskRecord = super.getParamsObject(content, RiskTaskRecord.class);
        // 执行任务
        TaskFactory.create(riskTaskRecord.getReferType()).execute(riskTaskRecord.getTaskSn(),false);
    }
}
