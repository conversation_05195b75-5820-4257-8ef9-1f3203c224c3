package com.ets.risk.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.risk.application.app.business.RiskBusiness;
import com.ets.risk.application.common.dto.risk.RiskAdminGetListDTO;
import com.ets.risk.application.common.vo.risk.RiskAdminGetListVo;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 售后审核管理
 */
@RestController
@RequestMapping("/admin/risk")
public class AdminRiskController {

    @Autowired
    private RiskBusiness riskBusiness;

    /**
     * 获取售后审核列表
     * @param dto 查询参数
     * @return 分页列表数据
     */
    @PostMapping("/getList")
    public JsonResult<IPage<RiskAdminGetListVo>> getList(@RequestBody @Valid RiskAdminGetListDTO dto) {
        return JsonResult.ok(riskBusiness.getList(dto));
    }


}
