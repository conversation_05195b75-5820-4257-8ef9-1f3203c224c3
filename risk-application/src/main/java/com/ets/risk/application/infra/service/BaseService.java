package com.ets.risk.application.infra.service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ets.common.base.MicroBaseService;
import com.ets.risk.application.infra.entity.BaseEntity;
import com.ets.risk.application.infra.mapper.CommonBaseMapper;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BaseService<M extends CommonBaseMapper<T>, T extends BaseEntity<T>> extends MicroBaseService<BaseMapper<T>, T> {


}
