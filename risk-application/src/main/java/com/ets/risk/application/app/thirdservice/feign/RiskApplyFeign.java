package com.ets.risk.application.app.thirdservice.feign;

import com.ets.common.JsonResult;

import com.ets.risk.application.app.thirdservice.fallback.RiskApplyFallbackFactory;
import com.ets.risk.application.app.thirdservice.request.GetOrderRiskCheckDTO;
import com.ets.risk.application.app.thirdservice.response.GetOrderRiskCheckVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        url = "${microUrls.apply:http://apply-application:20070}",
        name = "RiskApplyFeign",
        contextId = "RiskApplyFeign",
        fallbackFactory = RiskApplyFallbackFactory.class
)
public interface RiskApplyFeign {

    @PostMapping("/order/getOrderRiskCheckInfo")
    JsonResult<GetOrderRiskCheckVO> getOrderRiskCheckInfo(@RequestBody GetOrderRiskCheckDTO dto);

}
