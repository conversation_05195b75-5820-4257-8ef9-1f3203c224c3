package com.ets.risk.application.infra.service;

import com.ets.risk.application.infra.entity.RiskTaskRecordLog;
import com.ets.risk.application.infra.mapper.RiskTaskRecordLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 任务执行日志表 业务处理类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Service
@Slf4j
@Repository(value = "RiskTaskRecordLogService")
public class RiskTaskRecordLogService extends BaseService<RiskTaskRecordLogMapper, RiskTaskRecordLog> {
    /*
     * 增加日志
     */
    public RiskTaskRecordLog addLog(String taskSn, String content) {
        RiskTaskRecordLog riskTaskRecordLog = new RiskTaskRecordLog();
        riskTaskRecordLog.setTaskSn(taskSn);
        riskTaskRecordLog.setExecContent(content);
        riskTaskRecordLog.setCreatedAt(LocalDateTime.now());
        riskTaskRecordLog.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(riskTaskRecordLog);
        return riskTaskRecordLog;
    }
}
