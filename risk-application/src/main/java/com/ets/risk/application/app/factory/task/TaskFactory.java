package com.ets.risk.application.app.factory.task;

import cn.hutool.extra.spring.SpringUtil;
import com.ets.common.ToolsHelper;
import com.ets.risk.application.app.factory.task.impl.TaskBase;
import com.ets.risk.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.risk.application.common.dto.task.TaskRecordDTO;

import java.time.LocalDateTime;

public class TaskFactory {

    public static TaskBase create(String referType) {

        TaskRecordReferTypeEnum typeEnum = TaskRecordReferTypeEnum.getByType(referType);

        if (typeEnum == null) {
            ToolsHelper.throwException("暂不支持此任务999");
        }

        return create(typeEnum);
    }

    public static TaskBase create(TaskRecordReferTypeEnum referTypeEnum) {

        return SpringUtil.getBean(referTypeEnum.getJob());
    }

    public static void createAndPush(TaskRecordReferTypeEnum referTypeEnum, String referSn) {

        TaskBase task = create(referTypeEnum);
        TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
        taskRecordDTO.setReferSn(referSn);
        taskRecordDTO.setReferType(referTypeEnum.getType());
        taskRecordDTO.setNextExecTime(LocalDateTime.now());

        task.addAndPush(taskRecordDTO);
    }

}
