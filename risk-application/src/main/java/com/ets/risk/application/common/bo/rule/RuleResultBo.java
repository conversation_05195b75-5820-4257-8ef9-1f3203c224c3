package com.ets.risk.application.common.bo.rule;

import lombok.Data;

@Data
public class RuleResultBo {
    /**
     *  规则结果
     */
    private String ruleResult;

    /**
     *   item结果的状态：明细状态[0-待处理 1-命中规则 2-不命中规则]
     */
    private Integer itemStatus = 0;

    /**
     *   风控结果状态：0-待处理 1-处理中 2-通过 3-不通过
     */
    private Integer riskStatus = 0;

    /**
     *  备注
     */
    private String remark ="";
}
