package com.ets.risk.application.common.config.queue.task;

import com.ets.starter.base.BaseConsumer;
import com.ets.starter.interceptor.ApplicationContextHelper;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository(value = "RiskTaskConsumer")
@Component
public class RiskTaskConsumer extends BaseConsumer implements MessageListenerConcurrently {
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {

        DefaultMQProducer defaultMQProducer = (DefaultMQProducer) ApplicationContextHelper.getBean(RiskQueueTaskConfig.PRODUCER_BEAN_NAME);
        if (defaultMQProducer == null) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        return super.handleRocketMq(defaultMQProducer, list, consumeConcurrentlyContext);
    }
}
