package com.ets.risk.application.app.factory.task;


import com.ets.risk.application.common.dto.task.TaskRecordDTO;
import com.ets.risk.application.infra.entity.RiskTaskRecord;
import org.springframework.stereotype.Component;

@Component(value = "RiskITask")
public interface ITask {
    void addAndPush(TaskRecordDTO taskRecordDTO);
    void beforeExec(RiskTaskRecord riskTaskRecord, Boolean isDirect);
    void execute(String taskSn,Boolean isDirect);
    //子类实际执行
    void childExec(RiskTaskRecord riskTaskRecord);
    void afterExec(RiskTaskRecord riskTaskRecord, Integer status, String msg);
}
