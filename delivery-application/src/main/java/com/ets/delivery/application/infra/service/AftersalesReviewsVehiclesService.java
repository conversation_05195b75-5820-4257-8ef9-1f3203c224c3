package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.delivery.application.infra.mapper.AftersalesReviewsVehiclesMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@DS("db-issuer-admin-proxy")
public class AftersalesReviewsVehiclesService extends BaseService<AftersalesReviewsVehiclesMapper, AftersalesReviewsVehicles> {

    public List<AftersalesReviewsVehicles> getByReviewSn(String reviewSn) {
        Wrapper<AftersalesReviewsVehicles> wrapper = Wrappers.<AftersalesReviewsVehicles>lambdaQuery()
                .eq(AftersalesReviewsVehicles::getReviewSn, reviewSn);
        return this.baseMapper.selectList(wrapper);
    }
}
