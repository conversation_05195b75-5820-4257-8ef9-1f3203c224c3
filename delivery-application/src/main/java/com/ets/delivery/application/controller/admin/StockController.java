package com.ets.delivery.application.controller.admin;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.stock.AdminStockBusiness;
import com.ets.delivery.application.common.vo.SelectOptionsVO;
import com.ets.delivery.application.common.vo.stock.SearchSupplyGoodsVO;
import com.ets.delivery.application.common.vo.stock.StockUploadVO;
import com.ets.delivery.application.infra.entity.SupplyGoods;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/stock")
public class StockController {

    @Autowired
    private AdminStockBusiness adminStockBusiness;

    @PostMapping("/getSelectOptions")
    @ResponseBody
    public JsonResult<Map<String, List<SelectOptionsVO>>> getSelectOptions() {

        return JsonResult.ok(adminStockBusiness.getSelectOptions());
    }

    @PostMapping("/upload")
    @ResponseBody
    public JsonResult<StockUploadVO> upload(MultipartFile file) {

        return JsonResult.ok(adminStockBusiness.uploadImg(file));
    }

    @PostMapping("/searchSupplyGoods")
    @ResponseBody
    public JsonResult<List<SearchSupplyGoodsVO>> searchSupplyGoods(
            @RequestParam(value = "goodsName", required = false) String goodsName,
            @RequestParam(value = "storageCode") String storageCode
    ) {

        return JsonResult.ok(adminStockBusiness.getSupplyGoods(goodsName, storageCode));
    }

}
