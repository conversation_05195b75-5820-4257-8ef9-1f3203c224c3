package com.ets.delivery.application.common.dto.manage;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

@Data
public class ManageStorageSkuMapDTO {
    /**
     * 商品代码
     */
    private String sku;

    /**
     * 仓库代号
     */
    private String storageCode;

    /**
     * 仓库对应sku的编码
     */
    private String storageSku;


    /**
     * 状态：1正常2关闭
     */
    private Integer status = 0;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
