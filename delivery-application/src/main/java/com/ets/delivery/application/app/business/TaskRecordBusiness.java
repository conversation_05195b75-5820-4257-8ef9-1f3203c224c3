package com.ets.delivery.application.app.business;

import cn.hutool.core.util.ObjectUtil;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.disposer.TaskDisposer;
import com.ets.delivery.application.common.config.queue.task.QueueTask;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordStatusEnum;
import com.ets.delivery.application.common.dto.task.TaskExecDTO;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.TaskRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TaskRecordBusiness {

    @Autowired
    private TaskRecordService taskRecordService;

    @Autowired
    private QueueTask queueTask;

    public void findAndExec(TaskExecDTO execDTO) {
        TaskRecord task = taskRecordService.getOneByTaskSn(execDTO.getTaskSn());
        if (ObjectUtil.isNull(task)) {
            ToolsHelper.throwException("任务不存在");
        }

        executeTask(task);
    }

    public TaskRecord getByReferSn(String referSn, String referType, boolean isThrowException) {

        TaskRecord taskRecord = taskRecordService.getByReferSn(referSn, referType);

        if (taskRecord == null && isThrowException) {
            ToolsHelper.throwException("任务记录不存在：" + referSn);
        }

        return taskRecord;
    }

    public void executeTask(TaskRecord task) {

        if (task.getStatus().equals(TaskRecordStatusEnum.TASK_STATUS_FINISH.getCode())) {
            ToolsHelper.throwException("任务已完成，不需要执行");
        }

        queueTask.push(new TaskDisposer(task));
    }
}
