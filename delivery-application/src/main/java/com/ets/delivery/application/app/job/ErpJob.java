package com.ets.delivery.application.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.delivery.application.app.business.ErpOrderBusiness;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Component
public class ErpJob {

    @Autowired
    private ErpOrderBusiness erpOrderBusiness;


    /**
     * ERP订单日检
     * @param params {"orderSource":"Yunda","startTime":"2024-04-16 00:00:00","endTime":"2024-04-16 23:59:59"}
     * @return ReturnT
     */
    @XxlJob("erpOrderDailyCheckHandler")
    public ReturnT<String> erpOrderDailyCheckHandler(String params) {
        if (StringUtils.isEmpty(params)) {
            log.error("【ERP订单日检】参数为空");
            return ReturnT.FAIL;
        }
        try {
            JSONObject jsonObject = JSON.parseObject(params);
            String orderSource = jsonObject.getString("orderSource");

            LocalDateTime startTime = LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endTime = LocalDateTime.now().minusDays(1).withHour(23).withMinute(59).withSecond(59);
            if (jsonObject.containsKey("startTime")) {
                startTime = LocalDateTime.parse(jsonObject.getString("startTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            if (jsonObject.containsKey("endTime")) {
                endTime = LocalDateTime.parse(jsonObject.getString("endTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }

            erpOrderBusiness.erpOrderDailyCheck(orderSource, startTime, endTime);
        } catch (Exception e) {
            log.error("【ERP订单日检】异常：{}", e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 韵达ERP订单查询修复
     * @param params ERP流水号
     * @return ReturnT
     */
    @XxlJob("fixErpOrderHandler")
    public ReturnT<String> fixErpOrderHandler(String params) {
        if (StringUtils.isEmpty(params)) {
            log.error("【ERP订单修复】参数为空");
            return ReturnT.FAIL;
        }
        try {
            erpOrderBusiness.fixErpOrder(params);
        } catch (Exception e) {
            log.error("【ERP订单修复】异常：{}", e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
