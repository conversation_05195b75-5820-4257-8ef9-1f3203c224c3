package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 导入拒收新旧快递单号记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_import_reject_express_detail")
public class ImportRejectExpressDetail extends BaseEntity<ImportRejectExpressDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 寄回快递单号
     */
    private String sendbackExpressNumber;

    /**
     * 入库快递单号
     */
    private String storageExpressNumber;

    /**
     * 分析结果等级
     */
    private Integer resultLevel;

    /**
     * 分析结果说明
     */
    private String resultMsg;

    /**
     * 状态[1-成功 2-失败]
     */
    private Integer recordStatus;

    /**
     * 匹配结果[0-默认 1-有结果 2-无结果]
     */
    private Integer matchResult;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
