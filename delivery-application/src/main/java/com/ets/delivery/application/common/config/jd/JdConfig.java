package com.ets.delivery.application.common.config.jd;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "jd")
public class JdConfig {
    String apiUrl;
    String accessToken;
    String appKey;
    String appSecret;
    String v;
    String customerCode;

    /**
     * 收件人所属省
     */
    String receiverProvince;

    /**
     * 收件人所属市
     */
    String receiverCity;

    /**
     * 收件人所属区
     */
    String receiverDistrict;

    /**
     * 收件人详细地址
     */
    String receiverDetailAddress;

    /**
     * 收件人姓名
     */
    String receiverName;

    /**
     * 收件人电话
     */
    String receiverPhone;

    /**
     * 取件费百分比
     */
    @Deprecated
    BigDecimal freightRate;

    /**
     * 默认取件费
     */
    BigDecimal defaultFreight;

    String desp = "ETC设备，内含电池";
    Double weight = 0.50;
    Double volume = 360.00;
}
