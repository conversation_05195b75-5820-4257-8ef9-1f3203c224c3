package com.ets.delivery.application.common.config.yunda;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "yunda")
public class YundaConfig {
    String appKey;
    String customerId;
    String warehouseCode;
    String format;
    String signMethod;
    String version;
    Integer channel;
    String secret;
    String shopNick;
    String apiUrl;
    String logisticsCode;
    Boolean checkSign = true;
}
