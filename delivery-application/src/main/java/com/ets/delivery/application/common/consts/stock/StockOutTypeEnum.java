package com.ets.delivery.application.common.consts.stock;

import com.ets.delivery.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum StockOutTypeEnum {

    // 韵达业务类型 PTCK=普通出库单（退仓），DBCK=调拨出库 ，B2BCK=B2B出库，QTCK=其他出库，CGTH=采购退货出库单

    DBCK(1, "DBCK", "调拨出库"),

    QTCK(2, "QTCK", "退货出库"),

    XNCK(3, "QTCK", "虚拟出库"),

    PTCK(4, "PTCK", "普通出库");

    private final Integer code;
    private final String yunDaCode;
    private final String description;

    public static String getDescByCode(int code) {

        for (StockOutTypeEnum node : StockOutTypeEnum.values()) {

            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static StockOutTypeEnum getByCode(int code) {

        for (StockOutTypeEnum node : StockOutTypeEnum.values()) {

            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        List<SelectOptionsVO> selectOptionsVOList = new ArrayList<>();
        for (StockOutTypeEnum node : StockOutTypeEnum.values()) {
            selectOptionsVOList.add(new SelectOptionsVO(node.getCode().toString(),node.getDescription()));
        }

        return selectOptionsVOList;
    }

}
