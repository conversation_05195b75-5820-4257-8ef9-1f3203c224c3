package com.ets.delivery.application.common.config.yunda;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "yunda-open-api")
public class YundaOpenApiConfig {
    String appKey;
    String appSecret;
    String apiUrl;
    // 寄件人信息
    String senderName;
    String senderProvince;
    String senderCity;
    String senderCounty;
    String senderAddress;
    String senderMobile;
}
