package com.ets.delivery.application.common.consts.pickUp;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum PickUpOrderTypeEnum {

    ORDER_TYPE_APPLY("apply", "申办"),
    ORDER_TYPE_JD_APPLY("jd_apply", "商城订单"),
    ORDER_TYPE_AFTER_SALES_RETURN("after_sales_return", "售后退货"),
    ORDER_TYPE_AFTER_SALES_CANCEL("after_sales_cancel", "售后注销"),
    ORDER_TYPE_AFTER_SALES_MAINTAIN_EXCHANGE("after_sales_maintain_exchange", "维修换货"),
    ORDER_TYPE_AFTER_SALES_EXCHANGE("after_sales_exchange", "售后换货"),
    ORDER_TYPE_AFTER_SALES_RECALL_EXCHANGE("after_sales_recall_exchange", "召回换货"),
    ORDER_TYPE_REAPPLY("reapply", "补办"),
    ORDER_TYPE_REISSUE_APPLY("reissue_apply", "补发-申办"),
    ORDER_TYPE_REISSUE_MAINTAIN_EXCHANGE("reissue_maintain_exchange", "补发-维修换货"),
    ORDER_TYPE_REISSUE_REAPPLY("reissue_reapply", "补发-补办"),
    ORDER_TYPE_REISSUE_LOST("reissue_lost", "补发-丢件"),
    ORDER_TYPE_REISSUE_HISTORY("reissue_history", "补发-历史发货失败"),
    ORDER_TYPE_OFFLINE("offline", "地推"),
    ORDER_TYPE_UPGRADE("upgrade", "设备升级"),
    ORDER_TYPE_GOODS("goods", "商品订单"),
    ORDER_TYPE_MANUAL("manual", "手动下单"),
    ORDER_TYPE_AFTER_SALES_REJECT("after_sales_reject", "售后拒收"),
    ORDER_TYPE_AFTER_SALES_RECOVERY("after_sales_recovery", "售后回收");


    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;

    static {
        PickUpOrderTypeEnum[] enums = PickUpOrderTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Arrays.stream(enums).map(PickUpOrderTypeEnum::getValue).collect(Collectors.toList());
    }
}
