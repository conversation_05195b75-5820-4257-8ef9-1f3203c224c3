package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 地址仓储映射规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_address_storage_map_rules")
public class AddressStorageMapRules extends BaseEntity<AddressStorageMapRules> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置ID
     */
    private Integer configId;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 规则映射仓储代号
     */
    private String storageCode;

    /**
     * 规则key值[area-区域 city-城市 province-省份]
     */
    private String ruleKey;

    /**
     * 规则value:多个value以,划分
     */
    private String ruleValue;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
