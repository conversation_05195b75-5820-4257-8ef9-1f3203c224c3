package com.ets.delivery.application.app.disposer;

import com.ets.common.queue.BaseDisposer;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.infra.entity.TaskRecord;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@NoArgsConstructor
@Component(value = "TaskJobBean")
public class TaskDisposer extends BaseDisposer {
    public TaskDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "TaskJobBean";
    }

    @Override
    public void execute(Object content) {
        TaskRecord taskRecord = super.getParamsObject(content, TaskRecord.class);
        // 执行任务
        TaskFactory.create(taskRecord.getReferType()).execute(taskRecord.getTaskSn());
    }
}
