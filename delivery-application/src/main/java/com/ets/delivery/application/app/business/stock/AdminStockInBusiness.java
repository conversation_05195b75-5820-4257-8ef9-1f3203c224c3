package com.ets.delivery.application.app.business.stock;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.BeanHelper;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.ets.common.util.NumberUtil;
import com.ets.delivery.application.app.business.BaseBusiness;
import com.ets.delivery.application.app.business.storageMina.StorageBusiness;
import com.ets.delivery.application.app.thirdservice.business.YundaBusiness;
import com.ets.delivery.application.app.thirdservice.request.yunda.StockCancelXmlDTO;
import com.ets.delivery.application.app.thirdservice.request.yunda.StockInCreateXmlDTO;
import com.ets.delivery.application.app.thirdservice.response.yunda.StockResponseVO;
import com.ets.delivery.application.common.config.yunda.YundaConfig;
import com.ets.delivery.application.common.consts.stock.StockGoodsQualityEnum;
import com.ets.delivery.application.common.consts.stock.StockInStatusEnum;
import com.ets.delivery.application.common.consts.stock.StockInTypeEnum;
import com.ets.delivery.application.common.consts.stock.StockTypeEnum;
import com.ets.delivery.application.common.dto.stock.*;
import com.ets.delivery.application.common.utils.AdminUserUtil;
import com.ets.delivery.application.common.utils.StockInUtil;
import com.ets.delivery.application.common.vo.stock.*;
import com.ets.delivery.application.infra.entity.StockInOrder;
import com.ets.delivery.application.infra.entity.Storage;
import com.ets.delivery.application.infra.relation.StockInBindGoodsInfoRelation;
import com.ets.delivery.application.infra.service.StockGoodsInfoService;
import com.ets.delivery.application.infra.service.StockInOrderService;
import com.ets.delivery.application.infra.service.StockLogService;
import com.ets.delivery.application.infra.service.StorageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
public class AdminStockInBusiness {

    @Autowired
    private BaseBusiness baseBusiness;

    @Autowired
    private StockInOrderService stockInOrderService;

    @Autowired
    private AdminStockBusiness adminStockBusiness;

    @Autowired
    private StorageBusiness storageBusiness;

    @Autowired
    private StockGoodsInfoService goodsInfoService;

    @Autowired
    private YundaBusiness yundaBusiness;

    @Autowired
    private YundaConfig yundaConfig;

    @Autowired
    private StorageService storageService;

    @Autowired
    private StockLogService stockLogService;

    public void exportFile(StockInListDTO dto, HttpServletResponse response) {

        // 获取数据
        List<StockInListVO> exportList = getList(dto).getRecords();

        try {
            // 设置文本内省
            response.setContentType("application/vnd.ms-excel");
            // 设置字符编码
            response.setCharacterEncoding("utf-8");
            // 设置文件名
            String filename = URLEncoder.encode("入库单导出结果" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename);
            EasyExcel.write(response.getOutputStream(), StockInExportVO.class)
                    .sheet("导出结果")
                    .doWrite(exportList);

        } catch (IOException e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            ToolsHelper.throwException("导出文件失败");
        }
    }

    public IPage<StockInListVO> getList(StockInListDTO dto) {
        // 分页设置
        IPage<StockInOrder> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);
        // 查询条件设置
        LambdaQueryWrapper<StockInOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(dto.getStockInSn()), StockInOrder::getStockInSn, dto.getStockInSn())
                .eq(dto.getStatus() != null, StockInOrder::getStatus, dto.getStatus())
                .eq(dto.getType() != null, StockInOrder::getType, dto.getType())
                .eq(dto.getGoodsQuality() != null, StockInOrder::getGoodsQuality, dto.getGoodsQuality())
                .gt(StringUtils.isNotEmpty(dto.getApplyTimeBegin()), StockInOrder::getApplyTime, dto.getApplyTimeBegin())
                .lt(StringUtils.isNotEmpty(dto.getApplyTimeEnd()), StockInOrder::getApplyTime, dto.getApplyTimeEnd() + " 23:59:59")
                .gt(StringUtils.isNotEmpty(dto.getInTimeBegin()), StockInOrder::getInTime, dto.getInTimeBegin())
                .lt(StringUtils.isNotEmpty(dto.getInTimeEnd()), StockInOrder::getInTime, dto.getInTimeEnd() + " 23:59:59")
                .orderByDesc(StockInOrder::getCreatedAt);

        if (StringUtils.isNotEmpty(dto.getDeliveryGoodsCode())) {

            List<String> stockSnList = adminStockBusiness.getStockSnListByGoods(dto.getDeliveryGoodsCode(), StockTypeEnum.IN);
            if (stockSnList == null || stockSnList.isEmpty()) {
                return null;
            }

            wrapper.in(StockInOrder::getStockInSn, stockSnList);
        }

        IPage<StockInOrder> pageList = stockInOrderService.getPageListByWrapper(oPage, wrapper);
        if (pageList.getSize() == 0) {
            return null;
        }

        // 绑定goodsInfo
        goodsInfoService.bindListToMasterEntityList(pageList.getRecords(), StockInBindGoodsInfoRelation.class);

        Map<String, Storage> storageMap = storageBusiness.getListMap();

        IPage<StockInListVO> list = pageList.convert(entity -> {

            StockInListVO vo = BeanUtil.copyProperties(entity, StockInListVO.class);

            vo.setInTime(ToolsHelper.localDateTimeToString(entity.getInTime()));
            vo.setApplyTime(ToolsHelper.localDateTimeToString(entity.getApplyTime()));
            vo.setStorageName(storageMap.get(entity.getStorageCode()).getName());
            vo.setTypeStr(StockInTypeEnum.getDescByCode(entity.getType()));
            vo.setGoodsQualityStr(StockGoodsQualityEnum.getDescByCode(entity.getGoodsQuality()));
            vo.setStatusStr(StockInStatusEnum.getDescByCode(entity.getStatus()));
            vo.setAllowCancel(StockInUtil.allowCancelStatus().contains(entity.getStatus()));
            vo.setAllowEdit(! StockInUtil.notAllowEditStatus().contains(entity.getStatus()));

            StockGoodsUnionInfoVO unionInfoVO = adminStockBusiness.getGoodsUnionInfo(entity.getGoodsInfo());
            vo.setGoodsNameInfo(unionInfoVO.getGoodsNameInfo());
            vo.setGoodsApplyCount(unionInfoVO.getGoodsApplyCount());
            vo.setGoodsRealCount(unionInfoVO.getGoodsRealCount());

            return vo;
        });

        return list;
    }

    public String makeStockInSn(Integer goodsQuality) {

        String stockInSn = goodsQuality.equals(StockGoodsQualityEnum.GOOD.getCode()) ? "LR" : "CR";

        stockInSn = stockInSn + baseBusiness.getDate() + baseBusiness.getSnNumber("stockInSnNumber", 3);

        return stockInSn;
    }

    @DSTransactional
    public StockInOrder create(StockInCreateDTO dto) {

        StockInOrder inOrder = BeanHelper.copy(StockInOrder.class, dto);
        inOrder.setStockInSn(makeStockInSn(dto.getGoodsQuality()));
        inOrder.setOperator(AdminUserUtil.getOperatorName());

        StockInTypeEnum typeEnum = StockInTypeEnum.getByCode(dto.getType());
        if (typeEnum == null) {
            ToolsHelper.throwException("不支持的类型");
        }

        stockInOrderService.create(inOrder);

        List<StockInCreateXmlDTO.OrderLine> orderLines = new ArrayList<>();

        dto.getGoodsInfo().forEach(stockGoodsInfoDTO -> {
            adminStockBusiness.createGoodsInfo(stockGoodsInfoDTO, StockTypeEnum.IN, inOrder.getStockInSn(), dto.getStorageCode());

            StockInCreateXmlDTO.OrderLine orderLine = new StockInCreateXmlDTO.OrderLine();
            orderLine.setOwnerCode(yundaConfig.getCustomerId());
            orderLine.setItemCode(stockGoodsInfoDTO.getStorageSku());
            orderLine.setPlanQty(stockGoodsInfoDTO.getApplyCount());
            orderLine.setInventoryType(StockGoodsQualityEnum.getInventoryTypeByCode(dto.getGoodsQuality()));

            orderLines.add(orderLine);
        });

        // 调韵达接口
        StockInCreateXmlDTO xmlDTO = new StockInCreateXmlDTO();
        StockInCreateXmlDTO.EntryOrder entryOrder = new StockInCreateXmlDTO.EntryOrder();
        entryOrder.setEntryOrderCode(inOrder.getStockInSn());
        entryOrder.setOrderType(typeEnum.getYunDaCode());
        entryOrder.setOwnerCode(yundaConfig.getCustomerId());
        entryOrder.setWarehouseCode(yundaConfig.getWarehouseCode());

        xmlDTO.setEntryOrder(entryOrder);

        xmlDTO.setOrderLines(orderLines);

        StockResponseVO response = yundaBusiness.stockInCreate(xmlDTO);

        stockInOrderService.applySuccess(inOrder.getStockInSn(), response);

        return inOrder;
    }

    public void edit(StockInEditDTO dto) {

        stockInOrderService.edit(dto);

        // 商品信息号段修改
        if (dto.getGoodsEditInfo() != null) {
            dto.getGoodsEditInfo().forEach(editInfo -> {
                adminStockBusiness.updateNumber(editInfo.getId(), editInfo.getNumberInfoList());
            });
        }
    }

    public StockInDataVO getData(StockInSnDTO dto) {

        StockInOrder inOrder = getBySn(dto.getStockInSn());

        StockInDataVO vo = new StockInDataVO();
        vo.setOrder(inOrder);

        vo.setGoodsInfo(adminStockBusiness.getGoodsInfoList(dto.getStockInSn(), StockTypeEnum.IN));

        return vo;
    }

    public StockInDetailVO detail(StockInSnDTO dto) {

        StockInOrder inOrder = getBySn(dto.getStockInSn());

        StockInDetailVO vo = BeanHelper.copy(StockInDetailVO.class, inOrder);
        vo.setTypeStr(StockInTypeEnum.getDescByCode(inOrder.getType()));
        vo.setGoodsQualityStr(StockGoodsQualityEnum.getDescByCode(inOrder.getGoodsQuality()));
        vo.setStatusStr(StockInStatusEnum.getDescByCode(inOrder.getStatus()));
        vo.setImageList(inOrder.getImageList());
        vo.setStorageCode(storageService.getStorageNameByCode(inOrder.getStorageCode()));

        StockGoodsUnionVO unionVO = new StockGoodsUnionVO();
        vo.setGoodsInfo(adminStockBusiness.getGoodsInfoVOList(dto.getStockInSn(), StockTypeEnum.IN, unionVO));
        vo.setGoodsUnion(unionVO);

        vo.setLogList(adminStockBusiness.getLogList(dto.getStockInSn(), StockTypeEnum.IN));

        return vo;
    }

    public void cancel(StockInSnDTO dto) {

        StockInOrder inOrder = getBySn(dto.getStockInSn());

        if (inOrder.getStatus().equals(StockInStatusEnum.CANCELED.getCode())) {
            return;
        }

        if (! StockInUtil.allowCancelStatus().contains(inOrder.getStatus())) {
            ToolsHelper.throwException("货物已入库，不能取消");
        }

        // 调韵达接口取消入库
        StockCancelXmlDTO xmlDTO = new StockCancelXmlDTO();
        xmlDTO.setOwnerCode(yundaConfig.getCustomerId());
        xmlDTO.setWarehouseCode(yundaConfig.getWarehouseCode());
        xmlDTO.setOrderCode(dto.getStockInSn());
        xmlDTO.setOrderType(StockInTypeEnum.getByCode(inOrder.getType()).getYunDaCode());
        yundaBusiness.stockCancel(xmlDTO);

        stockInOrderService.cancel(dto.getStockInSn());

        stockLogService.addLog(
                inOrder.getStockInSn(), StockTypeEnum.IN, StockInStatusEnum.CANCELED.getCode(), "入库单取消", RequestHelper.getAdminOperator()
        );
    }

    public StockInOrder getBySn(String stockInSn) {

        StockInOrder inOrder = stockInOrderService.getBySn(stockInSn);
        if (inOrder == null) {
            ToolsHelper.throwException("入库单不存在");
        }

        return inOrder;
    }
}
