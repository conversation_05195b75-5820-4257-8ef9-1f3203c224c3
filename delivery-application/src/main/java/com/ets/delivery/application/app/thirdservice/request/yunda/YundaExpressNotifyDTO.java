package com.ets.delivery.application.app.thirdservice.request.yunda;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class YundaExpressNotifyDTO {

    @XmlElement(nillable = true)
    private Long sourceOrder; // 设置为Integer会导致默认为0
    private String modeType;
    private String abnormalCause;
    private String branchPhone;
    @NotEmpty(message = "操作时间不能为空")
    private String operateTime;
    private String customerCode;
    private String branchName;
    private String description;
    @NotEmpty(message = "出库单号不能为空")
    private String deliveryOrderCode;
    private String warehouseCode;
    private String empPhone;
    private String logisticsCompanyName;
    private String empName;
    @NotEmpty(message = "快递单号不能为空")
    private String expressCode;
    @NotEmpty(message = "物流状态不能为空")
    private String omsOrderStatus;
}
