package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.KdApiFeign;
import org.springframework.cloud.openfeign.FallbackFactory;

import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

@Component
public class KdApiFallbackFactory implements FallbackFactory<KdApiFeign> {
    @Override
    public KdApiFeign create(Throwable throwable) {
        return new KdApiFeign() {
            @Override
            public String subscribe(MultiValueMap<String, Object> body) {
                return JsonResult.error("请求Kd100订阅接口失败: " + throwable.getMessage()).toString();
            }

            @Override
            public String expressQuery(MultiValueMap<String, Object> body) {
                return JsonResult.error("请求Kd100物流轨迹查询接口失败: " + throwable.getMessage()).toString();
            }
        };
    }
}
