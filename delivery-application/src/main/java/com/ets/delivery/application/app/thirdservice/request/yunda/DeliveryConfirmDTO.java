package com.ets.delivery.application.app.thirdservice.request.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.*;
import java.math.BigDecimal;
import java.util.List;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class DeliveryConfirmDTO {

    @XmlElementWrapper(name = "orderLines")
    @XmlElement(name = "orderLine")
    private List<OrderLine> orderLine;

    @XmlElement(name = "deliveryOrder")
    private DeliveryOrder deliveryOrder;

    @XmlElementWrapper(name = "packages")
    @XmlElement(name = "package")
    private List<Package> aPackage;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class OrderLine {
        String orderLineNo;
        String inventoryType;
        String planQty;
        String ownerCode;
        String actualPrice;
        String itemCode;
        String batchCode;
        String discountAmount;
        String subSourceCode;
        Integer actualQty;
        String productDate;
        String itemId;
        String produceCode;
        String itemName;
        String extCode;
        String orderSourceCode;
        String outBizCode;
        String expireDate;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class DeliveryOrder {
        String deliveryOrderCode;
        String deliveryOrderId;
        String warehouseCode;
        String orderType;
        String status;
        String outBizCode;
        Integer confirmType;
        String orderConfirmTime;

        String operatorName;
        Integer sourceOrder;
        String payTime;
        String logisticsCode;
        String logisticsName;
        String payNo;
        String sourcePlatformCode;
        String orderSourceCode;
        String expressCode;
        BigDecimal arAmount;

        @XmlElement(name = "receiver")
        Receiver receiver;

        @Data
        public static class Receiver {
            private String receiverName;
            private String receiverMobile;
            private String receiverProvince;
            private String receiverCity;
            private String receiverArea;
            private String receiverDetailAddress;
        }
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Package {
        String logisticsCode;
        String logisticsName;
        String expressCode;
        String packageCode;

        @XmlElementWrapper(name = "items")
        @XmlElement(name = "item")
        List<Item> item;

        @Data
        public static class Item {
            String itemCode;
            String itemId;
            Integer quantity;
        }
    }

}
