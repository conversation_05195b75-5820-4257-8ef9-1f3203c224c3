package com.ets.delivery.application.common.consts.express;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum ExpressSubscribeStatusEnum {

    DEFAULT(0, "未订阅"),
    SUBSCRIBING(1, "订阅中"),
    SUBSCRIBED(2, "已订阅"),
    SUBSCRIBE_FAILED(3, "订阅失败"),
    SUBSCRIBE_SHUTDOWN(4, "订阅结束"),
    SUBSCRIBE_ABORT(5, "订阅中止"),
    SUBSCRIBE_UPDATEALL(6, "重新推送"),
    SUBSCRIBE_ABNORMAL(7, "订阅异常");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    public static final Map<String, Integer> statusValueMap;

    ExpressSubscribeStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        ExpressSubscribeStatusEnum[] enums = ExpressSubscribeStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        statusValueMap = new LinkedHashMap<>();
        statusValueMap.put("polling", SUBSCRIBING.getValue());
        statusValueMap.put("shutdown", SUBSCRIBE_SHUTDOWN.getValue());
        statusValueMap.put("abort", SUBSCRIBE_ABORT.getValue());
        statusValueMap.put("updateall", SUBSCRIBE_UPDATEALL.getValue());
    }
}
