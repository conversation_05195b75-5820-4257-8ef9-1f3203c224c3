package com.ets.delivery.application.common.consts.pickUp;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum PickUpLogTypeEnum {

    ADD("add", "新增"),
    UPDATE("update", "更新"),
    CANCEL("cancel", "取消"),
    NOTIFY("notify", "状态推送通知");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    PickUpLogTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        PickUpLogTypeEnum[] enums = PickUpLogTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
