package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.ReDelivery;
import com.ets.delivery.application.infra.mapper.ReDeliveryMapper;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 补发货表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Service
@DS("db-issuer-admin-proxy")
public class ReDeliveryService extends BaseService<ReDeliveryMapper, ReDelivery> {

    public ReDelivery getByDeliverySn(String deliverySn) {
        Wrapper<ReDelivery> wrapper = Wrappers.<ReDelivery>lambdaQuery()
                .eq(ReDelivery::getDeliverySn, deliverySn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
