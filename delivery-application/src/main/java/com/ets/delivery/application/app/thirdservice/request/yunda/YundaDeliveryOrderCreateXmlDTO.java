package com.ets.delivery.application.app.thirdservice.request.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class YundaDeliveryOrderCreateXmlDTO implements Serializable {

    @XmlElement(name = "deliveryOrder")
    private DeliveryOrder deliveryOrder;

    @XmlElementWrapper(name = "orderLines")
    @XmlElement(name = "orderLine")
    private List<OrderLine> orderLine;

    @Data
    public static class DeliveryOrder {
        String sourcePlatformCode;
        String deliveryOrderCode;
        String orderType;
        String warehouseCode;
        String createTime;
        String placeOrderTime;
        String operateTime;
        String shopNick;
        String logisticsCode;
        ReceiverInfo receiverInfo;
        String remark = "";

        @Data
        public static class ReceiverInfo {
            String name;
            String mobile;
            String province;
            String city;
            String area;
            String detailAddress;
        }
    }

    @Data
    public static class OrderLine {
        String sourceOrderCode;
        String ownerCode;
        String itemCode;
        Integer planQty = 1;
    }
}
