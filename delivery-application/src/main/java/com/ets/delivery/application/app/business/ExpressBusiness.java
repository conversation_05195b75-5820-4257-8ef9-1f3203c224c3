package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ets.common.BeanHelper;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.ets.common.queue.EventDto;
import com.ets.delivery.application.app.disposer.ExpressNotifyDisposer;
import com.ets.delivery.application.app.disposer.ExpressNumberWrongDisposer;
import com.ets.delivery.application.app.disposer.ExpressSubscribeDisposer;
import com.ets.delivery.application.app.event.bean.EventBeans;
import com.ets.delivery.application.app.factory.express.ExpressFactory;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.common.bo.express.*;
import com.ets.delivery.application.common.bo.task.TaskLogisticsRejectCheckBO;
import com.ets.delivery.application.common.config.queue.express.QueueExpress;
import com.ets.delivery.application.common.consts.ErrCodeConstant;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeStatusEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeConstant;
import com.ets.delivery.application.common.dto.express.*;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.common.vo.express.ExpressSubscribeQueryVO;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.Express;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.service.ExWarehouseService;
import com.ets.delivery.application.infra.service.ExpressLogService;
import com.ets.delivery.application.infra.service.ExpressService;
import com.ets.delivery.application.infra.service.LogisticsService;
import com.ets.delivery.feign.request.express.ExpressBatchQueryDTO;
import com.ets.delivery.feign.response.express.ExpressBatchQueryVO;
import com.ets.starter.queue.QueueEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ExpressBusiness {

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private ExpressLogService expressLogService;

    @Autowired
    private ExWarehouseService exWarehouseService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private QueueExpress queueExpress;

    @Autowired
    private QueueEvent queueEvent;

    public Express expressQuery(ExWarehouse exWarehouse) {
        Express express = null;
        if (exWarehouse.getSplitFlag() == 0) {
            // 有物流单号
            if (StringUtils.isNotEmpty(exWarehouse.getWayBill())) {
                express = updateExpressQuery(exWarehouse);
            }
        } else if (exWarehouse.getSplitFlag() == 1) {
            // 获取拆单记录
            List<ExWarehouse> splitOrderList = exWarehouseService.getSplitOrderList(exWarehouse.getDeliverOrderNo());
            for (ExWarehouse splitOrder : splitOrderList) {
                if (StringUtils.isNotEmpty(splitOrder.getWayBill())) {
                    express = updateExpressQuery(splitOrder);
                }
            }
        }

        return express;
    }

    public Express initExpress(ExpressBO expressBO) {
        // 新建物流记录
        Express express = expressService.getOneByExpressNumber(expressBO.getExpressNumber());
        if (ObjectUtils.isNull(express)) {
            express = new Express();
            express.setExpressCode(expressBO.getExpressCode());
            String expressSn = ToolsHelper.genNum(redisPermanentTemplate, "express", ACTIVE, 8);
            express.setExpressSn(expressSn);
            express.setExpressNumber(expressBO.getExpressNumber());
            express.setExpressCompany(expressBO.getExpressCompany());
            express.setPhone(expressBO.getPhone());
            express.setSubscribeStatus(ExpressSubscribeStatusEnum.DEFAULT.getValue());
            express.setState(ExpressStateEnum.DEFAULT.getValue());
            express.setCreatedAt(LocalDateTime.now());
            express.setUpdatedAt(LocalDateTime.now());
            express.setSubscribeFailedTime(0);
            expressService.save(express);

            ExpressLogBO logBO = new ExpressLogBO();
            logBO.setExpressSn(expressSn);
            logBO.setExpressNumber(express.getExpressNumber());
            logBO.setSubscribeStatus(express.getSubscribeStatus());
            logBO.setContent("物流查询初始化");
            expressLogService.addLog(logBO);
        }

        return express;
    }

    public List<ExpressBatchQueryVO> batchQuery(ExpressBatchQueryDTO batchQueryDTO) {
        if (batchQueryDTO.getExpressNumberList().isEmpty()) {
            return Collections.emptyList();
        }
        if (batchQueryDTO.getExpressNumberList().size() > 1000) {
            ToolsHelper.throwException("最大物流查询量为1000");
        }
        List<Express> expressList = expressService.getListByExpressNumber(batchQueryDTO.getExpressNumberList());
        return expressList.stream().map(s -> {
            ExpressBatchQueryVO queryVO = BeanUtil.copyProperties(s, ExpressBatchQueryVO.class);
            queryVO.setStateStr(ExpressStateEnum.map.getOrDefault(queryVO.getState(), "未知"));
            return queryVO;
        }).collect(Collectors.toList());
    }

    public Express subscribe(ExpressSubscribeDTO subscribeDTO) {
        if (!ExpressCodeEnum.list.contains(subscribeDTO.getExpressCode())) {
            ToolsHelper.throwException("物流商编码不正确");
        }
        // 初始化物流信息
        ExpressBO expressBO = new ExpressBO();
        expressBO.setExpressCode(subscribeDTO.getExpressCode());
        expressBO.setExpressNumber(subscribeDTO.getExpressNumber());
        expressBO.setExpressCompany(subscribeDTO.getExpressCompany());
        expressBO.setPhone(subscribeDTO.getMobile());
        Express express = initExpress(expressBO);

        // 同一快递单不同手机号
        if (!express.getPhone().equals(subscribeDTO.getMobile())) {
            ToolsHelper.throwException("快递单号已存在，请检查是否有误");
        }

        // 已订阅过
        if (!express.getSubscribeStatus().equals(ExpressSubscribeStatusEnum.DEFAULT.getValue())) {
            return express;
        }

        // 异步订阅
        queueExpress.push(new ExpressSubscribeDisposer(subscribeDTO));

        return express;
    }

    public void expressNotify(ExpressNotifyDTO notifyDTO) {
        if (!ExpressCodeEnum.list.contains(notifyDTO.getExpressCode())) {
            ToolsHelper.throwException("物流商编码不正确");
        }

        // 异步处理回调
        queueExpress.push(new ExpressNotifyDisposer(notifyDTO));
    }

    public void sendExpressReceived(Express express) {
        ExpressReceivedEventBO expressReceivedEventBO = new ExpressReceivedEventBO();
        expressReceivedEventBO.setExpressNumber(express.getExpressNumber());
        expressReceivedEventBO.setExpressCompany(express.getExpressCompany());
        expressReceivedEventBO.setReceivedTime(ToolsHelper.localDateTimeToString(express.getReceivedTime()));

        EventDto eventDTO = EventDto.build()
                .bindMicroBean(EventBeans.EXPRESS_RECEIVED_EVENT_BEAN)
                .setParams(expressReceivedEventBO);
        queueEvent.pushEvent(eventDTO);
    }

    public void sendExpressNumberWrong(String expressNumber, String orderSn) {
        ExpressNumberWrongEventBO eventBO = new ExpressNumberWrongEventBO();
        eventBO.setExpressNumber(expressNumber);
        eventBO.setOrderSn(orderSn);

        queueExpress.push(new ExpressNumberWrongDisposer(eventBO));
    }

    public String getExpressCode(ExWarehouse exWarehouse) {
        String expressCode = ExpressCodeEnum.KD100.getValue();
        // 京东出库
        if (exWarehouse.getStorageCode().equals(StorageCodeEnum.JD_CLOUD.getValue())) {
            expressCode = ExpressCodeEnum.JD_CLOUD.getValue();
        }
        // 韵达出库
        if (exWarehouse.getStorageCode().equals(StorageCodeEnum.YUNDA.getValue())) {
            expressCode = ExpressCodeEnum.YUNDA.getValue();
        }
        return expressCode;
    }

    public void checkExpressReject(CheckExpressRejectDTO checkExpressRejectDTO) {
        try {
            TaskLogisticsRejectCheckBO checkBO = BeanUtil.copyProperties(checkExpressRejectDTO, TaskLogisticsRejectCheckBO.class);
            String content = JSON.toJSONString(checkBO);

            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferType(TaskRecordReferTypeConstant.LOGISTICS_REJECT_CHECK);
            taskRecordDTO.setReferSn(checkExpressRejectDTO.getOrderSn());
            taskRecordDTO.setNotifyContent(content);

            TaskFactory.create(TaskRecordReferTypeConstant.LOGISTICS_REJECT_CHECK).addAndPush(taskRecordDTO);
        } catch (Exception e) {
            log.error("创建物流拒收检查任务失败", e);
        }
    }

    private Express updateExpressQuery(ExWarehouse exWarehouse) {
        // 初始化物流记录
        ExpressBO expressBO = new ExpressBO();
        expressBO.setExpressCode(getExpressCode(exWarehouse));
        expressBO.setExpressNumber(exWarehouse.getWayBill());
        expressBO.setExpressCompany(exWarehouse.getShipperNo());
        if (exWarehouse.getStorageCode().equals(StorageCodeEnum.JD_CLOUD.getValue())) {
            expressBO.setExpressCompany("jd");
        }
        expressBO.setPhone(exWarehouse.getConsigneeMobile());
        Express express = initExpress(expressBO);

        // 查询更新物流轨迹
        if (ObjectUtils.isNotNull(express)) {
            // 已订阅结束
            if (express.getSubscribeStatus().equals(ExpressSubscribeStatusEnum.SUBSCRIBE_SHUTDOWN.getValue())) {
                return express;
            }
            // 订阅中止
            if (express.getSubscribeStatus().equals(ExpressSubscribeStatusEnum.SUBSCRIBE_ABORT.getValue())) {
                return express;
            }
            ExpressFactory.create(express.getExpressCode()).expressQuery(express);

            // 已签收
            if (express.getState().equals(ExpressStateEnum.RECEIVED.getValue())) {
                sendExpressReceived(express);
            }
        }

        return express;
    }

    public ExpressSubscribeQueryVO subscribeQuery(ExpressSubscribeQueryDTO subscribeQueryDTO) {
        // 查询express表
        Express express = expressService.getOneByExpressNumber(subscribeQueryDTO.getExpressNumber());

        // 查询是否我方发货
        Logistics logistics = logisticsService.getByExpressNumber(subscribeQueryDTO.getExpressNumber());

        // 不存在则发起订阅
        if (ObjectUtils.isEmpty(express)) {

            // 默认快递100
            String expressCode = ExpressCodeEnum.KD100.getValue();
            String expressCompany = null;
            String name = "无";
            String province = "无";
            String city = "无";
            String area = "无";
            String address = "无";
            // 京东仓、韵达仓
            if (ObjectUtils.isNotEmpty(logistics)) {
                name = logistics.getSendName();
                if (logistics.getStorageCode().equals(StorageCodeEnum.JD_CLOUD.getValue())) {
                    expressCode = ExpressCodeEnum.JD_CLOUD.getValue();
                    expressCompany = "jd";
                }
                if (logistics.getStorageCode().equals(StorageCodeEnum.YUNDA.getValue())) {
                    expressCode = ExpressCodeEnum.YUNDA.getValue();
                }
            }

            // 发起订阅
            ExpressSubscribeDTO subscribeDTO = new ExpressSubscribeDTO();
            subscribeDTO.setExpressCode(expressCode);
            subscribeDTO.setOrderSn(subscribeQueryDTO.getOrderSn());
            subscribeDTO.setExpressNumber(subscribeQueryDTO.getExpressNumber());
            subscribeDTO.setExpressCompany(expressCompany);
            subscribeDTO.setName(name);
            subscribeDTO.setProvince(province);
            subscribeDTO.setCity(city);
            subscribeDTO.setArea(area);
            subscribeDTO.setAddress(address);
            subscribeDTO.setMobile(subscribeQueryDTO.getMobile());

            express = subscribe(subscribeDTO);

            // 主动查询一次
            express = ExpressFactory.create(expressCode).expressQuery(express);
        }

        // 格式化物流轨迹
        ExpressSubscribeQueryVO subscribeQueryVO = new ExpressSubscribeQueryVO();
        subscribeQueryVO.setNumber(express.getExpressNumber());
        subscribeQueryVO.setCorp(ObjectUtils.isNotEmpty(logistics) ? logistics.getExpressCorp() : express.getExpressCompany());
        subscribeQueryVO.setSigned((express.getState().equals(ExpressStateEnum.RECEIVED.getValue())) ? 1 : 0);
        subscribeQueryVO.setReceivedTime(express.getReceivedTime());

        List<ExpressSubscribeQueryVO.TraceData> traceDataList = new ArrayList<>();
        if (StringUtils.isNotEmpty(express.getData())) {
            List<ExpressDataBO> expressDataList = JSON.parseObject(express.getData(), new TypeReference<List<ExpressDataBO>>() {});
            expressDataList.forEach(expressData -> {
                ExpressSubscribeQueryVO.TraceData traceData = new ExpressSubscribeQueryVO.TraceData();
                if (expressData.getFtime().contains("T")) {
                    expressData.setFtime(expressData.getFtime().replace("T", " "));
                }
                traceData.setTime(LocalDateTime.parse(expressData.getFtime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                traceData.setStatus(expressData.getContext());
                traceData.setTag(expressData.getStatus());
                traceDataList.add(traceData);
            });
        }

        subscribeQueryVO.setList(traceDataList);

        return subscribeQueryVO;
    }

    /**
     * 是否异常快递状态（即用户未收到货且不可能再收到货）
     * @param expressNumber
     * @param phone
     * @return
     */
    public Boolean isAbnormalStatus(String expressNumber, String phone) {
        Express express = expressService.getByNumberAndPhone(expressNumber, phone);
        if (express == null) {
            ToolsHelper.throwException("没有找到快递记录");
        }

        // 在途超15天
        if (express.getState().equals(ExpressStateEnum.ON_THE_WAY.getValue())
                && express.getLastExpressTime() != null
                && express.getLastExpressTime().isBefore(LocalDateTime.now().minusDays(15))
        ) {
            return true;
        }

        // 已退回
        if (express.getIsBack().equals(1)) {
            return true;
        }

        // 异常状态
        return Arrays.asList(
                ExpressStateEnum.REJECTED.getValue(),
                ExpressStateEnum.SEND_BACK.getValue(),
                ExpressStateEnum.RECEIVER_REJECT.getValue()
        ).contains(express.getState());
    }

    public Express getExpressByNumberAndPhone(String expressNumber, String phone) {
        return expressService.getByNumberAndPhone(expressNumber, phone);
    }

    public void subscribeExpressDirectly(ExpressSubscribeDirectlyDTO dto) {

        ExpressSubscribeDTO subscribeDTO = BeanHelper.copy(ExpressSubscribeDTO.class, dto);
        subscribeDTO.setExpressCode(ExpressCodeEnum.KD100.getValue());
        subscribeDTO.setProvince("无");
        subscribeDTO.setCity("无");
        subscribeDTO.setArea("无");
        subscribeDTO.setName("无");
        subscribeDTO.setAddress("无");

        if (subscribeDTO.getExpressNumber().length() > 30) {
            ToolsHelper.throwException("快递单号错误，无法订阅");
        }

        // 初始化物流信息
        ExpressBO expressBO = new ExpressBO();
        expressBO.setExpressCode(subscribeDTO.getExpressCode());
        expressBO.setExpressNumber(subscribeDTO.getExpressNumber());
        expressBO.setExpressCompany(subscribeDTO.getExpressCompany());
        expressBO.setPhone(subscribeDTO.getMobile());
        Express express = initExpress(expressBO);

        // 同一快递单不同手机号
        if (!express.getPhone().equals(subscribeDTO.getMobile())) {
            ToolsHelper.throwException("快递单号已存在，请检查是否有误");
        }

        // 已订阅过
        if (! express.getSubscribeStatus().equals(ExpressSubscribeStatusEnum.DEFAULT.getValue())) {
            return;
        }

        ExpressLogBO logBO = new ExpressLogBO();
        Express updateExpress = new Express();
        String errorMsg = "";

        // 发起物流订阅
        try {
            ExpressFactory.create(subscribeDTO.getExpressCode()).subscribe(subscribeDTO);
            // 订阅成功
            updateExpress.setSubscribeStatus(ExpressSubscribeStatusEnum.SUBSCRIBED.getValue());

        } catch (BizException e) {
            // 订阅失败 快递单号错误
            if (e.getErrorCode().equals(ErrCodeConstant.CODE_EXPRESS_NUMBER_WRONG.getCode())) {
                errorMsg = "订阅失败: 快递单号错误";
            } else {
                errorMsg = "订阅失败: " + e.getErrorMsg();
            }
            updateExpress.setSubscribeStatus(ExpressSubscribeStatusEnum.SUBSCRIBE_FAILED.getValue());
            updateExpress.setSubscribeFailedTime(express.getSubscribeFailedTime() + 1);
        } catch (Throwable e) {
            // 发起订阅失败
            updateExpress.setSubscribeStatus(ExpressSubscribeStatusEnum.SUBSCRIBE_FAILED.getValue());
            updateExpress.setSubscribeFailedTime(express.getSubscribeFailedTime() + 1);
            errorMsg = "发起订阅失败: " + e.getMessage();
        }
        updateExpress.setId(express.getId());
        expressService.updateById(updateExpress);

        // 记录日志
        logBO.setContent(StringUtils.isNotEmpty(errorMsg) ? errorMsg : "订阅成功");
        logBO.setExpressSn(express.getExpressSn());
        logBO.setExpressNumber(express.getExpressNumber());
        logBO.setSubscribeStatus(updateExpress.getSubscribeStatus());
        expressLogService.addLog(logBO);

        if (StringUtils.isNotEmpty(errorMsg)) {
            ToolsHelper.throwException(errorMsg);
        }
    }
}
