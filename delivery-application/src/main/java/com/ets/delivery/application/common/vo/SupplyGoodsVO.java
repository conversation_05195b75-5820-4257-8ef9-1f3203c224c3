package com.ets.delivery.application.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SupplyGoodsVO {

    private Integer id;

    /**
     * 所属仓储代号
     */
    private String storageCode;

    /**
     * 供应商代码
     */
    private String supplyCode;

    /**
     * 供应商名称
     */
    private String supplyName;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 货品代码
     */
    private String goodsCode;

    /**
     * 货品类型：主要为card,obu,card_obu
     */
    private String goodsType;

    /**
     * 设备厂商[0-未知 1-埃特斯 2-金溢 3-聚力 4-万集 5-成谷 6-云星宇]
     */
    private Integer manufacturer;

    /**
     * 设备类型[0-普通设备 1-可充电设备 2-单片式设备]
     */
    private Integer deviceType;

    /**
     * 状态,0不可使用，1可使用
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;
}
