package com.ets.delivery.application.app.business;

import com.ets.common.ToolsHelper;
import com.ets.starter.config.AppConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Date;

@Component
public class BaseBusiness {

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private AppConfig appConfig;

    public String generateSn(String key) {
        return ToolsHelper.genNum(redisPermanentTemplate, key, appConfig.getEnv(), 8);
    }

    public String getDate() {

        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");

        return formatter.format(new Date());
    }

    public String getSnNumber (String key, Integer len) {

        key = key + ":" + getDate();

        Long num;

        if (Boolean.valueOf(true).equals(redisPermanentTemplate.hasKey(key))) {
            num = redisPermanentTemplate.opsForValue().increment(key);
            redisPermanentTemplate.expire(key, Duration.ofHours(24));
        } else {
            num = redisPermanentTemplate.opsForValue().increment(key);

            if (String.valueOf(num).length() > len) {
                num = Long.parseLong(String.valueOf(num).substring(String.valueOf(num).length() - len));
            }
        }

        return StringUtils.leftPad(String.valueOf(num), len, "0");

    }

}
