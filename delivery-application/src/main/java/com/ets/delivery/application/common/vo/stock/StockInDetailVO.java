package com.ets.delivery.application.common.vo.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class StockInDetailVO {

    /**
     * 入库单号
     */
    private String stockInSn;

    /**
     * 库存编号
     */
    private String storageCode;

    /**
     * 实际入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inTime;

    /**
     * 申请入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 入库类型
     */
    private String typeStr;

    /**
     * 商品属性
     */
    private String goodsQualityStr;

    /**
     * 入库状态
     * NEW-未开始处理,  ACCEPT-仓库接单 , PARTFULFILLED-部分收货完成,  FULFILLED-收货完成,  EXCEPTION-异常,  CANCELED-取消,  CLOSED-关闭,  REJECT-拒单,  CANCELEDFAIL-取消失败
     */
    private String statusStr;

    /**
     * 图片
     */
    private List<String> imageList;

    /**
     * 商品聚合信息
     */
    private StockGoodsUnionVO goodsUnion;

    /**
     * 商品信息列表
     */
    private List<StockGoodsInfoVO> goodsInfo;

    /**
     * 日志列表
     */
    private List<StockLogVO> logList;

}
