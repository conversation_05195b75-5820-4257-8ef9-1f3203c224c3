package com.ets.delivery.application.common.consts.postReviews;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum PostReviewExceptionStatusEnum {
    // 异常处理状态[0-无需处理 1-未处理 2-已处理]
    EXCEPTION_STATUS_DEFAULT(0, "无需处理"),
    EXCEPTION_STATUS_WAIT(1, "未处理"),
    EXCEPTION_STATUS_DONE(2, "已处理");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    PostReviewExceptionStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        PostReviewExceptionStatusEnum[] enums = PostReviewExceptionStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
