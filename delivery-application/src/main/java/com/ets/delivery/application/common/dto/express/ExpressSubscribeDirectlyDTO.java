package com.ets.delivery.application.common.dto.express;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class ExpressSubscribeDirectlyDTO {

    /**
     * 快递单号
     */
    @NotBlank(message = "快递单号不能为空")
    String expressNumber;

    /**
     * 收件人姓名
     */
    @NotBlank(message = "收件人姓名不能为空")
    String name;

    /**
     * 收件详细地址
     */
    @NotBlank(message = "收件详细地址不能为空")
    String address;

    /**
     * 收件人手机
     */
    @NotBlank(message = "收件人手机号码不能为空")
    String mobile;
}
