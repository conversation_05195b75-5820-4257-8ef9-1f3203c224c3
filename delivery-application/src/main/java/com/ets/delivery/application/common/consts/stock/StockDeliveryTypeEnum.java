package com.ets.delivery.application.common.consts.stock;

import com.ets.delivery.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum StockDeliveryTypeEnum {

    SELF(1, "到仓自提"),

    EXPRESS(2, "快递");

    private final Integer code;
    private final String description;

    public static String getDescByCode(int code) {

        for (StockDeliveryTypeEnum node : StockDeliveryTypeEnum.values()) {

            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static StockDeliveryTypeEnum getByCode(int code) {

        for (StockDeliveryTypeEnum node : StockDeliveryTypeEnum.values()) {

            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        List<SelectOptionsVO> selectOptionsVOList = new ArrayList<>();
        for (StockDeliveryTypeEnum node : StockDeliveryTypeEnum.values()) {
            selectOptionsVOList.add(new SelectOptionsVO(node.getCode().toString(),node.getDescription()));
        }

        return selectOptionsVOList;
    }
}
