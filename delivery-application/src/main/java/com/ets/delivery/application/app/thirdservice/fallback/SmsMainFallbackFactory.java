package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.SmsMainFeign;
import com.ets.delivery.application.app.thirdservice.request.SmsSendDTO;
import org.springframework.cloud.openfeign.FallbackFactory;

import org.springframework.stereotype.Component;

@Component
public class SmsMainFallbackFactory implements FallbackFactory<SmsMainFeign> {
    @Override
    public SmsMainFeign create(Throwable throwable) {
        return new SmsMainFeign() {
            @Override
            public String sendSms(SmsSendDTO sendDTO) {
                return JsonResult.error("短信服务发送接口异常" + throwable.getLocalizedMessage()).toString();
            }
        };
    }
}
