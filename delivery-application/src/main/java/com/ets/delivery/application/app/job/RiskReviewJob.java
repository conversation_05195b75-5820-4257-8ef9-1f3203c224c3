package com.ets.delivery.application.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.delivery.application.app.business.RiskReviewQueueBusiness;
import com.ets.delivery.application.app.thirdservice.feign.RiskFeign;
import com.ets.delivery.application.app.thirdservice.request.risk.RiskCreateDTO;
import com.ets.delivery.application.common.consts.reviews.ReviewsRiskStatusEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewRiskTypeEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import com.ets.delivery.application.infra.entity.Reviews;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviews;
import com.ets.delivery.application.infra.service.ReviewsService;
import com.ets.delivery.application.infra.service.RiskReviewsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class RiskReviewJob {

    @Autowired
    private ReviewsService reviewsService;

    @Autowired
    private RiskReviewsService riskReviewsService;

    @Autowired
    private RiskReviewQueueBusiness riskReviewQueueBusiness;

    @Autowired
    private RiskFeign riskFeign;

    @XxlJob("applyReviewOrderRiskHandler")
    public ReturnT<String> applyReviewOrderRiskHandler(String param) {
        try {
            if (StringUtils.isBlank(param)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
            }

            // 解析参数：格式为 "操作类型,查询条数" 例如 "0,100" 或 "1,50"
            String[] params = param.trim().split(",");
            if (params.length < 1 || params.length > 2) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数格式错误，格式应为：操作类型[,查询条数]，例如：0,100");
            }

            int operationType;
            int queryLimit = 100; // 默认查询条数

            try {
                operationType = Integer.parseInt(params[0].trim());
            } catch (NumberFormatException e) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "操作类型格式错误，必须为数字");
            }

            if (params.length == 2) {
                try {
                    queryLimit = Integer.parseInt(params[1].trim());
                    if (queryLimit <= 0 || queryLimit > 1000) {
                        return new ReturnT<>(ReturnT.FAIL_CODE, "查询条数必须在1-1000之间");
                    }
                } catch (NumberFormatException e) {
                    return new ReturnT<>(ReturnT.FAIL_CODE, "查询条数格式错误，必须为数字");
                }
            }

            if (operationType == 0) {
                // 查询待风控审核单记录
                return queryPendingRiskReviews(queryLimit);
            } else if (operationType == 1) {
                // 作废旧风控初审单记录，重新请求风控
                return cancelAndReprocessRiskReviews(queryLimit);
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "操作类型错误，只支持0或1");
            }

        } catch (Exception e) {
            log.error("执行风控审核任务失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败：" + e.getMessage());
        }
    }

    @XxlJob("releaseUserRiskReviewHandler")
    public ReturnT<String> releaseUserRiskReviewHandler(String param) {
        try {
            // 解析参数：查询条数限制，默认100
            int queryLimit = 100;
            if (StringUtils.isNotBlank(param)) {
                try {
                    queryLimit = Integer.parseInt(param.trim());
                    if (queryLimit <= 0 || queryLimit > 1000) {
                        return new ReturnT<>(ReturnT.FAIL_CODE, "查询条数必须在1-1000之间");
                    }
                } catch (NumberFormatException e) {
                    return new ReturnT<>(ReturnT.FAIL_CODE, "参数格式错误，必须为数字");
                }
            }

            return releaseUserRiskReviews(queryLimit);

        } catch (Exception e) {
            log.error("释放用户风控审核记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败：" + e.getMessage());
        }
    }

    /**
     * 释放用户风控审核记录
     * @param limit 查询条数限制
     */
    private ReturnT<String> releaseUserRiskReviews(int limit) {
        try {
            // 获取待审核的风控记录（初审和复审）
            List<RiskReviews> pendingFirstReviews = riskReviewsService.getDefaultListLimitDate(
                RiskReviewRiskTypeEnum.FIRST_REVIEW.getValue(), 7, limit);
            List<RiskReviews> pendingRecheckReviews = riskReviewsService.getDefaultListLimitDate(
                RiskReviewRiskTypeEnum.RECHECK_REVIEW.getValue(), 7, limit);

            int releasedCount = 0;
            int totalChecked = 0;

            // 处理初审记录
            releasedCount += processRiskReviewRelease(pendingFirstReviews, RiskReviewRiskTypeEnum.FIRST_REVIEW.getValue());
            totalChecked += pendingFirstReviews.size();

            // 处理复审记录
            releasedCount += processRiskReviewRelease(pendingRecheckReviews, RiskReviewRiskTypeEnum.RECHECK_REVIEW.getValue());
            totalChecked += pendingRecheckReviews.size();

            String resultMsg = String.format("处理完成 - 检查了 %d 条风控审核记录，释放了 %d 条记录的operator",
                totalChecked, releasedCount);
            log.info(resultMsg);

            return new ReturnT<>(resultMsg);

        } catch (Exception e) {
            log.error("释放用户风控审核记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "处理失败：" + e.getMessage());
        }
    }

    /**
     * 处理风控审核记录释放
     * @param riskReviewsList 风控审核记录列表
     * @param riskType 风控类型
     * @return 释放的记录数量
     */
    private int processRiskReviewRelease(List<RiskReviews> riskReviewsList, Integer riskType) {
        int releasedCount = 0;

        for (RiskReviews riskReview : riskReviewsList) {
            try {
                // 筛选出有operator的记录
                if (StringUtils.isBlank(riskReview.getOperator())) {
                    continue;
                }

                // 查询缓存是否有领取记录
                List<String> userRiskReviewList = riskReviewQueueBusiness.getUserRiskReviewList(
                    riskReview.getOperator(), riskType);

                // 检查当前记录是否在用户的领取记录中
                boolean hasClaimRecord = userRiskReviewList != null &&
                    userRiskReviewList.contains(riskReview.getRiskReviewSn());

                if (!hasClaimRecord) {
                    // 保存原操作人信息用于日志记录
                    String originalOperator = riskReview.getOperator();

                    // 如果没有领取记录，则清理operator，释放出来让其他人可以领取
                    riskReview.setOperator("");
                    riskReviewsService.updateById(riskReview);
                    releasedCount++;

                    // 重新推到队列
                    riskReviewQueueBusiness.rePushToRiskReviewQueueList(riskReview.getRiskReviewSn(), riskType);

                    log.info("释放风控审核记录 - 风控单号: {}, 业务单号: {}, 原操作人: {}, 风控类型: {}",
                        riskReview.getRiskReviewSn(),
                        riskReview.getBusinessSn(),
                        originalOperator,
                        riskType == 1 ? "初审" : "复审");
                } else {
                    log.debug("风控审核记录仍在用户领取列表中 - 风控单号: {}, 操作人: {}, 风控类型: {}",
                        riskReview.getRiskReviewSn(),
                        riskReview.getOperator(),
                        riskType == 1 ? "初审" : "复审");
                }

            } catch (Exception e) {
                log.error("处理风控审核记录释放失败 - 风控单号: {}, 业务单号: {}",
                    riskReview.getRiskReviewSn(), riskReview.getBusinessSn(), e);
            }
        }

        return releasedCount;
    }

    /**
     * 查询待风控审核单记录
     * @param limit 查询条数限制
     */
    private ReturnT<String> queryPendingRiskReviews(int limit) {
        try {
            // 查询待风控的审核单记录
            List<Reviews> pendingReviews = reviewsService.getPendingRiskReviewList(limit);

            if (pendingReviews.isEmpty()) {
                log.info("未找到待风控审核单记录，查询条数限制: {}", limit);
                return new ReturnT<>("未找到待风控审核单记录");
            }

            log.info("查询到 {} 条待风控审核单记录，查询条数限制: {}", pendingReviews.size(), limit);

            // 输出详细信息
            for (Reviews review : pendingReviews) {
                log.info("待风控审核单 - 审核单号: {}, 业务单号: {}, 车牌号: {}, 发卡方ID: {}, 创建时间: {}",
                    review.getReviewSn(),
                    review.getOrderSn(),
                    review.getPlateNo(),
                    review.getIssuerId(),
                    review.getCreatedAt());
            }

            return new ReturnT<>("查询完成，共找到 " + pendingReviews.size() + " 条待风控审核单记录（限制条数: " + limit + "）");

        } catch (Exception e) {
            log.error("查询待风控审核单记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 作废旧风控初审单记录，重新请求风控
     * @param limit 查询条数限制
     */
    private ReturnT<String> cancelAndReprocessRiskReviews(int limit) {
        try {
            // 首先查询待风控的审核单记录
            List<Reviews> pendingReviews = reviewsService.getPendingRiskReviewList(limit);

            if (pendingReviews.isEmpty()) {
                log.info("未找到待风控审核单记录，查询条数限制: {}", limit);
                return new ReturnT<>("未找到待风控审核单记录");
            }

            log.info("开始处理 {} 条待风控审核单记录，查询条数限制: {}", pendingReviews.size(), limit);

            int canceledCount = 0;
            int reprocessedCount = 0;

            for (Reviews review : pendingReviews) {
                try {
                    // 根据业务单号查询对应的风控初审单记录
                    RiskReviews riskReview = riskReviewsService.getByBusinessSnAndRiskType(
                        review.getOrderSn(), RiskReviewRiskTypeEnum.FIRST_REVIEW.getValue());

                    if (riskReview != null && riskReview.getRiskReviewStatus().equals(RiskReviewStatusEnum.PENDING.getValue())) {
                        // 作废旧的风控初审单记录
                        riskReview.setRiskReviewStatus(RiskReviewStatusEnum.CANCELED.getValue());
                        riskReview.setRiskReviewRemark("系统自动作废，重新请求风控");
                        riskReviewsService.updateById(riskReview);
                        canceledCount++;

                        log.info("作废风控初审单 - 风控单号: {}, 业务单号: {}",
                            riskReview.getRiskReviewSn(), riskReview.getBusinessSn());
                    }

                    // 确保审核单状态为待风控（可能已经是，但确保一致性）
                    if (!review.getRiskStatus().equals(ReviewsRiskStatusEnum.PENDING.getValue())) {
                        review.setRiskStatus(ReviewsRiskStatusEnum.PENDING.getValue());
                        reviewsService.updateById(review);
                        reprocessedCount++;

                        log.info("重新设置待风控状态 - 审核单号: {}, 业务单号: {}",
                            review.getReviewSn(), review.getOrderSn());
                    }

                    // 请求风控
                    Integer bizType = 0;
                    JSONObject jsonObject = JSON.parseObject(review.getContentParams());
                    if (jsonObject != null) {
                        JSONObject orderInfo = jsonObject.getJSONObject("order_info");
                        if (orderInfo != null) {
                            bizType = jsonObject.getInteger("biz_type");
                        }
                    }

                    Map<String, Object> riskParams = new HashMap<>();
                    riskParams.put("issuerId", review.getIssuerId());
                    riskParams.put("carType", review.getIsTruck() == 1 ? 2 : 1);
                    riskParams.put("type", bizType);

                    RiskCreateDTO createDTO = new RiskCreateDTO();
                    createDTO.setBusinessSn(review.getOrderSn());
                    createDTO.setBusinessType(1);
                    createDTO.setNotifyUrl("http://delivery-application/notify/review/risk-result-notify");
                    createDTO.setRiskParams(JSON.toJSONString(riskParams));
                    riskFeign.accept(createDTO);

                } catch (Exception e) {
                    log.error("处理审核单失败 - 审核单号: {}, 业务单号: {}",
                        review.getReviewSn(), review.getOrderSn(), e);
                }
            }

            String resultMsg = String.format("处理完成 - 基于 %d 条待风控审核单（限制条数: %d），作废风控初审单: %d 条, 重新设置待风控: %d 条",
                pendingReviews.size(), limit, canceledCount, reprocessedCount);
            log.info(resultMsg);

            return new ReturnT<>(resultMsg);

        } catch (Exception e) {
            log.error("作废风控初审单记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "处理失败：" + e.getMessage());
        }
    }
}
