package com.ets.delivery.application.common.vo;

import com.ets.delivery.application.common.consts.ChannelIdEnum;
import com.ets.delivery.application.common.consts.EmergencyTypeEnum;
import com.ets.delivery.application.common.consts.reviews.PushIssuerStatusEnum;
import com.ets.delivery.application.common.consts.reviews.ReviewNotifyStatusEnum;
import com.ets.delivery.application.common.consts.reviews.ReviewReviewStatusEnum;
import com.ets.delivery.application.common.consts.reviews.ReviewStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ReviewListVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 审核单流水号
     */
    private String reviewSn;

    /**
     * 订单流水号
     */
    private String orderSn;

    /**
     * 对接方id
     */
    private Integer issuerId;

    /**
     * 发卡方名称
     */
    private String issuerName;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 资料审核状态,0待审核，1审核中，2审核通过，3审核拒绝，4审核取消
     */
    private Integer reviewStatus;

    private String reviewStatusStr;

    /**
     * 资料审核备注
     */
    private String reviewRemark;

    /**
     * 资料审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime reviewTime;

    /**
     * 领取时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime drawTime;

    /**
     * 状态：1正常2取消3暂停
     */
    private Integer status;

    private String statusStr;

    /**
     * 推送发卡方状态,0待推送，1推送中，2推送成功，3推送失败，4推送取消，5推送结果未定义
     */
    private Integer pushIssuerStatus;

    private String pushIssuerStatusStr;

    /**
     * 推送备注
     */
    private String pushIssuerRemark;

    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pushIssuerTime;

    /**
     * 通知申办的状态：0待通知，1通知成功，2通知失败
     */
    private Integer notifyStatus;

    private String notifyStatusStr;

    /**
     * 通知成功时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime notifyTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 是否推送自动审核：0、人工审核 1、自动审核
     */
    private Integer autoAudit;

    /**
     * 渠道值
     */
    private String channel;

    /**
     * 渠道值描述
     */
    private String channelStr;

    /**
     * 后审类型:默认0-无需后审 1-正常后审 2-优先后审 3-紧急后审
     */
    private Integer emergencyType;

    /**
     * 后审类型描述
     */
    private String emergencyTypeStr;


    public String getReviewStatusStr() {
        return ReviewReviewStatusEnum.map.getOrDefault(this.reviewStatus, "未知");
    }

    public String getStatusStr() {
        return ReviewStatusEnum.map.getOrDefault(this.status, "未知");
    }

    public String getPushIssuerStatusStr() {
        return PushIssuerStatusEnum.map.getOrDefault(this.pushIssuerStatus, "未知");
    }

    public String getNotifyStatusStr() {
        return ReviewNotifyStatusEnum.map.getOrDefault(this.notifyStatus, "未知");
    }

    public String getChannelStr() {
        return ChannelIdEnum.descMap.getOrDefault(this.channel, "未知");
    }

    public String getEmergencyTypeStr() {
        return EmergencyTypeEnum.map.getOrDefault(this.emergencyType, "未知");
    }
}
