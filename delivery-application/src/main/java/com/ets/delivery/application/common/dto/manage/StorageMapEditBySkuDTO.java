package com.ets.delivery.application.common.dto.manage;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class StorageMapEditBySkuDTO {

    /**
     * 商品代码
     */
    @NotBlank(message = "Sku不能为空")
    private String skuSn;

    /**
     * 仓库代号
     */
    @NotBlank(message = "仓库代号storageCode不能为空")
    private String storageCode;

    /**
     * 仓库对应sku的编码
     */
    @NotBlank(message = "仓库对应sku的编码storageSku不能为空")
    private String storageSku;

    private String storageSkuName = "";

    /**
     * 状态：1正常2关闭
     */
    private Integer mapStatus = 1;
}
