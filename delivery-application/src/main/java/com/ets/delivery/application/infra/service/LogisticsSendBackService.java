package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.common.bo.sendback.LogisticsSendBackPageBO;
import com.ets.delivery.application.common.consts.sendback.SendBackReceiveStatusEnum;
import com.ets.delivery.application.common.consts.sendback.SendBackStatusEnum;
import com.ets.delivery.application.infra.entity.LogisticsSendBack;
import com.ets.delivery.application.infra.mapper.LogisticsSendBackMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 寄回件列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Service
@DS("db-issuer-admin-proxy")
public class LogisticsSendBackService extends BaseService<LogisticsSendBackMapper, LogisticsSendBack> {

    public LogisticsSendBack getInfoBySn(String sendBackSn) {
        Wrapper<LogisticsSendBack> wrapper = Wrappers.<LogisticsSendBack>lambdaQuery()
                .eq(LogisticsSendBack::getSendbackSn, sendBackSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public LogisticsSendBack getUnfinishedOrder(String orderSn) {
        Wrapper<LogisticsSendBack> wrapper = Wrappers.<LogisticsSendBack>lambdaQuery()
                .eq(LogisticsSendBack::getOrderSn, orderSn)
                .eq(LogisticsSendBack::getStatus, SendBackStatusEnum.STATUS_NORMAL.getValue())
                .in(LogisticsSendBack::getReviceStatus, Arrays.asList(
                        SendBackReceiveStatusEnum.RECEIVE_STATUS_WAIT.getValue(),
                        SendBackReceiveStatusEnum.RECEIVE_STATUS_OVERTIME.getValue(),
                        SendBackReceiveStatusEnum.RECEIVE_STATUS_ENTER.getValue()))
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public List<LogisticsSendBack> getWaitRecord(LocalDateTime startTime) {
        Wrapper<LogisticsSendBack> wrapper = Wrappers.<LogisticsSendBack>lambdaQuery()
                .eq(LogisticsSendBack::getStatus, SendBackStatusEnum.STATUS_NORMAL.getValue())
                .eq(LogisticsSendBack::getReviceStatus, SendBackReceiveStatusEnum.RECEIVE_STATUS_WAIT.getValue())
                .ne(LogisticsSendBack::getExpressNumber, "")
                .ge(LogisticsSendBack::getCreatedAt, startTime)
                .last("limit 1000");
        return this.baseMapper.selectList(wrapper);
    }

    public LogisticsSendBack getByExpressNumber(String expressNumber) {

        if (StringUtils.isEmpty(expressNumber)) {
            return null;
        }

        LambdaQueryWrapper<LogisticsSendBack> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogisticsSendBack::getExpressNumber, expressNumber)
                .orderByDesc(LogisticsSendBack::getCreatedAt);

        return getOneByWrapper(wrapper);
    }

    public void updateReceiveInfo(String sendBackSn, Integer receiveStatus, String remark, LocalDateTime receiveTime) {

        LambdaUpdateWrapper<LogisticsSendBack> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(LogisticsSendBack::getSendbackSn, sendBackSn)
                .set(LogisticsSendBack::getReviceStatus, receiveStatus)
                .set(LogisticsSendBack::getReviceRemark, remark)
                .set(ObjectUtils.isNotEmpty(receiveTime), LogisticsSendBack::getReviceTime, receiveTime);

        updateByWrapper(wrapper);
    }

    @Override
    public int updateByWrapper(LambdaUpdateWrapper<LogisticsSendBack> lambdaUpdateWrapper) {
        lambdaUpdateWrapper.set(LogisticsSendBack::getUpdatedAt, ToolsHelper.getDateTime());

        return super.updateByWrapper(lambdaUpdateWrapper);
    }

    public void notifySuccess(String sendBackSn) {

        LambdaUpdateWrapper<LogisticsSendBack> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(LogisticsSendBack::getSendbackSn, sendBackSn)
                .set(LogisticsSendBack::getNotifyStatus, 2)
                .set(LogisticsSendBack::getNotifyRemark, "通知成功");

        updateByWrapper(wrapper);
    }

    public void notifyFailed(String sendBackSn, String errorMsg) {

        LambdaUpdateWrapper<LogisticsSendBack> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(LogisticsSendBack::getSendbackSn, sendBackSn)
                .set(LogisticsSendBack::getNotifyStatus, 3)
                .set(LogisticsSendBack::getNotifyRemark, errorMsg);

        updateByWrapper(wrapper);
    }

    public IPage<LogisticsSendBack> getPage(LogisticsSendBackPageBO pageBO) {
        Wrapper<LogisticsSendBack> wrapper = Wrappers.<LogisticsSendBack>lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(pageBO.getIssuerId()), LogisticsSendBack::getIssuerId, pageBO.getIssuerId())
                .eq(StringUtils.isNotEmpty(pageBO.getOrderSn()), LogisticsSendBack::getOrderSn, pageBO.getOrderSn())
                .eq(StringUtils.isNotEmpty(pageBO.getPlateNo()), LogisticsSendBack::getPlateNo, pageBO.getPlateNo())
                .eq(StringUtils.isNotEmpty(pageBO.getOrderSource()), LogisticsSendBack::getOrderSource, pageBO.getOrderSource())
                .eq(StringUtils.isNotEmpty(pageBO.getOrderType()), LogisticsSendBack::getOrderType, pageBO.getOrderType())
                .and(StringUtils.isNotEmpty(pageBO.getExpressNumber()), wrapper1 -> wrapper1
                        .eq(LogisticsSendBack::getExpressNumber, pageBO.getExpressNumber())
                        .or()
                        .eq(LogisticsSendBack::getOriginExpressNumber, pageBO.getExpressNumber()))
                .eq(ObjectUtils.isNotEmpty(pageBO.getReceiveStatus()), LogisticsSendBack::getReviceStatus, pageBO.getReceiveStatus())
                .eq(ObjectUtils.isNotEmpty(pageBO.getNotifyStatus()), LogisticsSendBack::getNotifyStatus, pageBO.getNotifyStatus())
                .in(ObjectUtils.isNotEmpty(pageBO.getStorageCodeList()), LogisticsSendBack::getStorageCode, pageBO.getStorageCodeList())
                .eq(StringUtils.isNotEmpty(pageBO.getSendbackSn()), LogisticsSendBack::getSendbackSn, pageBO.getSendbackSn())
                .ge(ObjectUtils.isNotEmpty(pageBO.getCreateStartTime()), LogisticsSendBack::getCreatedAt, pageBO.getCreateStartTime())
                .le(ObjectUtils.isNotEmpty(pageBO.getCreateEndTime()), LogisticsSendBack::getCreatedAt, pageBO.getCreateEndTime())
                .ge(ObjectUtils.isNotEmpty(pageBO.getReceiveStartTime()), LogisticsSendBack::getReviceTime, pageBO.getReceiveStartTime())
                .le(ObjectUtils.isNotEmpty(pageBO.getReceiveEndTime()), LogisticsSendBack::getReviceTime, pageBO.getReceiveEndTime())
                .orderByDesc(LogisticsSendBack::getCreatedAt);

        return this.baseMapper.selectPage(new Page<>(pageBO.getPageNum(), pageBO.getPageSize()), wrapper);
    }

    public void cancel(String sendBackSn) {

        LambdaUpdateWrapper<LogisticsSendBack> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(LogisticsSendBack::getSendbackSn, sendBackSn)
                .set(LogisticsSendBack::getStatus, SendBackStatusEnum.STATUS_CANCEL.getValue());

        updateByWrapper(wrapper);
    }
}
