package com.ets.delivery.application.common.dto.supplyGoods;

import com.ets.delivery.application.common.consts.supplyGoods.GoodsStatusEnum;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
public class SupplyGoodsChangeStatusDTO {

    /**
     * 主键ID
     */
    @NotNull(message = "id不能为空")
    private Integer id;

    /**
     * 状态：1正常2取消
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    public boolean checkStatus() {
        return GoodsStatusEnum.list.contains(this.status);
    }
}
