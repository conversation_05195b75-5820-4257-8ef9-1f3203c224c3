package com.ets.delivery.application.common.consts.logistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum LogisticsOperateTypeEnum {
    TYPE_ADD("add","新增"),
    TYPE_MODIFY("modify","修改"),
    TYPE_RECEIVE("receive","领取"),
    TYPE_DELIVERY("delivery","发货"),
    TYPE_SHIP("ship","发货通知"),
    TYPE_CANCEL("cancel", "取消");

    private final String code;
    private final String description;
    public static final Map<String, String> map;

    static {
        map = Arrays.stream(LogisticsOperateTypeEnum.values())
                .collect(Collectors.toMap(LogisticsOperateTypeEnum::getCode, LogisticsOperateTypeEnum::getDescription));
    }
}
