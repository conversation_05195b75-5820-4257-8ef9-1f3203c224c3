package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.GoodsFeign;
import com.ets.delivery.application.app.thirdservice.request.goods.GoodsSkuInfoDTO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class GoodsFallbackFactory implements FallbackFactory<GoodsFeign> {
    @Override
    public GoodsFeign create(Throwable cause) {
        return new GoodsFeign() {
            @Override
            public String getSkusInfo(GoodsSkuInfoDTO goodsSkuInfoDTO) {
                return JsonResult.error("调用goods服务，查询sku信息异常：" + cause.getMessage()).toString();
            }
        };
    }
}
