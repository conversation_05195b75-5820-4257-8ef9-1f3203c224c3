package com.ets.delivery.application.common.consts.sendback;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.IntStream;
import java.util.LinkedHashMap;

@Getter
@AllArgsConstructor
public enum SendBackStatusEnum {

    STATUS_NORMAL(1, "正常"),
    STATUS_CANCEL(2, "取消"),
    STATUS_STOP(3, "暂停");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        SendBackStatusEnum[] enums = SendBackStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
