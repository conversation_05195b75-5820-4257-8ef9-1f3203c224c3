package com.ets.delivery.application.common.vo.aftersalesreivews;

import com.ets.delivery.application.common.vo.MultiSelectOptionVO;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AdminAfterSalesReviewsSelectOptionVO {
    // 业务类型
    private List<Map<String, String>> orderTypeList;
    // 发卡方
    private List<Map<String, String>> issuerIdList;
    // 审核状态
    private List<Map<String, String>> reviewStatusList;
    // 驳回原因（二级联动）
    private List<MultiSelectOptionVO> rejectReasonList;
    // 自动审核
    private List<Map<String, String>> autoAuditList;
}
