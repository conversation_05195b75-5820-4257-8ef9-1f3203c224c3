package com.ets.delivery.application.common.dto.stock;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class StockInEditDTO {

    @NotBlank(message = "入库单号不能为空")
    private String stockInSn;

    /**
     * 备注
     */
    private String remark;

    /**
     * 图片，逗号分隔
     */
    private String images;

    private @Valid List<GoodsEditInfo> goodsEditInfo;

    @Data
    public static class GoodsEditInfo {

        @NotNull
        private Integer id;

        @Valid
        private List<StockGoodsNumberDTO> numberInfoList;
    }

}
