package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 地址仓储映射配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_address_storage_map_config")
public class AddressStorageMapConfig extends BaseEntity<AddressStorageMapConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 业务类型[1-申办]
     */
    private Integer bizType;

    /**
     * 默认仓储代号
     */
    private String defaultStorageCode;
    /**
     * 物流公司代号
     */
    private String logisticsCode;
    /**
     * 配置描述
     */
    private String configDesc;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 状态[-1-删除 1-生效 2-未生效]
     */
    private Integer status;

    /**
     * 是否默认规则
     */
    private Integer isDefault;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
