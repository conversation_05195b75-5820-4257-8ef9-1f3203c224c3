package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.KdFeign;
import org.springframework.cloud.openfeign.FallbackFactory;

import org.springframework.stereotype.Component;

@Component
public class KdFallbackFactory implements FallbackFactory<KdFeign> {
    @Override
    public KdFeign create(Throwable throwable) {
        return new KdFeign() {
            @Override
            public String autoNumber(String num, String key) {
                return JsonResult.error("请求Kd100获取物流编码接口失败：" + throwable.getMessage()).toString();
            }
        };
    }
}
