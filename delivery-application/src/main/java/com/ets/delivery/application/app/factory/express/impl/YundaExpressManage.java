package com.ets.delivery.application.app.factory.express.impl;

import cn.hutool.core.convert.ConverterRegistry;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.express.ExpressFactory;
import com.ets.delivery.application.app.thirdservice.request.yunda.YundaExpressNotifyDTO;
import com.ets.delivery.application.common.bo.express.ExpressDataBO;
import com.ets.delivery.application.common.bo.express.ExpressLogBO;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsExpressStatusEnum;
import com.ets.delivery.application.common.consts.yunda.YundaExpressOrderStatusEnum;
import com.ets.delivery.application.common.consts.yunda.YundaModeTypeEnum;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.*;
import com.ets.redisson.template.DistributedLockTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class YundaExpressManage extends ExpressBase {

    @Autowired
    private ExpressService expressService;

    @Autowired
    private ExpressLogService expressLogService;

    @Autowired
    private ExWarehouseService exWarehouseService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private ErpOrderService erpOrderService;

    @Autowired
    private StockOutOrderService stockOutOrderService;

    @Autowired
    private DistributedLockTemplate distributedLockTemplate;

    @Override
    public Express expressNotify(ExpressNotifyDTO notifyDTO) {
        YundaExpressNotifyDTO yundaExpressNotifyDTO = ConverterRegistry.getInstance().convert(
                YundaExpressNotifyDTO.class, notifyDTO.getNotifyData());

        if (ObjectUtil.isNull(yundaExpressNotifyDTO)) {
            ToolsHelper.throwException("物流轨迹推送数据格式不正确");
        }

        String lockKey = "delivery:express:yunda:" + yundaExpressNotifyDTO.getExpressCode();
        return distributedLockTemplate.tryLock(lockKey, 10, 20, TimeUnit.SECONDS, () ->{

            if (ObjectUtils.isNotNull(yundaExpressNotifyDTO.getSourceOrder())) {
                // 区分不同类型订单(业务发货单、出库单、erp发货单)
                switch (yundaExpressNotifyDTO.getSourceOrder().intValue()) {
                    case 11:
                        // 系统发货单
                        if (yundaExpressNotifyDTO.getModeType().equals(YundaModeTypeEnum.B2B.getValue())) {
                            updateStockOutOrder(yundaExpressNotifyDTO);
                        } else if (yundaExpressNotifyDTO.getModeType().equals(YundaModeTypeEnum.B2C.getValue())) {
                            updateLogisticsOrder(yundaExpressNotifyDTO);
                        }
                        break;
                    case 0:
                        // erp订单
                        updateErpOrder(yundaExpressNotifyDTO);
                        break;
                    default:

                }
            } else {
                // 兼容没有sourceOrder的情况
                updateLogisticsOrder(yundaExpressNotifyDTO);
            }

            // 更新物流信息
            return updateExpress(yundaExpressNotifyDTO);
        });
    }

    @Override
    public Express expressQuery(Express express) {
        express.setExpressCompany("");

        // 韵达没有查询接口 使用快递100
        return ExpressFactory.create(ExpressCodeEnum.KD100.getValue()).expressQuery(express);
    }

    private void updateLogisticsOrder(YundaExpressNotifyDTO notifyDTO) {
        // 更新发货单记录
        updateLogistics(notifyDTO);

        // 更新出库记录
        updateExWarehouse(notifyDTO);
    }

    private void updateErpOrder(YundaExpressNotifyDTO notifyDTO) {
        // 通过erp流水号查发货单号
        ErpOrder erpOrder = erpOrderService.getOneByColumn(notifyDTO.getDeliveryOrderCode(), ErpOrder::getErpSn);
        if (ObjectUtils.isNull(erpOrder)) {
            ToolsHelper.throwException("ERP订单记录不存在");
        }
        notifyDTO.setDeliveryOrderCode(erpOrder.getLogisticsSn());

        // 更新发货单记录
        updateLogistics(notifyDTO);

        // 更新出库记录
        updateExWarehouse(notifyDTO);
    }

    private void updateStockOutOrder(YundaExpressNotifyDTO notifyDTO) {
        // 通过出库单号
        StockOutOrder stockOutOrder = stockOutOrderService.getBySn(notifyDTO.getDeliveryOrderCode());
        if (ObjectUtils.isNull(stockOutOrder)) {
            ToolsHelper.throwException("出库单记录不存在");
        }
    }

    private void updateLogistics(YundaExpressNotifyDTO notifyDTO) {
        Logistics logistics = logisticsService.getByLogisticsSn(notifyDTO.getDeliveryOrderCode());
        if (ObjectUtils.isNull(logistics)) {
            ToolsHelper.throwException("发货单记录不存在");
        }
        // 已取消
        if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue())) {
            return;
        }
        String orderStatus = notifyDTO.getOmsOrderStatus();
        Integer expressStatus = YundaExpressOrderStatusEnum.statusMap.getOrDefault(orderStatus,
                LogisticsExpressStatusEnum.EXCEPTION.getValue());
        if (!logistics.getExpressStatus().equals(expressStatus)) {
            Logistics update = new Logistics();
            update.setId(logistics.getId());
            update.setExpressStatus(expressStatus);
            update.setUpdatedAt(LocalDateTime.now());
            logisticsService.updateById(update);
        }
    }

    private void updateExWarehouse(YundaExpressNotifyDTO notifyDTO) {
        ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(notifyDTO.getDeliveryOrderCode());
        if (ObjectUtil.isNull(exWarehouse)) {
            ToolsHelper.throwException(notifyDTO.getDeliveryOrderCode() + "出库记录不存在");
        }
        String orderStatus = notifyDTO.getOmsOrderStatus();
        if (!exWarehouse.getCurrentStatus().equals(10034) && !exWarehouse.getCurrentStatus().equals(10035)) {
            ExWarehouse update = new ExWarehouse();
            update.setId(exWarehouse.getId());
            update.setUpdatedAt(LocalDateTime.now());
            if (orderStatus.equals(YundaExpressOrderStatusEnum.SIGNED.getValue())) {
                // 已签收
                update.setCurrentStatus(10034);
            } else if (orderStatus.equals(YundaExpressOrderStatusEnum.DELIVER_EXCEPTION.getValue())) {
                // 签收失败
                update.setCurrentStatus(10035);
            }
            exWarehouseService.updateById(update);
        }
    }

    private Express updateExpress(YundaExpressNotifyDTO notifyDTO) {
        Express express = expressService.getOneByExpressNumber(notifyDTO.getExpressCode());
        if (ObjectUtils.isNotNull(express)) {
            // 非韵达轨迹不更新
            if (!express.getExpressCode().equals(ExpressCodeEnum.YUNDA.getValue())) {
                return express;
            }

            // 有历史记录数据
            List<ExpressDataBO> expressDataList = new ArrayList<>();
            if (StringUtils.isNotEmpty(express.getData())) {
                expressDataList = JSON.parseObject(express.getData(), new TypeReference<List<ExpressDataBO>>() {});
            }
            // 格式化数据
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            ExpressDataBO expressData = new ExpressDataBO();
            expressData.setFtime(notifyDTO.getOperateTime());
            expressData.setContext(notifyDTO.getDescription());
            expressData.setAreaName(notifyDTO.getBranchName());
            expressData.setStatus(notifyDTO.getOmsOrderStatus());
            expressDataList.add(0, expressData);
            expressDataList.sort((t1, t2) -> t2.getFtime().compareTo(t1.getFtime()));
            String dataList = JSON.toJSONString(expressDataList);

            Integer subscribeStatus = ExpressSubscribeStatusEnum.SUBSCRIBING.getValue();
            LocalDateTime receivedTime = null;

            // 退回、拒收状态 记录状态和时间
            Integer pushState = YundaExpressOrderStatusEnum.stateMap.getOrDefault(notifyDTO.getOmsOrderStatus(), ExpressStateEnum.ON_THE_WAY.getValue());
            if (Arrays.asList(ExpressStateEnum.SEND_BACK.getValue(), ExpressStateEnum.RECEIVER_REJECT.getValue()).contains(pushState)) {
                express.setIsBack(1);
                express.setBackTime(LocalDateTime.parse(notifyDTO.getOperateTime(), dtf));
            }

            // 轨迹接收可能不按时间顺序 需要取排序后的最新记录
            ExpressDataBO lastExpress = expressDataList.get(0);
            String orderStatus = lastExpress.getStatus();
            Integer state = YundaExpressOrderStatusEnum.stateMap.getOrDefault(orderStatus, ExpressStateEnum.ON_THE_WAY.getValue());
            if (orderStatus.equals(YundaExpressOrderStatusEnum.SIGNED.getValue())) {
                // 已签收
                subscribeStatus = ExpressSubscribeStatusEnum.SUBSCRIBE_SHUTDOWN.getValue();
                receivedTime = LocalDateTime.parse(lastExpress.getFtime(), dtf);
            }

            express.setExpressCompany(notifyDTO.getLogisticsCompanyName().toLowerCase());
            express.setState(state);
            express.setData(dataList);
            express.setSubscribeStatus(subscribeStatus);
            express.setReceivedTime(receivedTime);

            // 最早状态
            ExpressDataBO firstExpress = expressDataList.get(expressDataList.size() - 1);
            if (express.getFirstContext().isEmpty() || !express.getFirstContext().equals(firstExpress.getContext())) {
                express.setFirstArea(firstExpress.getAreaName());
                express.setFirstStatus(orderStatus);
                express.setFirstContext(firstExpress.getContext());
                express.setFirstExpressTime(LocalDateTime.parse(firstExpress.getFtime(), dtf));
            }

            // 最新状态
            if (!express.getLastContext().equals(lastExpress.getContext())) {
                express.setLastArea(lastExpress.getAreaName());
                express.setLastStatus(orderStatus);
                express.setLastContext(lastExpress.getContext());
                express.setLastExpressTime(LocalDateTime.parse(lastExpress.getFtime(), dtf));
            }

            express.setUpdatedAt(LocalDateTime.now());
            expressService.updateById(express);

            // 记录日志
            ExpressLogBO logBO = new ExpressLogBO();
            logBO.setExpressSn(express.getExpressSn());
            logBO.setExpressNumber(express.getExpressNumber());
            logBO.setSubscribeStatus(express.getSubscribeStatus());
            logBO.setContent("物流订阅回调修改:" + JSON.toJSONString(express));
            expressLogService.addLog(logBO);
        }
        return express;
    }
}
