package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.common.dto.manage.ManageStorageSkuMapDTO;
import com.ets.delivery.application.common.dto.manage.ManageStorageSkuMapEditDTO;
import com.ets.delivery.application.common.dto.manage.StorageMapEditBySkuDTO;
import com.ets.delivery.application.common.vo.SelectOptionsVO;
import com.ets.delivery.application.common.vo.manage.StorageSkuMapListVO;
import com.ets.delivery.application.infra.entity.StorageSkuMap;
import com.ets.delivery.application.infra.service.StorageService;
import com.ets.delivery.application.infra.service.StorageSkuMapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class ManageBusiness {
    @Autowired
    private StorageSkuMapService storageSkuMapService;

    @Autowired
    private StorageService storageService;
    /*
     * 获取仓储映射sku列表
     */
    public IPage<StorageSkuMapListVO> getStorageSkuMapList(ManageStorageSkuMapDTO listDTO) {
        // 搜索符合条件的后审单
        IPage<StorageSkuMap> defaultPage = storageSkuMapService.getDefaultPage(listDTO);
        List<StorageSkuMap> storageSkuMaps = defaultPage.getRecords();
        if (CollectionUtils.isEmpty(storageSkuMaps)) {
            return new Page<>();
        }
        return defaultPage.convert(v -> BeanUtil.copyProperties(v, StorageSkuMapListVO.class));
    }
    /*
     * 新增/修改 仓储映射sku
     */
    public Boolean editStorageSkuMapList(ManageStorageSkuMapEditDTO manageStorageSkuMapEditDTO){
        StorageSkuMap storageSkuMap = new StorageSkuMap();
        StorageSkuMap selectStorageSkuMap = storageSkuMapService.getOneByStorageCodeSku(manageStorageSkuMapEditDTO.getStorageCode(),manageStorageSkuMapEditDTO.getSku());
        if(manageStorageSkuMapEditDTO.getId().equals(0)){
            if(!ObjectUtil.isEmpty(selectStorageSkuMap)){
                ToolsHelper.throwException("数据已存在");
            }
            storageSkuMap.setCreatedAt(LocalDateTime.now());
        }else{
            if(!ObjectUtil.isEmpty(selectStorageSkuMap) && !manageStorageSkuMapEditDTO.getId().equals(selectStorageSkuMap.getId())){
                ToolsHelper.throwException("数据已存在");
            }
            storageSkuMap.setId(manageStorageSkuMapEditDTO.getId());
        }
        storageSkuMap.setSku(manageStorageSkuMapEditDTO.getSku());
        storageSkuMap.setStorageSku(manageStorageSkuMapEditDTO.getStorageSku());
        storageSkuMap.setStorageCode(manageStorageSkuMapEditDTO.getStorageCode());
        storageSkuMap.setGoodsName(manageStorageSkuMapEditDTO.getGoodsName());
        storageSkuMap.setStatus(manageStorageSkuMapEditDTO.getStatus());
        storageSkuMap.setAssignArea(manageStorageSkuMapEditDTO.getAssignArea());
        storageSkuMap.setUpdatedAt(LocalDateTime.now());

        storageSkuMapService.saveOrUpdate(storageSkuMap);
        return true;
    }

    public void editBySkuSn(StorageMapEditBySkuDTO dto) {

        StorageSkuMap storageSkuMap = storageSkuMapService.getBySkuSn(dto.getStorageCode(),dto.getSkuSn());

        boolean isCreate = storageSkuMap == null;
        if (isCreate) {
            storageSkuMap = new StorageSkuMap();
            storageSkuMap.setSku(dto.getSkuSn());
            storageSkuMap.setStorageCode(dto.getStorageCode());
        }
        storageSkuMap.setStorageSku(dto.getStorageSku());
        if (StringUtils.isNotEmpty(dto.getStorageSkuName())) {
            storageSkuMap.setGoodsName(dto.getStorageSkuName());
        }
        storageSkuMap.setStatus(dto.getMapStatus());

        if (isCreate) {
            storageSkuMapService.create(storageSkuMap);
        } else {
            storageSkuMap.setUpdatedAt(LocalDateTime.now());
            storageSkuMapService.updateById(storageSkuMap);
        }
    }

    public StorageSkuMap getStorageSkuMapById(Integer id){
        return storageSkuMapService.getById(id);
    }

    /*
     *  获取仓库下拉选择项
     */
    public Map<String, List<SelectOptionsVO>> getSelectOptions(){
        Map<String,List<SelectOptionsVO>> map = new HashMap<>();
        map.put("storage", storageService.getSelectOptions());
        return map;
    }
}
