package com.ets.delivery.application.app.disposer;

import cn.hutool.core.util.ObjectUtil;
import com.ets.common.BizException;
import com.ets.common.queue.BaseDisposer;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.factory.express.ExpressFactory;
import com.ets.delivery.application.common.bo.express.ExpressLogBO;
import com.ets.delivery.application.common.consts.ErrCodeConstant;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeStatusEnum;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeDTO;
import com.ets.delivery.application.infra.entity.Express;
import com.ets.delivery.application.infra.service.ExpressLogService;
import com.ets.delivery.application.infra.service.ExpressService;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@NoArgsConstructor
@Component(value = "ExpressSubscribeJobBean")
public class ExpressSubscribeDisposer extends BaseDisposer {

    @Autowired
    private ExpressBusiness expressBusiness;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private ExpressLogService expressLogService;

    public ExpressSubscribeDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "ExpressSubscribeJobBean";
    }

    @Override
    public void execute(Object content) {
        ExpressSubscribeDTO subscribeDTO = super.getParamsObject(content, ExpressSubscribeDTO.class);

        Express express = expressService.getOneByExpressNumber(subscribeDTO.getExpressNumber());
        if (ObjectUtil.isNull(express)) {
            return;
        }

        ExpressLogBO logBO = new ExpressLogBO();
        Express updateExpress = new Express();

        // 发起物流订阅
        try {
            ExpressFactory.create(subscribeDTO.getExpressCode()).subscribe(subscribeDTO);
            // 订阅成功
            updateExpress.setSubscribeStatus(ExpressSubscribeStatusEnum.SUBSCRIBED.getValue());
            logBO.setContent("订阅成功");
        } catch (BizException e) {
            // 订阅失败 快递单号错误
            if (e.getErrorCode().equals(ErrCodeConstant.CODE_EXPRESS_NUMBER_WRONG.getCode())) {
                // 通知业务方
                expressBusiness.sendExpressNumberWrong(subscribeDTO.getExpressNumber(), subscribeDTO.getOrderSn());
            }
            updateExpress.setSubscribeStatus(ExpressSubscribeStatusEnum.SUBSCRIBE_FAILED.getValue());
            updateExpress.setSubscribeFailedTime(express.getSubscribeFailedTime() + 1);
            logBO.setContent("订阅失败");
        } catch (Throwable e) {
            // 发起订阅失败
            updateExpress.setSubscribeStatus(ExpressSubscribeStatusEnum.SUBSCRIBE_FAILED.getValue());
            updateExpress.setSubscribeFailedTime(express.getSubscribeFailedTime() + 1);
            logBO.setContent("订阅失败");
        }
        updateExpress.setId(express.getId());
        expressService.updateById(updateExpress);

        // 记录日志
        logBO.setExpressSn(express.getExpressSn());
        logBO.setExpressNumber(express.getExpressNumber());
        logBO.setSubscribeStatus(updateExpress.getSubscribeStatus());
        expressLogService.addLog(logBO);
    }
}
