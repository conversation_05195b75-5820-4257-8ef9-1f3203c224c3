package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.consts.supplyGoods.GoodsStatusEnum;
import com.ets.delivery.application.common.dto.supplyGoods.SupplyGoodsListDTO;
import com.ets.delivery.application.common.vo.SelectOptionsVO;
import com.ets.delivery.application.infra.entity.SupplyGoods;
import com.ets.delivery.application.infra.mapper.SupplyGoodsMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 供应商货品列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Service
@DS("db-issuer-admin")
public class SupplyGoodsService extends BaseService<SupplyGoodsMapper, SupplyGoods> {

    public SupplyGoods getOneByGoodsCode(String goodsCode) {
        Wrapper<SupplyGoods> wrapper = Wrappers.<SupplyGoods>lambdaQuery()
                .eq(SupplyGoods::getGoodsCode, goodsCode)
                .eq(SupplyGoods::getStatus, 1)
                .orderByDesc(SupplyGoods::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public List<SupplyGoods> getListByGoodsCodeList(List<String> goodsCodeList) {
        Wrapper<SupplyGoods> wrapper = Wrappers.<SupplyGoods>lambdaQuery()
                .in(SupplyGoods::getGoodsCode, goodsCodeList)
                .eq(SupplyGoods::getStatus, 1)
                .orderByDesc(SupplyGoods::getCreatedAt);
        return this.baseMapper.selectList(wrapper);
    }

    public List<SupplyGoods> getGoodsUnionCodeList() {
        QueryWrapper<SupplyGoods> wrapper = new QueryWrapper<>();
        wrapper.select("distinct goods_union_code, goods_name").lambda()
                .eq(SupplyGoods::getStatus, 1)
                .ne(SupplyGoods::getGoodsUnionCode, "");
        return this.baseMapper.selectList(wrapper);
    }

    public List<SupplyGoods> getListByGoodsUnionCode(String goodsUnionCode) {
        Wrapper<SupplyGoods> wrapper = Wrappers.<SupplyGoods>lambdaQuery()
                .eq(SupplyGoods::getGoodsUnionCode, goodsUnionCode)
                .eq(SupplyGoods::getStatus, 1)
                .orderByDesc(SupplyGoods::getCreatedAt);
        return this.baseMapper.selectList(wrapper);
    }

    public List<SupplyGoods> getStorageGoodsList(String storageCode) {
        QueryWrapper<SupplyGoods> wrapper = new QueryWrapper<>();
        wrapper.select("distinct goods_union_code, goods_code, goods_name").lambda()
                .eq(SupplyGoods::getStorageCode, storageCode)
                .eq(SupplyGoods::getStatus, 1);
        return this.baseMapper.selectList(wrapper);
    }

    public IPage<SupplyGoods> getList(SupplyGoodsListDTO listDTO) {
        Wrapper<SupplyGoods> wrapper = Wrappers.<SupplyGoods>lambdaQuery()
                .eq(StringUtils.isNotEmpty(listDTO.getStorageCode()), SupplyGoods::getStorageCode, listDTO.getStorageCode())
                .eq(StringUtils.isNotEmpty(listDTO.getGoodsType()), SupplyGoods::getGoodsType, listDTO.getGoodsType())
                .eq(listDTO.getStatus() != null, SupplyGoods::getStatus, listDTO.getStatus())
                .orderByAsc(SupplyGoods::getStatus);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }

    public List<SupplyGoods> search(String goodsName, String storageCode) {

        QueryWrapper<SupplyGoods> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT goods_name,goods_code,goods_union_code")
                .like(StringUtils.isNotEmpty(goodsName), "goods_name", goodsName)
                .eq("storage_code", storageCode)
                .eq("status", GoodsStatusEnum.STATUS_NORMAL.getValue());

        return this.getBaseMapper().selectList(wrapper);
    }

    public List<SelectOptionsVO> getOptionList() {

        QueryWrapper<SupplyGoods> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT goods_name,goods_code")
                .eq("status", GoodsStatusEnum.STATUS_NORMAL.getValue());

        List<SupplyGoods> list = this.getBaseMapper().selectList(wrapper);

        List<SelectOptionsVO> options = new ArrayList<>();
        for (SupplyGoods supplyGoods : list) {
            SelectOptionsVO vo = new SelectOptionsVO(
                    supplyGoods.getGoodsCode(),
                    supplyGoods.getGoodsName()
            );

            options.add(vo);
        }

        return options;
    }
}
