package com.ets.delivery.application.app.thirdservice.response.jd;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class JdQueryEstimatedFreightsVO {

    /**
     * 响应状态描述
     */
    String statusMessage;

    /**
     * 响应状态标识（0：调用成功，1：业务异常，-1：系统异常）
     */
    Integer statusCode;

    /**
     * 运费信息列表
     */
    List<WaybillFreightsAndPredictTimeDTO> data;

    @Data
    public static class WaybillFreightsAndPredictTimeDTO {
        /**
         * 产品编码：P1-特惠送；P2-特准送
         */
        String productCode;

        /**
         * 预计送达时间
         */
        Date predictTime;

        /**
         * 预估标价运费，单位：元
         */

        String freight;
        /**
         * 寄托物重量，单位：KG
         */
        String weight;
    }
}
