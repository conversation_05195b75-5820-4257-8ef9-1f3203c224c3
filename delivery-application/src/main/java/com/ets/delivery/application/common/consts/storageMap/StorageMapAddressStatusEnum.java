package com.ets.delivery.application.common.consts.storageMap;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum StorageMapAddressStatusEnum {

    STATUS_DELETE(-1, "删除"),
    STATUS_VALID(1, "生效"),
    STATUS_INVALID(2, "未生效");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(StorageMapAddressStatusEnum.values()).collect(Collectors.toMap(StorageMapAddressStatusEnum::getValue, StorageMapAddressStatusEnum::getDesc));
    }
}
