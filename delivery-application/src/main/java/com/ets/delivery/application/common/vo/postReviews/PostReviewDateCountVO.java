package com.ets.delivery.application.common.vo.postReviews;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class PostReviewDateCountVO {
    /**
     * 后审生成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate date;

    /**
     * 优先级
     */
    private Integer emergencyType;

    /**
     * 条数
     */
    private Integer count;
}
