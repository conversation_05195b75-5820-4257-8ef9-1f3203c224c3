package com.ets.delivery.application.common.dto.setting;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class SettingEditDTO {

    /**
     * 主键ID
     */
    @NotNull(message = "id不能为空")
    private Integer id;

    /**
     * 分类
     */
    @NotBlank(message = "category不能为空")
    private String category;

    /**
     * 键值key
     */
    @NotBlank(message = "key不能为空")
    private String key;

    /**
     * 渠道
     */
    @NotBlank(message = "value不能为空")
    private String value;

    /**
     * 操作参数
     */
    private String params = "";

    /**
     * 引导
     */
    private String guide = "default";

    /**
     * 排序
     */
    private Integer sort = 0;
}
