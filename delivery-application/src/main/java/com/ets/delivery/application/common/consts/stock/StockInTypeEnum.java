package com.ets.delivery.application.common.consts.stock;

import com.ets.delivery.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum StockInTypeEnum {

    // 韵达业务类型 (SCRK=生产入库，LYRK=领用入库，CCRK=残次品入库，CGRK=采购入库，DBRK=调拨入库, QTRK=其他入库，B2BRK=B2B入库

    CGRK(1, "CGRK", "采购入库"),

    DBRK(2, "DBRK", "调拨入库"),

    LYRK(3, "LYRK", "换新入库"),

    CCRK(4, "CCRK", "次品入库"),

    QTRK(5, "QTRK", "退货入库"),

    XNRK(6, "QTRK", "虚拟入库");

    private final Integer code;
    private final String yunDaCode;
    private final String description;

    public static String getDescByCode(int code) {

        for (StockInTypeEnum node : StockInTypeEnum.values()) {

            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static StockInTypeEnum getByCode(int code) {

        for (StockInTypeEnum node : StockInTypeEnum.values()) {

            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }

    public static StockInTypeEnum getByYunDaCode(String yunDaCode) {

        for (StockInTypeEnum node : StockInTypeEnum.values()) {

            if (node.getYunDaCode().equals(yunDaCode)) {
                return node;
            }
        }

        return null;
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        List<SelectOptionsVO> selectOptionsVOList = new ArrayList<>();
        for (StockInTypeEnum node : StockInTypeEnum.values()) {
            selectOptionsVOList.add(new SelectOptionsVO(node.getCode().toString(),node.getDescription()));
        }

        return selectOptionsVOList;
    }

}
