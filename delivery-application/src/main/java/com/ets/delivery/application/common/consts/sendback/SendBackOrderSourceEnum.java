package com.ets.delivery.application.common.consts.sendback;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum SendBackOrderSourceEnum {

    APPLY("apply", "申办"),
    AFTER_SALES("after_sales", "售后"),
    JAVA("java", "java"),
    RE_DELIVERY("re_delivery", "非标"),
    MANUAL("manual", "手动下单"),
    GOODS("goods", "商品");


    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        map = Arrays.stream(SendBackOrderSourceEnum.values()).collect(Collectors.toMap(SendBackOrderSourceEnum::getValue, SendBackOrderSourceEnum::getDesc));
    }
}
