package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.fallback.DeviceExchangeFallbackFactory;
import com.ets.delivery.application.app.thirdservice.request.PickUpCallBackRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        url = "${microUrls.saleafter:http://etc-saleafter-main:6610}",
        name = "SaleafterDeviceExchangeFeign",
        contextId = "device-exchange",
        path = "/saleafter/device-exchange",
        fallbackFactory = DeviceExchangeFallbackFactory.class
)
public interface SaleafterDeviceExchangeFeign {
    @PostMapping(value = "/call-back/pick-up")
    JsonResult<?> pickUpCallBack(@RequestBody PickUpCallBackRequest request);
}