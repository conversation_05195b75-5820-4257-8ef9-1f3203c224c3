package com.ets.delivery.application.app.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsExpressStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsStatusEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.Express;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.service.ExWarehouseService;
import com.ets.delivery.application.infra.service.ExpressService;
import com.ets.delivery.application.infra.service.LogisticsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class LogisticsQueryJob {

    @Autowired
    private ExpressBusiness expressBusiness;

    @Autowired
    private ExWarehouseService exWarehouseService;

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private ExpressService expressService;

    @XxlJob("LogisticsQueryHandler")
    public ReturnT<String> logisticsQueryHandler(String params) {

        List<ExWarehouse> exWarehouseList = exWarehouseService.getNeedQueryDeliveryList(params, 30);
        log.info("【{}】【发货单查询】数量：{}", params, exWarehouseList.size());

        exWarehouseList.forEach(exWarehouse -> {
            try {
                // 出库单详情查询
                logisticsBusiness.orderQuery(exWarehouse);

                // 物流轨迹查询
                expressBusiness.expressQuery(exWarehouse);
            } catch (Exception e) {
                log.error("【{}】【发货单查询】错误：{}", params, e.getMessage());
            }
        });

        return ReturnT.SUCCESS;
    }

    @XxlJob("yundaLogisticsExpressQueryHandler")
    public ReturnT<String> yundaLogisticsExpressQueryHandler(String params) {

        List<Logistics> logisticsList = logisticsService.getOverTimeNotSignList();
        log.info("【韵达】【发货单查询】数量：{}", logisticsList.size());

        logisticsList.forEach(logistics -> {
            try {
                ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(logistics.getLogisticsSn());
                if (ObjectUtils.isNotEmpty(exWarehouse)) {
                    expressBusiness.expressQuery(exWarehouse);
                }
            } catch (Throwable e) {
                log.error("【韵达】【发货单查询】错误：{}", e.getMessage());
            }
        });

        return ReturnT.SUCCESS;
    }

    @XxlJob("fixLogisticsExpressStatusHandler")
    public ReturnT<String> fixLogisticsExpressStatusHandler(String params) {
        LambdaQueryWrapper<Logistics> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Logistics::getDeliveryStatus, LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue())
                .eq(Logistics::getStatus, LogisticsStatusEnum.STATUS_NORMAL.getValue())
                .eq(Logistics::getStorageCode, StorageCodeEnum.YUNDA.getValue())
                .eq(Logistics::getExpressStatus, LogisticsExpressStatusEnum.EXCEPTION.getValue())
                .last("limit " + params);
        List<Logistics> logisticsList = logisticsService.getListByWrapper(wrapper);
        log.info("【韵达】【发货单查询】快递状态运输异常数量：{}", logisticsList.size());

        if (ObjectUtils.isNotEmpty(logisticsList)) {
            logisticsList.forEach(logistics -> {
                Express express = expressService.getOneByExpressNumber(logistics.getExpressNumber());
                if (express.getState().equals(ExpressStateEnum.RECEIVED.getValue())) {
                    logistics.setExpressStatus(LogisticsExpressStatusEnum.SIGNED.getValue());
                    logisticsService.updateById(logistics);
                }
            });
        }

        return ReturnT.SUCCESS;
    }
}
