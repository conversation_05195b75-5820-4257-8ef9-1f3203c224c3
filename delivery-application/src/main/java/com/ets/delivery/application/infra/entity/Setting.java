package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 配置信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_setting")
public class Setting extends BaseEntity<Setting> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型[0-通用 1-客车 2-货车]
     */
    private Integer type;

    /**
     * 对接方id
     */
    private Integer issuerId;

    /**
     * 大类别
     */
    private String category;

    /**
     * 键值key
     */
    @TableField(value = "`key`")
    private String key;

    /**
     * 渠道
     */
    private String value;

    /**
     * 操作参数
     */
    private String params;

    /**
     * 引导
     */
    private String guide;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 状态：1正常2取消
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    private Integer sort;


}
