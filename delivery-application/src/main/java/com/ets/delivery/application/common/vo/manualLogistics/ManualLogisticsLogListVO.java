package com.ets.delivery.application.common.vo.manualLogistics;

import com.ets.delivery.application.common.consts.logistics.LogisticsOperateTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ManualLogisticsLogListVO {
    private Integer id;

    /**
     * 物流单id
     */
    private Integer logisticsId;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作类型:draw领取，delivery发货
     */
    private String type;

    private String typeStr;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    public String getTypeStr() {
        return LogisticsOperateTypeEnum.map.getOrDefault(type, "未知");
    }
}
