package com.ets.delivery.application.infra.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.infra.entity.Logistics;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 发货单列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
public interface LogisticsMapper extends CommonBaseMapper<Logistics> {

    @Select("select l.* from etc_logistics l left join etc_logistics_sku ls on l.logistics_sn = ls.logistics_sn  ${ew.customSqlSegment}")
    IPage<Logistics> getLogisticJoinSkuPage(Page page, @Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("select l.* from etc_logistics l  ${ew.customSqlSegment}")
    IPage<Logistics> getLogisticPage(Page page, @Param(Constants.WRAPPER) Wrapper wrapper);
}
