package com.ets.delivery.application.app.factory.task.impl;

import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.common.consts.ErrCodeConstant;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOperateTypeEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsStatusEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.delivery.application.common.dto.logistics.LogisticsAddOrderDTO;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.entity.LogisticsSku;
import com.ets.delivery.application.infra.entity.StorageSkuMap;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.LogisticsLogService;
import com.ets.delivery.application.infra.service.LogisticsService;
import com.ets.delivery.application.infra.service.LogisticsSkuService;
import com.ets.delivery.application.infra.service.StorageSkuMapService;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TaskLogisticsDeliverGoods extends TaskBase {
    @Autowired
    private LogisticsService logisticsService;
    @Autowired
    private LogisticsBusiness logisticsBusiness;
    @Autowired
    private LogisticsSkuService logisticsSkuService;

    @Autowired
    private StorageSkuMapService storageSkuMapService;


    @Autowired
    private LogisticsLogService logisticsLogService;

    @Qualifier("redisPermanentTemplate")
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void childExec(TaskRecord currentTaskRecord){
        Logistics logistics = null;
        
        try{
            //判断发货单是否正常状态
            logistics = logisticsService.getByLogisticsSn(currentTaskRecord.getReferSn());
            if(logistics == null){
                String msg = "发货单"+  currentTaskRecord.getReferSn()+"订单状态不正常不进行出库下单";
                ToolsHelper.throwException(msg, TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
            }
            if(
                ! logistics.getStatus().equals(LogisticsStatusEnum.STATUS_NORMAL.getValue()) ||
                !Arrays.asList(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),
                        LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue()).contains(logistics.getDeliveryStatus())
            ){
                String msg = "发货单"+  currentTaskRecord.getReferSn()+"订单状态不正常不进行出库下单";
                //添加日志
                logisticsLogService.addLog(logistics.getId(), LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),msg,"system");
                ToolsHelper.throwException(msg, TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
            }
            //加锁
            if (! ToolsHelper.addLock(redisTemplate, "TaskRecord:"+logistics.getLogisticsSn(), 20)) {
                ToolsHelper.throwException("发货单处理中，请稍后！");
            }

            // 待发货状态改成发货中
            if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue())) {
                Logistics update = new Logistics();
                update.setId(logistics.getId());
                update.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue());
                update.setPushTime(LocalDateTime.now());
                update.setUpdatedAt(LocalDateTime.now());
                logisticsService.updateById(update);
            }

            //构造goodsList
            List<LogisticsSku> skulist = logisticsSkuService.getListByLogisticsSn(logistics.getLogisticsSn());
            List<String> skuArr = skulist.stream().map(LogisticsSku::getSku).collect(Collectors.toList());

            List<StorageSkuMap> storageSkuMapList = storageSkuMapService.getListByStorageCodeSku(logistics.getStorageCode(),skuArr);
            Map<String,String> storageSkuMap = storageSkuMapList.stream().collect(Collectors.toMap(StorageSkuMap::getSku,StorageSkuMap::getStorageSku));
            List<LogisticsAddOrderDTO.Goods> goodsList = new ArrayList<>();
            skulist.forEach(v ->{
                LogisticsAddOrderDTO.Goods goods =  new LogisticsAddOrderDTO.Goods();
                goods.setGoodsCode(storageSkuMap.get(v.getSku()));
                goods.setQuantity(v.getNums());
                goodsList.add(goods);
            });

            //发货操作
            String[] sendAreaStr = logistics.getSendArea().split(" ");
            LogisticsAddOrderDTO logisticsAddOrderDTO = new LogisticsAddOrderDTO();
            logisticsAddOrderDTO.setLogisticsSn(logistics.getLogisticsSn());
            logisticsAddOrderDTO.setLogisticsCode(logistics.getLogisticsCode());
            logisticsAddOrderDTO.setProvince(sendAreaStr[0]);
            logisticsAddOrderDTO.setCity(sendAreaStr[1]);
            logisticsAddOrderDTO.setArea(sendAreaStr[2]);
            logisticsAddOrderDTO.setGoodsList(goodsList);
            logisticsAddOrderDTO.setDetailAddress(logistics.getSendAddress());
            logisticsAddOrderDTO.setMobile(logistics.getSendPhone());
            logisticsAddOrderDTO.setName(logistics.getSendName());
            logisticsAddOrderDTO.setStorageCode(logistics.getStorageCode());
            logisticsBusiness.deliverOrder(logisticsAddOrderDTO);

        }catch (BizException e) {
            if (e.getErrorCode().equals(ErrCodeConstant.CODE_DELIVERY_STOCK_OUT.getCode())) {
                // 缺货记录
                if (ObjectUtils.isNotEmpty(logistics) && !logistics.getDeliveryRemark().equals("仓库缺货")) {
                    Logistics update = new Logistics();
                    update.setId(logistics.getId());
                    update.setDeliveryRemark("仓库缺货");
                    update.setUpdatedAt(LocalDateTime.now());
                    logisticsService.updateById(update);

                    //添加日志
                    logisticsLogService.addLog(logistics.getId(), LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(), e.getErrorMsg(),"system");
                }
            }
            //原路输出
            ToolsHelper.throwException(e.getMessage(), e.getErrorCode());
        }catch(Exception e1){
            ToolsHelper.throwException("系统错误"+e1.getMessage());
        }

    }
}
