package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.app.disposer.PickUpSmsNotifyDisposer;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.app.thirdservice.business.JdBusiness;
import com.ets.delivery.application.app.thirdservice.request.jd.*;
import com.ets.delivery.application.app.thirdservice.response.jd.JdPickUpOrderCreateVO;
import com.ets.delivery.application.app.thirdservice.response.jd.JdQueryEstimatedFreightsVO;
import com.ets.delivery.application.app.thirdservice.response.jd.JdQueryTraceInfoVO;
import com.ets.delivery.application.app.thirdservice.response.jd.JdStandardCalendarVO;
import com.ets.delivery.application.common.bo.PickUpLogBO;
import com.ets.delivery.application.common.bo.PickUpSmsBO;
import com.ets.delivery.application.common.bo.SendBackLogBO;
import com.ets.delivery.application.common.bo.express.ExpressBO;
import com.ets.delivery.application.common.bo.express.ExpressDataBO;
import com.ets.delivery.application.common.bo.express.ExpressLogBO;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.feign.request.pickup.PickupGoodsBO;
import com.ets.delivery.application.common.bo.sendback.LogisticsSendBackCreateBO;
import com.ets.delivery.application.common.bo.task.TaskPickUpNotifyContentBO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.config.jd.JdConfig;
import com.ets.delivery.application.common.config.queue.express.QueueExpress;
import com.ets.delivery.application.common.consts.DeviceTypeEnum;
import com.ets.delivery.application.common.consts.sendback.SendBackLogTypeEnum;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeStatusEnum;
import com.ets.delivery.application.common.consts.jd.JdTradeNodeExpressEnum;
import com.ets.delivery.application.common.consts.pickUp.*;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.dto.pickUp.*;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.common.vo.pickUp.*;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.*;
import com.ets.delivery.feign.request.pickup.PickUpBookingInfoDTO;
import com.ets.delivery.feign.request.pickup.PickUpCancelDTO;
import com.ets.delivery.feign.request.pickup.PickUpCreateDTO;
import com.ets.delivery.feign.response.pickup.PickUpBookingInfoVO;
import com.ets.delivery.feign.response.pickup.PickUpCreateVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class PickUpBusiness {

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private JdConfig jdConfig;

    @Autowired
    private DeliveryConfig deliveryConfig;

    @Autowired
    private JdBusiness jdBusiness;

    @Autowired
    private PickupService pickupService;

    @Autowired
    private PickupDetailService pickupDetailService;

    @Autowired
    private PickupLogService pickupLogService;

    @Autowired
    private TaskRecordService taskRecordService;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private ExpressLogService expressLogService;

    @Autowired
    private ExpressBusiness expressBusiness;

    @Autowired
    private SendBackBusiness sendBackBusiness;

    @Autowired
    private LogisticsSendbackLogService sendBackLogService;

    @Autowired
    private QueueExpress queueExpress;

    public PickUpBookingInfoVO getBookingInfo(PickUpBookingInfoDTO pickUpBookingInfoDTO) {
        try {
            // 超区校验
            JdCheckBlinkAreaDTO checkBlinkAreaDTO = new JdCheckBlinkAreaDTO();
            checkBlinkAreaDTO.setSenderProvince(pickUpBookingInfoDTO.getProvince())
                    .setSenderCity(pickUpBookingInfoDTO.getCity())
                    .setSenderDistrict(pickUpBookingInfoDTO.getDistrict())
                    .setSenderDetailAddress(pickUpBookingInfoDTO.getAddress())
                    .setReceiverProvince(jdConfig.getReceiverProvince())
                    .setReceiverCity(jdConfig.getReceiverCity())
                    .setReceiverDistrict(jdConfig.getReceiverDistrict())
                    .setReceiverDetailAddress(jdConfig.getReceiverDetailAddress());
            jdBusiness.checkBlinkArea(checkBlinkAreaDTO);

            // 获取预约日历
            String today = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDateTime.now());
            JdStandardCalendarDTO standardCalendarDTO = new JdStandardCalendarDTO();
            standardCalendarDTO.setSenderProvince(pickUpBookingInfoDTO.getProvince())
                    .setSenderCity(pickUpBookingInfoDTO.getCity())
                    .setSenderDistrict(pickUpBookingInfoDTO.getDistrict())
                    .setSenderDetailAddress(pickUpBookingInfoDTO.getAddress())
                    .setReceiverProvince(jdConfig.getReceiverProvince())
                    .setReceiverCity(jdConfig.getReceiverCity())
                    .setReceiverDistrict(jdConfig.getReceiverDistrict())
                    .setReceiverDetailAddress(jdConfig.getReceiverDetailAddress())
                    .setQueryStartDate(today);
            JdStandardCalendarVO standardCalendarVO = jdBusiness.standardCalendar(standardCalendarDTO);

            // 获取取件费用
            AtomicReference<BigDecimal> fee = new AtomicReference<>(jdConfig.getDefaultFreight());
            try {
                JdQueryEstimatedFreightsDTO freightsDTO = new JdQueryEstimatedFreightsDTO();
                freightsDTO.setSenderAddress(pickUpBookingInfoDTO.getProvince() +
                                pickUpBookingInfoDTO.getCity() +
                                pickUpBookingInfoDTO.getDistrict() +
                                pickUpBookingInfoDTO.getAddress())
                        .setReceiverAddress(jdConfig.getReceiverProvince())
                        .setWeight(pickUpBookingInfoDTO.getWeight().toString());
                JdQueryEstimatedFreightsVO freightsVO = jdBusiness.queryFreights(freightsDTO);
                if (ObjectUtils.isNotEmpty(freightsVO)) {
                    freightsVO.getData().forEach(freights -> {
                        if (freights.getProductCode().equals("P1")) {
                            fee.set(new BigDecimal(freights.getFreight()));
                        }
                    });
                }
            } catch (Throwable e) {
                log.warn("获取预计运费失败" + e.getLocalizedMessage());
            }

            // 返回结果
            PickUpBookingInfoVO pickUpBookingInfoVO = new PickUpBookingInfoVO();
            List<PickUpBookingInfoVO.BookingCalendar> bookingCalendarList = new ArrayList<>();
            standardCalendarVO.getData().forEach(sliceTime -> {
                PickUpBookingInfoVO.BookingCalendar bookingCalendar = BeanUtil.copyProperties(sliceTime, PickUpBookingInfoVO.BookingCalendar.class);
                bookingCalendarList.add(bookingCalendar);
            });
            pickUpBookingInfoVO.setBookingCalendar(bookingCalendarList);
            pickUpBookingInfoVO.setPickUpFee(fee.get().setScale(2, RoundingMode.HALF_UP));
            pickUpBookingInfoVO.setStoragePhone(jdConfig.getReceiverPhone());
            return pickUpBookingInfoVO;
        } catch (Throwable e) {
            log.warn("获取上门取件预约信息失败：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("获取上门取件预约信息失败：" + e.getLocalizedMessage());
            return null;
        }
    }

    public PickUpOrderBookingInfoVO getBookingInfo(PickUpOrderBookingInfoDTO bookingInfoDTO) {
        try {
            String[] sendAreaArr = bookingInfoDTO.getSendArea().split(" ");
            String[] receiveAreaArr = bookingInfoDTO.getReceiveArea().split(" ");

            // 省市区格式不正确
            if (sendAreaArr.length != 3 || receiveAreaArr.length != 3) {
                ToolsHelper.throwException("省市区格式不正确");
            }

            // 超区校验
            JdCheckBlinkAreaDTO checkBlinkAreaDTO = new JdCheckBlinkAreaDTO();
            checkBlinkAreaDTO.setSenderProvince(sendAreaArr[0])
                    .setSenderCity(sendAreaArr[1])
                    .setSenderDistrict(sendAreaArr[2])
                    .setSenderDetailAddress(bookingInfoDTO.getSendAddress())
                    .setReceiverProvince(receiveAreaArr[0])
                    .setReceiverCity(receiveAreaArr[1])
                    .setReceiverDistrict(receiveAreaArr[2])
                    .setReceiverDetailAddress(bookingInfoDTO.getReceiveAddress());
            jdBusiness.checkBlinkArea(checkBlinkAreaDTO);

            // 获取预约日历
            String today = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDateTime.now());
            JdStandardCalendarDTO standardCalendarDTO = new JdStandardCalendarDTO();
            standardCalendarDTO.setSenderProvince(sendAreaArr[0])
                    .setSenderCity(sendAreaArr[1])
                    .setSenderDistrict(sendAreaArr[2])
                    .setSenderDetailAddress(bookingInfoDTO.getSendAddress())
                    .setReceiverProvince(receiveAreaArr[0])
                    .setReceiverCity(receiveAreaArr[1])
                    .setReceiverDistrict(receiveAreaArr[2])
                    .setReceiverDetailAddress(bookingInfoDTO.getReceiveAddress())
                    .setQueryStartDate(today);
            JdStandardCalendarVO standardCalendarVO = jdBusiness.standardCalendar(standardCalendarDTO);

            // 返回结果
            PickUpOrderBookingInfoVO bookingInfoVO = new PickUpOrderBookingInfoVO();
            List<PickUpOrderBookingInfoVO.BookingCalendar> bookingCalendarList = new ArrayList<>();
            standardCalendarVO.getData().forEach(sliceTime -> {
                PickUpOrderBookingInfoVO.BookingCalendar bookingCalendar = BeanUtil.copyProperties(sliceTime, PickUpOrderBookingInfoVO.BookingCalendar.class);
                bookingCalendarList.add(bookingCalendar);
            });
            bookingInfoVO.setBookingCalendar(bookingCalendarList);
            return bookingInfoVO;
        } catch (Throwable e) {
            log.warn("获取上门取件预约信息失败：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("获取上门取件预约信息失败：" + e.getLocalizedMessage());
            return null;
        }
    }

    public PickUpCreateVO createPickUpOrder(PickUpCreateDTO pickUpCreateDTO, String goodsSkuInfo) {
        String lockKey = "pickup:" + pickUpCreateDTO.getOrderType() + ":" + pickUpCreateDTO.getOrderSn();
        try {
            if (!ToolsHelper.addLock(redisPermanentTemplate, lockKey, 10)) {
                ToolsHelper.throwException("取件单创建中");
            }

            String pickupSn;
            // 创建取件单 已取消、取件妥投、取件终止都能创建新订单
            Pickup pickup = pickupService.getOneByOrderSn(pickUpCreateDTO.getOrderSn());
            if (ObjectUtils.isEmpty(pickup) ||
                    Arrays.asList(
                            PickUpStatusEnum.STATUS_PICKUP_TERMINAL.getValue(),
                            PickUpStatusEnum.STATUS_PICKUP_DELIVERED.getValue()
                    ).contains(pickup.getPickupStatus())
            ) {
                pickupSn = ToolsHelper.genNum(redisPermanentTemplate, "pickup", ACTIVE, 8);
                pickUpCreateDTO.setSendAddress(pickUpCreateDTO.getSendAddress().replaceAll("[\\s*\\u00A0]", ""));
                pickup = BeanUtil.copyProperties(pickUpCreateDTO, Pickup.class);
                pickup.setPickupSn(pickupSn);
                pickup.setPickupStartTime(pickUpCreateDTO.getPickUpStartTime());
                pickup.setPickupEndTime(pickUpCreateDTO.getPickUpEndTime());
                pickup.setReceiveName(jdConfig.getReceiverName());
                pickup.setReceiveMobile(jdConfig.getReceiverPhone());
                pickup.setReceiveArea(jdConfig.getReceiverProvince() + " " +
                        jdConfig.getReceiverCity() + " " +
                        jdConfig.getReceiverDistrict());
                pickup.setReceiveAddress(jdConfig.getReceiverDetailAddress());
                pickup.setOrderStatus(PickUpOrderStatusEnum.STATUS_NORMAL.getValue());
                pickup.setPickupStatus(PickUpStatusEnum.STATUS_DEFAULT.getValue());
                pickup.setOperator("admin");
                pickup.setCreatedAt(LocalDateTime.now());
                pickup.setUpdatedAt(LocalDateTime.now());
                pickupService.save(pickup);

                // 记录日志
                PickUpLogBO logBO = new PickUpLogBO();
                logBO.setPickupId(pickup.getId())
                        .setType(PickUpLogTypeEnum.ADD.getValue())
                        .setOperateContent("创建取件单，取件单订单号：" + pickupSn);
                pickupLogService.addLog(logBO);
            } else {
                ToolsHelper.throwException("取件单进行中，不能重复创建");
            }
            pickupSn = pickup.getPickupSn();

            // 取件单商品置为无效
            pickupDetailService.updateStatusByPickUpSn(pickupSn, PickUpDetailStatusEnum.STATUS_DELETE.getValue());

            // 创建取件单商品
            createPickUpDetail(pickupSn, pickUpCreateDTO.getGoodsList());

            // 请求下单
            createJdOrder(pickup, pickUpCreateDTO.getRemark(), "system");

            // 创建寄回件
            createSendBack(pickup, pickUpCreateDTO.getSendbackNotifyUrl(), pickUpCreateDTO.getIssuerId(), goodsSkuInfo);

            // 返回结果
            PickUpCreateVO pickUpCreateVO = new PickUpCreateVO();
            pickUpCreateVO.setPickupSn(pickupSn);
            return pickUpCreateVO;
        } catch (Throwable e) {
            log.error("创建取件单失败：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("创建取件单失败：" + e.getLocalizedMessage());
            return null;
        } finally {
            ToolsHelper.unLock(redisPermanentTemplate, lockKey);
        }
    }

    protected void createPickUpDetail(String pickupSn, List<PickupGoodsBO> goodsList) {
        // 创建取件单商品
        goodsList.forEach(goods -> {
            PickupDetail pickupDetail = pickupDetailService.getOneByPickUpSnAndGoodsCode(pickupSn, goods.getGoodsCode());
            if (ObjectUtils.isEmpty(pickupDetail)) {
                pickupDetail = new PickupDetail();
                pickupDetail.setPickupSn(pickupSn);
                pickupDetail.setGoodsCode(goods.getGoodsCode());
                pickupDetail.setGoodsName(goods.getGoodsName());
                pickupDetail.setQuantity(goods.getQuantity());
                pickupDetail.setStatus(PickUpDetailStatusEnum.STATUS_NORMAL.getValue());
                pickupDetail.setCreatedAt(LocalDateTime.now());
                pickupDetail.setUpdatedAt(LocalDateTime.now());
                pickupDetailService.save(pickupDetail);
            } else {
                PickupDetail updateDetail = new PickupDetail();
                updateDetail.setId(pickupDetail.getId());
                updateDetail.setGoodsName(goods.getGoodsName());
                updateDetail.setQuantity(goods.getQuantity());
                pickupDetail.setStatus(PickUpDetailStatusEnum.STATUS_NORMAL.getValue());
                updateDetail.setUpdatedAt(LocalDateTime.now());
                pickupDetailService.updateById(updateDetail);
            }
        });
    }

    public void createPickUpOrder(PickUpOrderCreateDTO createDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        String lockKey = "pickup:" + createDTO.getOrderType() + ":" + createDTO.getOrderSn();
        try {
            if (!ToolsHelper.addLock(redisPermanentTemplate, lockKey, 10)) {
                ToolsHelper.throwException("取件单创建中");
            }

            // 创建取件单 已取消、取件妥投、取件终止都能创建新订单
            String pickupSn;
            AtomicReference<String> remark = new AtomicReference<>("取件货物为ETC卡和ETC设备，不可缺少，需要验货");
            Pickup pickup = pickupService.getOneByOrderSn(createDTO.getOrderSn());
            if (ObjectUtils.isEmpty(pickup) ||
                    Arrays.asList(
                            PickUpStatusEnum.STATUS_PICKUP_TERMINAL.getValue(),
                            PickUpStatusEnum.STATUS_PICKUP_DELIVERED.getValue()
                    ).contains(pickup.getPickupStatus())
            ) {
                pickupSn = ToolsHelper.genNum(redisPermanentTemplate, "pickup", ACTIVE, 8);
                createDTO.setSendAddress(createDTO.getSendAddress().replaceAll("[\\s*\\u00A0]", ""));
                pickup = BeanUtil.copyProperties(createDTO, Pickup.class);
                pickup.setSendMobile(createDTO.getSendMobile().replaceAll("[\\s*\\u00A0]", ""));
                pickup.setPickupSn(pickupSn);
                pickup.setPickupStartTime(createDTO.getPickUpStartTime());
                pickup.setPickupEndTime(createDTO.getPickUpEndTime());
                pickup.setOrderStatus(PickUpOrderStatusEnum.STATUS_NORMAL.getValue());
                pickup.setPickupStatus(PickUpStatusEnum.STATUS_DEFAULT.getValue());
                pickup.setOperator(user.getUsername());
                pickup.setSmsStatus(PickUpSmsStatusEnum.WAIT_SEND.getValue());
                pickup.setCreatedAt(LocalDateTime.now());
                pickup.setUpdatedAt(LocalDateTime.now());
                pickupService.save(pickup);

                // 记录日志
                PickUpLogBO logBO = new PickUpLogBO();
                logBO.setPickupId(pickup.getId())
                        .setType(PickUpLogTypeEnum.ADD.getValue())
                        .setOperator(user.getUsername())
                        .setOperateContent("后台创建取件单，取件单订单号：" + pickupSn);
                pickupLogService.addLog(logBO);
            } else {
                ToolsHelper.throwException("取件单进行中，不能重复创建");
            }
            pickupSn = pickup.getPickupSn();

            // 取件单商品置为无效
            pickupDetailService.updateStatusByPickUpSn(pickupSn, PickUpDetailStatusEnum.STATUS_DELETE.getValue());

            // 创建取件单商品
            createPickUpDetail(pickupSn, createDTO.getGoodsList());

            createDTO.getGoodsList().forEach(goods -> {
                if (goods.getDeviceType().equals(DeviceTypeEnum.DEVICE_TYPE_MONOLITHIC.getValue())) {
                    remark.set("取件货物为ETC设备，不可缺少，需要验货");
                }
            });

            // 请求下单
            createJdOrder(pickup, remark.get(), user.getUsername());
        } catch (Throwable e) {
            log.error("后台创建取件单失败：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("后台创建取件单失败：" + e.getLocalizedMessage());
        } finally {
            ToolsHelper.unLock(redisPermanentTemplate, lockKey);
        }
    }

    public void cancelPickUpOrder(PickUpCancelDTO pickUpCancelDTO) {
        Pickup pickup = pickupService.getOneByPickUpSn(pickUpCancelDTO.getPickupSn());
        if (ObjectUtils.isEmpty(pickup)) {
            ToolsHelper.throwException("取件订单不存在");
        }

        if (pickup.getOrderStatus().equals(PickUpOrderStatusEnum.STATUS_CANCEL.getValue())) {
            return;
        }

        // 请求接口
        cancelJdOrder(pickup, "system");

        // 取消寄回件
        sendBackBusiness.cancelSendBack(pickup.getOrderSn());
    }

    public void cancelPickUpOrder(PickUpOrderCancelDTO pickUpCancelDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        Pickup pickup = pickupService.getOneByPickUpSn(pickUpCancelDTO.getPickupSn());
        if (ObjectUtils.isEmpty(pickup)) {
            ToolsHelper.throwException("取件订单不存在");
        }

        if (pickup.getOrderStatus().equals(PickUpOrderStatusEnum.STATUS_CANCEL.getValue())) {
            return;
        }

        if (!pickup.getOrderSource().equals("admin")) {
            ToolsHelper.throwException("业务创建取件单不能后台取消");
        }

        // 请求接口
        cancelJdOrder(pickup, user.getUsername());
    }

    public void sendPickUpNotify(Pickup pickup) {
        // 已分配待取件、取件完成
        List<Integer> list = Arrays.asList(
                PickUpStatusEnum.STATUS_WAIT_PICKUP.getValue(),
                PickUpStatusEnum.STATUS_PICKUP_FINISH.getValue(),
                PickUpStatusEnum.STATUS_PICKUP_DELIVERED.getValue(),
                PickUpStatusEnum.STATUS_PICKUP_ERROR.getValue(),
                PickUpStatusEnum.STATUS_PICKUP_TERMINAL.getValue()
        );
        // 通知业务
        if (list.contains(pickup.getPickupStatus()) && ObjectUtils.isNotEmpty(pickup.getNotifyBackUrl())) {
            // 通知内容
            TaskPickUpNotifyContentBO contentBO = new TaskPickUpNotifyContentBO();
            contentBO.setOrderSn(pickup.getOrderSn());
            contentBO.setStatus(pickup.getPickupStatus());
            contentBO.setDesc(pickup.getPickupRemark());
            contentBO.setExpressCompany(pickup.getExpressCorp());
            contentBO.setExpressNumber(pickup.getExpressNumber());

            String content = JSON.toJSONString(contentBO);

            // 查询task_record
            TaskRecord taskRecord = taskRecordService.getOneByCondition(pickup.getPickupSn(),
                    TaskRecordReferTypeEnum.TASK_PICKUP_NOTIFY.getType(),
                    content
            );

            // 没有则创建
            if (ObjectUtils.isEmpty(taskRecord)) {
                // task参数
                TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
                taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_PICKUP_NOTIFY.getType());
                taskRecordDTO.setReferSn(pickup.getPickupSn());
                taskRecordDTO.setNotifyContent(content);

                TaskFactory.create(TaskRecordReferTypeEnum.TASK_PICKUP_NOTIFY).addAndPush(taskRecordDTO);
            }
        }

        // 后台创建的取件单 分配取件员 发送短信通知
        if (pickup.getOrderSource().equals("admin") &&
                pickup.getPickupStatus().equals(PickUpStatusEnum.STATUS_WAIT_PICKUP.getValue())) {
            PickUpSmsBO smsBO = new PickUpSmsBO();
            smsBO.setPickupId(pickup.getId());
            queueExpress.push(new PickUpSmsNotifyDisposer(smsBO));
        }
    }

    public IPage<PickUpListVO> getList(PickUpListDTO listDTO) {
        try {
            IPage<Pickup> page = pickupService.getPage(listDTO);
            return page.convert(pickup -> {
                PickUpListVO pickUpListVO = BeanUtil.copyProperties(pickup, PickUpListVO.class);
                pickUpListVO.setSendName(StrUtil.hide(pickup.getSendName(), 1, 2));
                pickUpListVO.setSendAddress("****");
                pickUpListVO.setSendMobile(DesensitizedUtil.mobilePhone(pickup.getSendMobile()));
                // 创建方式
                if (pickup.getOrderSource().equals("admin")) {
                    pickUpListVO.setCreateType(PickUpCreateTypeEnum.TYPE_ADMIN.getValue());
                }
                // 获取商品
                List<PickUpGoodsListVO> goodsList = new ArrayList<>();
                List<PickupDetail> detailList = pickupDetailService.getListByPickUpSn(pickup.getPickupSn());
                if (ObjectUtils.isNotEmpty(detailList)) {
                    detailList.forEach(detail -> {
                        PickUpGoodsListVO goodsListVO = BeanUtil.copyProperties(detail, PickUpGoodsListVO.class);
                        goodsList.add(goodsListVO);
                    });
                }
                pickUpListVO.setGoodsList(goodsList);
                return pickUpListVO;
            });
        } catch (Throwable e) {
            log.error("获取上门取件单列表异常：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("获取上门取件单列表异常，请稍后重试");
        }
        return null;
    }

    public PickUpDetailVO getDetail(PickUpDetailDTO detailDTO) {
        try {
            Pickup pickup = pickupService.getById(detailDTO.getPickupId());
            if (ObjectUtils.isEmpty(pickup)) {
                ToolsHelper.throwException("取件单不存在");
            }
            PickUpDetailVO detailVO = BeanUtil.copyProperties(pickup, PickUpDetailVO.class);
            // 创建方式
            if (pickup.getOrderSource().equals("admin")) {
                detailVO.setCreateType(PickUpCreateTypeEnum.TYPE_ADMIN.getValue());
            }
            // 获取商品
            List<PickUpGoodsListVO> goodsList = new ArrayList<>();
            List<PickupDetail> detailList = pickupDetailService.getListByPickUpSn(pickup.getPickupSn());
            if (ObjectUtils.isNotEmpty(detailList)) {
                detailList.forEach(detail -> {
                    PickUpGoodsListVO goodsListVO = BeanUtil.copyProperties(detail, PickUpGoodsListVO.class);
                    goodsList.add(goodsListVO);
                });
            }
            detailVO.setGoodsList(goodsList);
            return detailVO;
        } catch (Throwable e) {
            log.error("获取取件单详情异常：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("获取取件单详情异常");
        }
        return null;
    }

    public IPage<PickUpLogListVO> getLog(PickUpLogListDTO logListDTO) {
        return pickupLogService.getLogListById(logListDTO.getPickUpId(), logListDTO.getPageNum(), logListDTO.getPageSize())
                .convert(pickupLog -> BeanUtil.copyProperties(pickupLog, PickUpLogListVO.class));
    }

    private void createJdOrder(Pickup pickup, String remark, String username) {
        if (StringUtils.isEmpty(pickup.getPickupCode())) {
            List<JdPickUpOrderCreateDTO.ProductDetailDTO> productList = new ArrayList<>();
            List<PickupDetail> pickupDetailList = pickupDetailService.getListByPickUpSn(pickup.getPickupSn());
            pickupDetailList.forEach(goods -> {
                JdPickUpOrderCreateDTO.ProductDetailDTO productDetailDTO = new JdPickUpOrderCreateDTO.ProductDetailDTO();
                productDetailDTO.setProductName(goods.getGoodsName());
                productDetailDTO.setProductCount(goods.getQuantity());
                productList.add(productDetailDTO);
            });

            // 收件地址判断
            String defaultAddress = jdConfig.getReceiverProvince() +
                    jdConfig.getReceiverCity() +
                    jdConfig.getReceiverDistrict() +
                    jdConfig.getReceiverDetailAddress();
            String backAddress = StringUtils.isNotEmpty(pickup.getReceiveAddress()) ?
                    pickup.getReceiveArea() + pickup.getReceiveAddress() :
                    defaultAddress;

            JdPickUpOrderCreateDTO pickUpOrderCreateDTO = new JdPickUpOrderCreateDTO();
            pickUpOrderCreateDTO.setOrderId(pickup.getPickupSn())
                    .setPickupAddress(pickup.getSendArea().replaceAll("[\\s*\\u00A0]", "") + pickup.getSendAddress())
                    .setPickupName(pickup.getSendName())
                    .setPickupTel(pickup.getSendMobile())
                    .setGoodsDtoList(productList)
                    .setPickupStartTime(Date.from(pickup.getPickupStartTime().atZone(ZoneId.systemDefault()).toInstant()))
                    .setPickupEndTime(Date.from(pickup.getPickupEndTime().atZone(ZoneId.systemDefault()).toInstant()))
                    .setCustomerCode(jdConfig.getCustomerCode())
                    .setBackAddress(backAddress.replaceAll("[\\s*\\u00A0]", ""))
                    .setCustomerContract(jdConfig.getReceiverName())
                    .setCustomerTel(jdConfig.getReceiverPhone())
                    .setWeight(jdConfig.getWeight())
                    .setVolume(jdConfig.getVolume())
                    .setDesp(jdConfig.getDesp())
                    .setRemark(remark);
            JdPickUpOrderCreateVO pickUpOrderCreateVO = new JdPickUpOrderCreateVO();
            if (Arrays.asList("dev", "test", "test1").contains(ACTIVE)) {
                // 开发测试环境不请求下单
                pickUpOrderCreateVO.setPickUpCode("test");
            } else {
                try {
                    pickUpOrderCreateVO = jdBusiness.receivePickUpOrder(pickUpOrderCreateDTO);
                } catch (Throwable e) {
                    // 更新状态
                    Pickup update = new Pickup();
                    update.setId(pickup.getId());
                    update.setPickupStatus(PickUpStatusEnum.STATUS_CREATE_FAIL.getValue());
                    update.setUpdatedAt(LocalDateTime.now());
                    pickupService.updateById(update);

                    // 记录日志
                    PickUpLogBO logBO = new PickUpLogBO();
                    logBO.setPickupId(pickup.getId())
                            .setType(PickUpLogTypeEnum.UPDATE.getValue())
                            .setOperator(username)
                            .setOperateContent("下单失败：" + e.getLocalizedMessage());
                    pickupLogService.addLog(logBO);

                    ToolsHelper.throwException(e.getLocalizedMessage());
                }
            }
            // 更新状态
            Pickup update = new Pickup();
            update.setId(pickup.getId());
            update.setPickupCode(pickUpOrderCreateVO.getPickUpCode());
            update.setPickupStatus(PickUpStatusEnum.STATUS_CREATE_SUCCESS.getValue());
            update.setUpdatedAt(LocalDateTime.now());
            pickupService.updateById(update);

            // 记录日志
            PickUpLogBO logBO = new PickUpLogBO();
            logBO.setPickupId(pickup.getId())
                    .setType(PickUpLogTypeEnum.UPDATE.getValue())
                    .setOperator(username)
                    .setOperateContent("下单成功，取件单号：" + pickUpOrderCreateVO.getPickUpCode());
            pickupLogService.addLog(logBO);
        }
    }

    private void cancelJdOrder(Pickup pickup, String username) {
        try {
            if (StringUtils.isNotEmpty(pickup.getPickupCode())) {
                JdPickUpOrderCancelDTO cancelDTO = new JdPickUpOrderCancelDTO();
                cancelDTO.setPickupCode(pickup.getPickupCode())
                        .setCustomerCode(jdConfig.getCustomerCode());
                if (!Arrays.asList("dev", "test", "test1").contains(ACTIVE)) {
                    // 开发测试环境不用取消
                    jdBusiness.pickUpCancel(cancelDTO);
                }
            }

            // 更新状态取消
            Pickup update = new Pickup();
            update.setId(pickup.getId());
            update.setOrderStatus(PickUpOrderStatusEnum.STATUS_CANCEL.getValue());
            update.setPickupStatus(PickUpStatusEnum.STATUS_CANCEL.getValue());
            update.setUpdatedAt(LocalDateTime.now());
            pickupService.updateById(update);

            // 记录日志
            PickUpLogBO logBO = new PickUpLogBO();
            logBO.setPickupId(pickup.getId())
                    .setType(PickUpLogTypeEnum.CANCEL.getValue())
                    .setOperator(username)
                    .setOperateContent("取消取件订单");
            pickupLogService.addLog(logBO);
        } catch (Throwable e) {
            log.error("取消取件单失败：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("取消取件单失败：" + e.getLocalizedMessage());
        }
    }

    private void createSendBack(Pickup pickup, String sendBackNotifyUrl, Integer issuerId, String goodsSkuInfo) {
        List<PickupDetail> pickupDetailList = pickupDetailService.getListByPickUpSn(pickup.getPickupSn());

        // 创建寄回件
        LogisticsSendBack sendBack = sendBackBusiness.getUnfinishedOrder(pickup.getOrderSn());
        if (ObjectUtils.isEmpty(sendBack) && ObjectUtils.isNotEmpty(pickupDetailList)) {
            PickupDetail pickupDetail = pickupDetailList.get(0);

            LogisticsSendBackCreateBO createBO = new LogisticsSendBackCreateBO();
            createBO.setIssuerId(issuerId);
            createBO.setIssuerName(deliveryConfig.getNameByIssuerId(issuerId));
            createBO.setPlateNo(pickup.getPlateNo());
            createBO.setPlateColor(pickup.getPlateColor());
            createBO.setOrderSn(pickup.getOrderSn());
            createBO.setOrderType(pickup.getOrderType());
            createBO.setOrderSource(pickup.getOrderSource());
            createBO.setStorageCode(StorageCodeEnum.YUNDA.getValue());
            createBO.setGoodsCode(pickupDetail.getGoodsCode());
            createBO.setNums(pickupDetail.getQuantity());

            createBO.setSendName(pickup.getSendName());
            createBO.setSendPhone(pickup.getSendMobile());
            createBO.setSendArea(pickup.getSendArea());
            createBO.setSendAddress(pickup.getSendAddress());

            createBO.setReviceName(pickup.getReceiveName());
            createBO.setRevicePhone(pickup.getReceiveMobile());
            createBO.setReviceArea(pickup.getReceiveArea());
            createBO.setReviceAddress(pickup.getReceiveAddress());

            createBO.setExpressCorp(pickup.getExpressCorp());
            createBO.setExpressNumber(pickup.getExpressNumber());
            createBO.setNotifyBackUrl(sendBackNotifyUrl);

            createBO.setGoodsSkuInfo(goodsSkuInfo);

            LogisticsSendBack logisticsSendBack = sendBackBusiness.create(createBO);

            // 记录日志
            SendBackLogBO logBO = new SendBackLogBO();
            logBO.setSendbackId(logisticsSendBack.getId());
            logBO.setOperator("system");
            logBO.setType(SendBackLogTypeEnum.TYPE_ADD.getValue());
            logBO.setOperateContent("上门取件创建寄回件记录");
            sendBackLogService.addLog(logBO);
        }
    }

    public void queryTraceInfo(Pickup pickup) {
        try {
            if (StringUtils.isNotEmpty(pickup.getPickupCode()) &&
                    Arrays.asList(
                            PickUpStatusEnum.STATUS_CREATE_SUCCESS.getValue(),
                            PickUpStatusEnum.STATUS_WAIT_PICKUP.getValue()
                    ).contains(pickup.getPickupStatus())) {
                // 组装参数
                JdQueryTraceInfoDTO traceInfoDTO = new JdQueryTraceInfoDTO();
                traceInfoDTO.setWaybillCode(pickup.getPickupCode());
                traceInfoDTO.setCustomerCode(jdConfig.getCustomerCode());

                // 查询上门取件轨迹信息
                JdQueryTraceInfoVO traceInfoVO = jdBusiness.queryDynamicTraceInfo(traceInfoDTO);
                if (ObjectUtils.isNotEmpty(traceInfoVO.getData())) {
                    // 循环每条轨迹信息
                    traceInfoVO.getData().forEach(traceInfo -> {
                        if (ObjectUtils.isEmpty(traceInfo.getState())) {
                            return;
                        }
                        if (traceInfo.getWaybillCode().equals(pickup.getPickupCode())) {
                            // 组装取件单更新信息
                            switch (traceInfo.getState()) {
                                case "90017":
                                    // 提取收件配送员信息
                                    String namePattern = "快递员([\\u4e00-\\u9fa5]+)，联系电话(\\d+)";
                                    Pattern r = Pattern.compile(namePattern);
                                    Matcher m = r.matcher(traceInfo.getOpeRemark());
                                    if (m.find()) {
                                        String name = m.group(1);
                                        String mobile = m.group(2);
                                        pickup.setPickupName(name);
                                        pickup.setPickupMobile(mobile);
                                    }
                                    pickup.setPickupStatus(PickUpStatusEnum.STATUS_WAIT_PICKUP.getValue());
                                    break;
                                case "90020":
                                    // 提取取件运单号、取件完成时间
                                    String expressPattern = "取件条码号：([A-Za-z0-9]+)";
                                    Pattern er = Pattern.compile(expressPattern);
                                    Matcher em = er.matcher(traceInfo.getOpeRemark());
                                    if (em.find()) {
                                        String expressNumber = em.group(1);
                                        pickup.setExpressCorp("京东物流");
                                        pickup.setExpressNumber(expressNumber);
                                    }
                                    pickup.setPickupTime(LocalDateTime.parse(traceInfo.getOpeTime(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
                                    pickup.setPickupStatus(PickUpStatusEnum.STATUS_PICKUP_FINISH.getValue());
                                    break;
                                case "90040":
                                    // 取件终止
                                    pickup.setPickupStatus(PickUpStatusEnum.STATUS_PICKUP_ERROR.getValue());
                                default:
                                    break;
                            }
                            pickup.setPickupRemark(traceInfo.getOpeRemark());

                            // 记录日志
                            PickUpLogBO logBO = new PickUpLogBO();
                            logBO.setPickupId(pickup.getId())
                                    .setType(PickUpLogTypeEnum.NOTIFY.getValue())
                                    .setOperateContent(traceInfo.getOpeRemark());
                            pickupLogService.addLog(logBO);
                        } else {
                            // 组装物流轨迹信息
                            Express express = expressService.getOneByExpressNumber(traceInfo.getWaybillCode());

                            // 初始化物流信息
                            if (ObjectUtils.isEmpty(express)) {
                                ExpressBO expressBO = new ExpressBO();
                                expressBO.setExpressCode(ExpressCodeEnum.JD_CLOUD.getValue());
                                expressBO.setExpressNumber(traceInfo.getWaybillCode());
                                expressBO.setExpressCompany("jd");
                                expressBO.setPhone(jdConfig.getReceiverPhone());
                                express = expressBusiness.initExpress(expressBO);
                            }

                            if (ObjectUtils.isNotEmpty(express)) {
                                // 有历史记录数据
                                List<ExpressDataBO> expressDataList = new ArrayList<>();
                                if (StringUtils.isNotEmpty(express.getData())) {
                                    expressDataList = JSON.parseObject(express.getData(), new TypeReference<List<ExpressDataBO>>() {});
                                }
                                // 格式化数据
                                ExpressDataBO expressData = new ExpressDataBO();
                                expressData.setFtime(traceInfo.getOpeTime());
                                expressData.setContext(traceInfo.getOpeRemark());
                                expressData.setStatus(traceInfo.getOpeTitle());
                                expressDataList.add(0, expressData);
                                expressDataList.sort((t1, t2) -> t2.getFtime().compareTo(t1.getFtime()));
                                String dataList = JSON.toJSONString(expressDataList);

                                express.setData(dataList);
                                express.setState(JdTradeNodeExpressEnum.stateMap.getOrDefault(traceInfo.getOpeTitle(),
                                        ExpressStateEnum.ON_THE_WAY.getValue()));
                                express.setSubscribeStatus(JdTradeNodeExpressEnum.subscribeStatusMap.getOrDefault(traceInfo.getOpeTitle(),
                                        ExpressSubscribeStatusEnum.SUBSCRIBING.getValue()));

                                // 最早状态
                                if (StringUtils.isEmpty(express.getFirstContext())) {
                                    express.setFirstStatus(traceInfo.getOpeTitle());
                                    express.setFirstContext(traceInfo.getOpeRemark());
                                    express.setFirstExpressTime(LocalDateTime.parse(traceInfo.getOpeTime(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
                                }

                                // 最新状态
                                express.setLastStatus(traceInfo.getOpeTitle());
                                express.setLastContext(traceInfo.getOpeRemark());
                                express.setLastExpressTime(LocalDateTime.parse(traceInfo.getOpeTime(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));

                                // 签收时间
                                if (traceInfo.getOpeTitle().equals(JdTradeNodeExpressEnum.EXPRESS_DELIVERED.getValue())) {
                                    express.setReceivedTime(LocalDateTime.parse(traceInfo.getOpeTime(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
                                }

                                // 更新物流状态
                                express.setUpdatedAt(LocalDateTime.now());
                                expressService.updateById(express);

                                // 记录日志
                                ExpressLogBO logBO = new ExpressLogBO();
                                logBO.setExpressSn(express.getExpressSn());
                                logBO.setExpressNumber(express.getExpressNumber());
                                logBO.setSubscribeStatus(express.getSubscribeStatus());
                                logBO.setContent("物流订阅回调修改:" + JSON.toJSONString(express));
                                expressLogService.addLog(logBO);
                            }
                        }
                    });
                }
                // 更新取件状态
                pickup.setUpdatedAt(LocalDateTime.now());
                pickupService.updateById(pickup);
                sendPickUpNotify(pickup);

                // 记录日志
                PickUpLogBO logBO = new PickUpLogBO();
                logBO.setPickupId(pickup.getId())
                        .setType(PickUpLogTypeEnum.UPDATE.getValue())
                        .setOperateContent("修复物流轨迹数据");
                pickupLogService.addLog(logBO);
            }
        } catch (Throwable e) {
            log.error("修复物流轨迹数据异常", e);
        }
    }
}
