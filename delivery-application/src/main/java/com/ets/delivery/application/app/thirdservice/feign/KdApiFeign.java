package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.KdApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(url = "${kd100.apiHost}", name = "KdApiFeign", fallbackFactory = KdApiFallbackFactory.class)
public interface KdApiFeign {

    @PostMapping(
            value = "/poll"
    )
    String subscribe(MultiValueMap<String, Object> body);

    @PostMapping(
            value = "/poll/query.do"
    )
    String expressQuery(MultiValueMap<String, Object> body);
}
