package com.ets.delivery.application.common.consts.jd;

import com.ets.delivery.application.common.consts.pickUp.PickUpStatusEnum;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Getter
public enum JdTradeNodePickupEnum {

    // 取件单状态
    PICKUP_CREATE("京东获取取件任务", PickUpStatusEnum.STATUS_CREATE_SUCCESS.getValue(), "京东获取取件任务"),
    PICKUP_TASK_ISSUE("取件任务下发站点", PickUpStatusEnum.STATUS_CREATE_SUCCESS.getValue(), "取件任务下发站点"),
    PICKUP_ASSIGN("取件任务分配配送员", PickUpStatusEnum.STATUS_WAIT_PICKUP.getValue(), "取件任务分配配送员"),
    PICKUP_OVER_ZONE("取件单超区", PickUpStatusEnum.STATUS_PICKUP_ERROR.getValue(), "取件单超区"),
    PICKUP_FINISH("取件完成", PickUpStatusEnum.STATUS_PICKUP_FINISH.getValue(), "取件完成"),
    PICKUP_TERMINATION("取件终止", PickUpStatusEnum.STATUS_PICKUP_TERMINAL.getValue(), "取件终止"),
    PICKUP_DELIVER_AGAIN("取件单再投", PickUpStatusEnum.STATUS_WAIT_PICKUP.getValue(), "取件单再投"),
    PICKUP_DELIVERED("取件运单妥投", PickUpStatusEnum.STATUS_PICKUP_DELIVERED.getValue(), "取件运单妥投");

    private final String value;
    private final Integer status;
    private final String desc;
    public static final Map<String, String> map;
    public static final Map<String, Integer> statusMap;
    public static final List<String> list;

    JdTradeNodePickupEnum(String value, Integer status, String desc) {
        this.value = value;
        this.status = status;
        this.desc = desc;
    }

    static {
        JdTradeNodePickupEnum[] enums = JdTradeNodePickupEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        statusMap = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getStatus()),
                Map::putAll);

        list = Stream.of(enums)
                .map(JdTradeNodePickupEnum::getValue)
                .collect(Collectors.toList());
    }
}
