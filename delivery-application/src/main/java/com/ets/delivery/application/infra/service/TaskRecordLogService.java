package com.ets.delivery.application.infra.service;

import com.ets.delivery.application.infra.entity.TaskRecordLog;
import com.ets.delivery.application.infra.mapper.TaskRecordLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 任务执行日志表 业务处理类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Service
@Slf4j
public class TaskRecordLogService extends BaseService<TaskRecordLogMapper, TaskRecordLog> {
    /*
     * 增加日志
     */
    public TaskRecordLog addLog(String taskSn, String content) {
        TaskRecordLog taskRecordLog = new TaskRecordLog();
        taskRecordLog.setTaskSn(taskSn);
        taskRecordLog.setExecContent(content);
        taskRecordLog.setCreatedAt(LocalDateTime.now());
        taskRecordLog.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(taskRecordLog);
        return taskRecordLog;
    }
}
