package com.ets.delivery.application.app.business.storageMina;

import com.ets.common.ToolsHelper;
import com.ets.delivery.application.common.vo.user.UserDetailVO;
import com.ets.delivery.application.infra.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserBusiness {

    @Autowired
    private AuthBusiness authBusiness;

    public UserDetailVO getLoginUserInfo() {

        User user = authBusiness.getLoginUser();

        UserDetailVO vo =  new UserDetailVO();
        vo.setUserName(user.getUsername());
        vo.setPhone(ToolsHelper.desensitize(user.getPhone(), 4, 4));

        return vo;
    }

}
