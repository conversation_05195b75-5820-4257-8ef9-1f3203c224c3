package com.ets.delivery.application.common.dto.stock;

import com.ets.common.annotation.PhoneAnnotation;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class StockOutCreateDTO {

    /**
     * 仓库编号
     */
    @NotBlank(message = "请选择仓库")
    private String storageCode;

    /**
     * 出库类型
     */
    @NotNull(message = "请选择出库类型")
    private Integer type;

    /**
     * 商品属性
     */
    @NotNull(message = "请选择商品属性")
    private Integer goodsQuality;

    /**
     * 备注
     */
    private String remark;

    /**
     * 图片，逗号分隔
     */
    private String images;

    /**
     * 送货方式
     */
    @NotNull(message = "请选择送货方式")
    private Integer deliveryType;

    /**
     * 收件人
     */
    private String receiveName;

    /**
     * 收件手机号
     */
    @PhoneAnnotation
    private String receivePhone;

    /**
     * 收件地区
     */
    private String receiveArea;

    /**
     * 收件地址
     */
    private String receiveAddress;

    /**
     * 商品信息
     */
    @NotNull(message = "请填写商品信息")
    private @Valid List<StockGoodsInfoDTO> goodsInfo;

}
