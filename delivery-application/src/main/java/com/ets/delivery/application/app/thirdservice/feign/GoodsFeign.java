package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.GoodsFallbackFactory;
import com.ets.delivery.application.app.thirdservice.request.goods.GoodsSkuInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        url = "http://goods-application:20170",
        name = "GoodsFeign",
        fallbackFactory = GoodsFallbackFactory.class
)
public interface GoodsFeign {

    @PostMapping("/goodsSku/getSkusInfo")
    String getSkusInfo(@RequestBody GoodsSkuInfoDTO goodsSkuInfoDTO);
}
