package com.ets.delivery.application.common.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum PlateColorEnum {

    BLUE(0, "蓝色"),
    YELLOW(1, "黄色"),
    BLACK(2, "黑色"),
    WHITE(3, "白色"),
    GRADIENT_GREEN(4, "渐变绿色"),
    YELLOW_HYBRID_GREEN(5, "黄绿双拼色"),
    BLUE_GRADIENT_WHITE(6, "蓝白渐变色"),
    TEMP(7, "临时牌照"),
    UNCERTAIN(9, "未确定"),
    GREEN(11, "绿色"),
    RED(12, "红色");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    static {
        map = Arrays.stream(PlateColorEnum.values()).collect(Collectors.toMap(PlateColorEnum::getValue, PlateColorEnum::getDesc));
    }

}
