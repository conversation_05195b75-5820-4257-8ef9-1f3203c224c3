package com.ets.delivery.application.app.job;

import com.ets.delivery.application.app.business.PostReviewSummaryBusiness;
import com.ets.delivery.application.common.utils.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@Component
public class PostReviewSummaryJob {

    @Autowired
    private PostReviewSummaryBusiness summaryBusiness;

    @XxlJob("reviewDateSummaryHandler")
    public ReturnT<String> reviewDateSummaryHandler(String param) {
        summaryBusiness.doDateSummary();
        return ReturnT.SUCCESS;
    }


    @XxlJob("reviewUserSummaryHandler")
    public ReturnT<String> reviewUserSummaryHandler(String param) {
        Map<String, String> timeMap = DateUtils.dateTimeMap();
        LocalDateTime startTime = LocalDateTime.parse(timeMap.get("startTime"), DateUtils.getTimeFormatter());
        LocalDateTime endTime = LocalDateTime.parse(timeMap.get("endTime"), DateUtils.getTimeFormatter());
        summaryBusiness.doSummaryByUserEveryDay(startTime, endTime);
        return ReturnT.SUCCESS;
    }
}
