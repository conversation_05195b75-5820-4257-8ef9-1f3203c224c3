package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.common.bo.logistics.LogisticsOneBO;
import com.ets.delivery.application.common.bo.logistics.LogisticsPageBO;
import com.ets.delivery.application.common.bo.logistics.LogisticsSkuCreateBO;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.logistics.*;
import com.ets.delivery.application.common.consts.manualLogistics.ImportManualLogisticsRecordStatusEnum;
import com.ets.delivery.application.common.consts.manualLogistics.ImportManualLogisticsStatusEnum;
import com.ets.delivery.application.common.consts.manualLogistics.ImportManualLogisticsUploadStatusEnum;
import com.ets.delivery.application.common.consts.manualLogistics.ManualLogisticsReasonEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeConstant;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaLogiticsCodeEnum;
import com.ets.delivery.application.common.dto.logistics.LogisticsAcceptDTO;
import com.ets.delivery.application.common.dto.manualLogistics.*;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.common.vo.manualLogistics.*;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ManualLogisticsBusiness {

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private LogisticsSkuService logisticsSkuService;

    @Autowired
    private LogisticsLogService logisticsLogService;

    @Autowired
    private ImportFileRecordService importFileRecordService;

    @Autowired
    private ImportManualLogisticsDetailService detailService;

    @Autowired
    private StorageSkuMapService storageSkuMapService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    public IPage<ManualLogisticsListVO> getList(ManualLogisticsListDTO listDTO) {
        // 组装参数
        LogisticsPageBO pageBO = BeanUtil.copyProperties(listDTO, LogisticsPageBO.class);
        pageBO.setOrderType(LogisticsOrderTypeEnum.MANUAL.getValue());
        IPage<LogisticsSku> skuPage = null;
        if (ObjectUtils.isNotEmpty(listDTO.getSku())) {
            // 查询商品对应发货单
            skuPage = logisticsSkuService.getPageBySku(listDTO.getSku(), listDTO.getPageNum(), listDTO.getPageSize());
            if (CollectionUtils.isEmpty(skuPage.getRecords())) {
                return new Page<>();
            }
            // 发货流水号列表
            List<String> logisticsSnList = skuPage.getRecords().stream().map(LogisticsSku::getLogisticsSn).collect(Collectors.toList());
            pageBO.setLogisticsSnList(logisticsSnList);
        }

        // 获取数据
        IPage<Logistics> page = logisticsService.getPage(pageBO);
        IPage<ManualLogisticsListVO> logisticsList = page.convert(logistics -> {
            ManualLogisticsListVO listVO = BeanUtil.copyProperties(logistics, ManualLogisticsListVO.class);
            listVO.setSendName(StrUtil.hide(logistics.getSendName(), 1, 2));
            listVO.setSendAddress("****");
            listVO.setSendPhone(DesensitizedUtil.mobilePhone(logistics.getSendPhone()));

            // 获取商品列表
            List<ManualLogisticsGoodsVO> goodsList = new ArrayList<>();
            List<LogisticsSku> skuList = logisticsSkuService.getListByLogisticsSn(logistics.getLogisticsSn());
            if (ObjectUtils.isNotEmpty(skuList)) {
                skuList.forEach(detail -> {
                    ManualLogisticsGoodsVO goods = BeanUtil.copyProperties(detail, ManualLogisticsGoodsVO.class);
                    goodsList.add(goods);
                });
            }
            listVO.setGoodsList(goodsList);
            return listVO;
        });
        if (ObjectUtils.isNotEmpty(skuPage)) {
            logisticsList.setTotal(skuPage.getTotal());
            logisticsList.setPages(skuPage.getPages());
            logisticsList.setCurrent(skuPage.getCurrent());
            logisticsList.setSize(skuPage.getSize());
        }
        return logisticsList;
    }

    public ManualLogisticsDetailVO getDetail(ManualLogisticsDetailDTO detailDTO) {
        Logistics logistics = logisticsService.getById(detailDTO.getLogisticsId());
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单记录不存在");
        }

        if (!logistics.getOrderType().equals(LogisticsOrderTypeEnum.MANUAL.getValue())) {
            ToolsHelper.throwException("非手动下单记录");
        }

        // 获取发货商品列表
        List<LogisticsSku> skuList = logisticsSkuService.getListByLogisticsSn(logistics.getLogisticsSn());
        List<ManualLogisticsGoodsVO> goodsList = new ArrayList<>();
        skuList.forEach(sku -> {
            ManualLogisticsGoodsVO goods = BeanUtil.copyProperties(sku, ManualLogisticsGoodsVO.class);
            goodsList.add(goods);
        });

        ManualLogisticsDetailVO detailVO = BeanUtil.copyProperties(logistics, ManualLogisticsDetailVO.class);
        detailVO.setGoodsList(goodsList);

        return detailVO;
    }

    public IPage<ManualLogisticsLogListVO> getLog(ManualLogisticsLogListDTO logListDTO) {
        IPage<LogisticsLog> page = logisticsLogService.getPage(logListDTO.getLogisticsId(), logListDTO.getPageNum(), logListDTO.getPageSize());
        return page.convert(log -> BeanUtil.copyProperties(log, ManualLogisticsLogListVO.class));
    }

    public ManualLogisticsOriginInfoVO getOriginLogisticsInfo(ManualLogisticsOriginInfoDTO originInfoDTO) {
        Logistics logistics = logisticsService.getByLogisticsSn(originInfoDTO.getLogisticsSn());
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货流水号不正确");
        }

        // 发货商品
        List<ManualLogisticsGoodsVO> goodsList = new ArrayList<>();

        List<LogisticsSku> skuList = logisticsSkuService.getListByLogisticsSn(logistics.getLogisticsSn());
        if (CollectionUtils.isNotEmpty(skuList)) {
            skuList.forEach(sku -> {
                ManualLogisticsGoodsVO goodsVO = BeanUtil.copyProperties(sku, ManualLogisticsGoodsVO.class);
                goodsList.add(goodsVO);
            });
        } else if(StringUtils.isNotEmpty(logistics.getGoodsCode())) {
            StorageSkuMap storageSkuMap = storageSkuMapService.getOneByStorageSku(logistics.getStorageCode(), logistics.getGoodsCode());
            if (ObjectUtils.isNotEmpty(storageSkuMap)) {
                ManualLogisticsGoodsVO goodsVO = new ManualLogisticsGoodsVO();
                goodsVO.setSku(storageSkuMap.getSku());
                goodsVO.setGoodsName(storageSkuMap.getGoodsName());
                goodsVO.setNums(logistics.getNums());
                goodsList.add(goodsVO);
            }
        }

        ManualLogisticsOriginInfoVO originInfoVO = BeanUtil.copyProperties(logistics, ManualLogisticsOriginInfoVO.class);
        originInfoVO.setGoodsList(goodsList);

        return originInfoVO;
    }

    public ManualLogisticsGoodsInfoVO getGoodsInfo(ManualLogisticsGoodsInfoDTO goodsInfoDTO) {
        StorageSkuMap storageSkuMap = storageSkuMapService.getOneByStorageCodeSku(goodsInfoDTO.getStorageCode(), goodsInfoDTO.getSku());
        if (ObjectUtils.isEmpty(storageSkuMap)) {
            ToolsHelper.throwException("商品编码错误");
        }

        return BeanUtil.copyProperties(storageSkuMap, ManualLogisticsGoodsInfoVO.class);
    }

    public ManualLogisticsSaveVO save(ManualLogisticsSaveDTO saveDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        // 判断原因
        if (!Arrays.asList(ManualLogisticsReasonEnum.REASON_NBSQ.getValue(),
                ManualLogisticsReasonEnum.REASON_YWSQ.getValue(),
                ManualLogisticsReasonEnum.REASON_QT.getValue())
            .contains(saveDTO.getReason())) {
            if (ObjectUtils.isEmpty(saveDTO.getOriginOrderSn())) {
                ToolsHelper.throwException("原发货流水号不能为空");
            }
        }

        // 其他原因 备注不能为空
        if (saveDTO.getReason().equals(ManualLogisticsReasonEnum.REASON_QT.getValue())
                && StringUtils.isEmpty(saveDTO.getRemark())) {
            ToolsHelper.throwException("备注不能为空");
        }

        // 发货地区
        String[] areaArr = saveDTO.getSendArea().trim().split("\\s+");
        if (areaArr.length != 3) {
            ToolsHelper.throwException("收件地区格式不正确（省 市 区）");
        }

        // 快递
        if (!YundaLogiticsCodeEnum.list.contains(saveDTO.getLogisticsCode())) {
            ToolsHelper.throwException("所选快递暂不支持");
        }

        // 判断sku是否存在
        List<String> skuArr = saveDTO.getGoodsList().stream().map(ManualLogisticsSaveDTO.Goods::getSku).collect(Collectors.toList());
        List<StorageSkuMap> storageSkuMapList = storageSkuMapService.getListByStorageCodeSku(saveDTO.getStorageCode(),skuArr);
        List<String> compareSkuArr = storageSkuMapList.stream().map(StorageSkuMap::getSku).collect(Collectors.toList());
        //差集
        skuArr.removeAll(compareSkuArr);
        if (!skuArr.isEmpty()) {
            ToolsHelper.throwException("仓库" + saveDTO.getStorageCode() + "不存在sku:" + skuArr);
        }

        // 发货参数
        Map<String, String> storageSkuMap = storageSkuMapList.stream().collect(Collectors.toMap(StorageSkuMap::getSku, StorageSkuMap::getStorageSku));
        List<LogisticsAcceptDTO.Sku> skuList = new ArrayList<>();
        saveDTO.getGoodsList().forEach(goods -> {
            LogisticsAcceptDTO.Sku sku = BeanUtil.copyProperties(goods, LogisticsAcceptDTO.Sku.class);
            sku.setStorageSku(storageSkuMap.getOrDefault(goods.getSku(), ""));
            skuList.add(sku);
        });

        LogisticsAcceptDTO logisticsAcceptDTO = BeanUtil.copyProperties(saveDTO, LogisticsAcceptDTO.class);
        logisticsAcceptDTO.setOrderType(LogisticsOrderTypeEnum.MANUAL.getValue());
        logisticsAcceptDTO.setOrderSource(LogisticsOrderSourceEnum.MANUAL.getValue());
        logisticsAcceptDTO.setOperator(user.getUsername());
        logisticsAcceptDTO.setSkuList(skuList);

        // 查询原发货单信息
        Logistics originLogistics;
        if (StringUtils.isNotEmpty(saveDTO.getOriginOrderSn())) {
            originLogistics = logisticsService.getByLogisticsSn(saveDTO.getOriginOrderSn());
            if (ObjectUtils.isEmpty(originLogistics)) {
                ToolsHelper.throwException("原发货单不存在");
            }
            logisticsAcceptDTO.setOrderSn(originLogistics.getOrderSn());
            logisticsAcceptDTO.setPlateNo(originLogistics.getPlateNo());
            logisticsAcceptDTO.setPlateColor(originLogistics.getPlateColor());
            logisticsAcceptDTO.setNotifyBackUrl(originLogistics.getNotifyBackUrl());
        } else {
            String orderSn = ToolsHelper.genNum(redisPermanentTemplate, "manualLogistics", ACTIVE, 8);
            logisticsAcceptDTO.setOrderSn(orderSn);
        }

        ManualLogisticsSaveVO saveVO = new ManualLogisticsSaveVO();
        try {
            // 创建发货单
            String logisticsSn = ToolsHelper.genNum(redisPermanentTemplate,"etc_logistics",ACTIVE,8);
            Logistics logistics = logisticsService.createLogistics(logisticsSn, logisticsAcceptDTO);
            logisticsSkuService.create(logisticsSn, logisticsAcceptDTO);

            //添加日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_ADD.getCode(),
                    "新增手动下单发货订单",
                    user.getUsername());

            saveVO.setId(logistics.getId());
        } catch (Throwable e) {
            log.error("手动下单保存失败: {}", ExceptionUtils.getStackTrace(e));
            ToolsHelper.throwException("手动下单保存失败");
        }

        return saveVO;
    }

    public ManualLogisticsEditVO edit(ManualLogisticsEditDTO editDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        Logistics logistics = logisticsService.getById(editDTO.getLogisticsId());
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单不存在");
        }

        if (!logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue())) {
            ToolsHelper.throwException("发货单状态不允许编辑");
        }

        // 判断sku是否存在
        List<String> skuArr = editDTO.getGoodsList().stream().map(ManualLogisticsEditDTO.Goods::getSku).collect(Collectors.toList());
        List<StorageSkuMap> storageSkuMapList = storageSkuMapService.getListByStorageCodeSku(logistics.getStorageCode(),skuArr);
        List<String> compareSkuArr = storageSkuMapList.stream().map(StorageSkuMap::getSku).collect(Collectors.toList());
        //差集
        skuArr.removeAll(compareSkuArr);
        if (!skuArr.isEmpty()) {
            ToolsHelper.throwException("仓库" + logistics.getStorageCode() + "不存在sku:" + skuArr);
        }

        // 修改发货单信息
        Logistics update = BeanUtil.copyProperties(editDTO, Logistics.class);
        update.setId(logistics.getId());
        logisticsService.updateById(update);

        // 修改商品
        // 删除旧发货商品
        logisticsSkuService.delete(logistics.getLogisticsSn());

        // 创建新发货商品
        Map<String, String> storageSkuMap = storageSkuMapList.stream().collect(Collectors.toMap(StorageSkuMap::getSku, StorageSkuMap::getStorageSku));
        List<LogisticsSkuCreateBO> skuList = new ArrayList<>();
        editDTO.getGoodsList().forEach(goods -> {
            LogisticsSkuCreateBO createBO = BeanUtil.copyProperties(goods, LogisticsSkuCreateBO.class);
            createBO.setStorageSku(storageSkuMap.getOrDefault(goods.getSku(), ""));
            skuList.add(createBO);
        });
        logisticsSkuService.create(logistics.getLogisticsSn(), skuList);

        //添加日志
        logisticsLogService.addLog(logistics.getId(),
                LogisticsOperateTypeEnum.TYPE_MODIFY.getCode(),
                "编辑手动下单",
                user.getUsername());

        ManualLogisticsEditVO editVO = new ManualLogisticsEditVO();
        editVO.setId(editDTO.getLogisticsId());
        return editVO;
    }

    public ManualLogisticsPushVO push(ManualLogisticsPushDTO pushDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        Logistics logistics = logisticsService.getById(pushDTO.getLogisticsId());
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单不存在");
        }

        // 检查状态
        if (!logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue())) {
            ToolsHelper.throwException("发货单非待推送状态");
        }

        ManualLogisticsPushVO pushVO = new ManualLogisticsPushVO();
        pushVO.setId(logistics.getId());
        pushVO.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue());

        if (!pushDTO.getConfirmPush()) {
            // 检查相同的原发货流水号+状态为（发货中、已发货）+时间范围两个月内
            LogisticsOneBO oneBO = new LogisticsOneBO();
            if (StringUtils.isNotEmpty(logistics.getOriginOrderSn())) {
                oneBO.setOriginOrderSn(logistics.getOriginOrderSn());
                oneBO.setDeliveryStatusList(Arrays.asList(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue(),
                        LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue()));
                oneBO.setPushStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusMonths(2)));
                Logistics exist = logisticsService.getOneByCondition(oneBO);
                if (ObjectUtils.isNotEmpty(exist)) {
                    pushVO.setMsg("原发货流水号" + logistics.getOriginOrderSn() +
                            "于" + exist.getPushTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) +
                            "由" + exist.getOperator() + "创建");
                    return pushVO;
                }
            }
        }

        // 待发货状态改成发货中
        if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue())) {
            Logistics update = new Logistics();
            update.setId(logistics.getId());
            update.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue());
            update.setPushTime(LocalDateTime.now());
            update.setUpdatedAt(LocalDateTime.now());
            logisticsService.updateById(update);
        }

        // 推送发货
        TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
        taskRecordDTO.setReferSn(logistics.getLogisticsSn());
        taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_DELIVER_GOODS.getType());
        TaskFactory.create(TaskRecordReferTypeConstant.LOGISTICS_DELIVER_GOODS).addAndPush(taskRecordDTO);
        pushVO.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue());

        //添加日志
        logisticsLogService.addLog(logistics.getId(),
                LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                "手动下单推送发货",
                user.getUsername());

        return pushVO;
    }

    public void cancel(ManualLogisticsCancelDTO cancelDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        Logistics logistics = logisticsService.getById(cancelDTO.getLogisticsId());
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单不存在");
        }

        // 已取消
        if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue())) {
            return;
        }

        if (!logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue())) {
            ToolsHelper.throwException("发货单状态不允许取消");
        }

        Logistics update = new Logistics();
        update.setId(logistics.getId());
        update.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue());
        update.setStatus(LogisticsStatusEnum.STATUS_CANCEL.getValue());
        update.setUpdatedAt(LocalDateTime.now());
        logisticsService.updateById(update);

        // 记录日志
        logisticsLogService.addLog(logistics.getId(),
                LogisticsOperateTypeEnum.TYPE_CANCEL.getCode(),
                "取消了手动发货单",
                user.getUsername());
    }

    public IPage<ManualLogisticsImportListVO> getImportDataList(ManualLogisticsImportListDTO importListDTO) {
        ImportFileRecord importFileRecord = importFileRecordService.getByBatchNo(importListDTO.getBatchNo());
        if (ObjectUtils.isEmpty(importFileRecord)) {
            ToolsHelper.throwException("上传记录不存在");
        }

        // 上传失败
        if (importFileRecord.getUploadStatus().equals(ImportManualLogisticsUploadStatusEnum.FAIL.getValue())) {
            ToolsHelper.throwException(importFileRecord.getErrorMsg());
        }

        IPage<ImportManualLogisticsDetail> page = detailService.getPage(importListDTO);
        return page.convert(detail -> BeanUtil.copyProperties(detail, ManualLogisticsImportListVO.class));
    }

    public void batchSave(ManualLogisticsBatchSaveDTO batchSaveDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        List<ImportManualLogisticsDetail> saveList = detailService.getCanSaveList(batchSaveDTO.getBatchNo());
        if (CollectionUtils.isEmpty(saveList)) {
            ToolsHelper.throwException("没有可保存数据");
        }

        saveList.forEach(detail -> {
            try {
                Logistics originLogistics = logisticsService.getByLogisticsSn(detail.getOriginLogisticsSn());
                if (ObjectUtils.isEmpty(originLogistics)) {
                    ToolsHelper.throwException("原发货流水号不存在");
                }

                // 组装发货单参数
                LogisticsAcceptDTO logisticsAcceptDTO = new LogisticsAcceptDTO();
                logisticsAcceptDTO.setOriginOrderSn(detail.getOriginLogisticsSn());
                logisticsAcceptDTO.setOrderSn(originLogistics.getOrderSn());
                logisticsAcceptDTO.setOrderType(LogisticsOrderTypeEnum.MANUAL.getValue());
                logisticsAcceptDTO.setOrderSource(LogisticsOrderSourceEnum.MANUAL.getValue());
                logisticsAcceptDTO.setPlateNo(originLogistics.getPlateNo());
                logisticsAcceptDTO.setPlateColor(originLogistics.getPlateColor());
                logisticsAcceptDTO.setStorageCode(detail.getStorageCode());
                logisticsAcceptDTO.setSendName(detail.getSendName());
                logisticsAcceptDTO.setSendPhone(detail.getSendPhone());
                logisticsAcceptDTO.setSendArea(detail.getSendArea());
                logisticsAcceptDTO.setSendAddress(detail.getSendAddress());
                logisticsAcceptDTO.setLogisticsCode(detail.getLogisticsCode());
                logisticsAcceptDTO.setReason(detail.getReason());
                logisticsAcceptDTO.setRemark(detail.getRemark());
                logisticsAcceptDTO.setNotifyBackUrl(originLogistics.getNotifyBackUrl());
                logisticsAcceptDTO.setOperator(user.getUsername());

                // 发货商品
                List<LogisticsAcceptDTO.Sku> skuList = new ArrayList<>();
                LogisticsAcceptDTO.Sku sku = new LogisticsAcceptDTO.Sku();
                sku.setSku(detail.getSku());
                sku.setNums(detail.getNums());
                sku.setGoodsName(detail.getGoodsName());
                skuList.add(sku);
                logisticsAcceptDTO.setSkuList(skuList);

                // 创建发货单
                String logisticsSn = ToolsHelper.genNum(redisPermanentTemplate,"etc_logistics",ACTIVE,8);
                Logistics logistics = logisticsService.createLogistics(logisticsSn, logisticsAcceptDTO);
                logisticsSkuService.create(logisticsSn, logisticsAcceptDTO);

                // 更新导入记录
                ImportManualLogisticsDetail update = new ImportManualLogisticsDetail();
                update.setId(detail.getId());
                update.setLogisticsSn(logisticsSn);
                update.setRecordStatus(ImportManualLogisticsRecordStatusEnum.SAVED.getValue());
                update.setUpdatedAt(LocalDateTime.now());
                detailService.updateById(update);

                //添加日志
                logisticsLogService.addLog(logistics.getId(),
                        LogisticsOperateTypeEnum.TYPE_ADD.getCode(),
                        "【手动下单】批量保存发货订单 批次号 " + detail.getBatchNo(),
                        user.getUsername());
            } catch (Throwable e) {
                log.error("批量保存失败 批次号：{} 导入记录：{}", detail.getBatchNo(), detail.getId());
            }
        });
    }

    public void batchPush(ManualLogisticsBatchPushDTO batchPushDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        List<String> pushList = new ArrayList<>();
        String content = "批量推送手动下单发货订单";
        if (StringUtils.isNotEmpty(batchPushDTO.getBatchNo())) {
            // 通过批次号推送
            List<ImportManualLogisticsDetail> detailList = detailService.getCanPushList(batchPushDTO.getBatchNo());
            pushList = detailList.stream().map(ImportManualLogisticsDetail::getLogisticsSn).collect(Collectors.toList());
            content = "批量推送手动下单发货订单 批次号：" + batchPushDTO.getBatchNo();
        } else if (StringUtils.isNotEmpty(batchPushDTO.getIds())) {
            // 通过ids推送
            String[] idsArr = batchPushDTO.getIds().split(",");
            if (idsArr.length == 0) {
                ToolsHelper.throwException("请选择要推送的订单");
            }
            List<Integer> idList = new ArrayList<>();
            for (String id : idsArr) {
                idList.add(Integer.valueOf(id));
            }

            List<Logistics> logisticsList = logisticsService.listByIds(idList);
            pushList = logisticsList.stream().map(Logistics::getLogisticsSn).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(pushList)) {
            ToolsHelper.throwException("没有可推送数据");
        }
        String finalContent = content;
        pushList.forEach(logisticsSn -> {
            try {
                Logistics logistics = logisticsService.getByLogisticsSn(logisticsSn);
                if (ObjectUtils.isEmpty(logistics)) {
                    ToolsHelper.throwException("发货单不存在");
                }

                if (!logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue())) {
                    ToolsHelper.throwException("发货单非待推送状态");
                }

                // 待发货状态改成发货中
                if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue())) {
                    Logistics update = new Logistics();
                    update.setId(logistics.getId());
                    update.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue());
                    update.setPushTime(LocalDateTime.now());
                    update.setUpdatedAt(LocalDateTime.now());
                    logisticsService.updateById(update);
                }

                // 推送发货
                TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
                taskRecordDTO.setReferSn(logistics.getLogisticsSn());
                taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_DELIVER_GOODS.getType());
                TaskFactory.create(TaskRecordReferTypeConstant.LOGISTICS_DELIVER_GOODS).addAndPush(taskRecordDTO);

                // 推送发货日志
                logisticsLogService.addLog(logistics.getId(),
                        LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                        finalContent,
                        user.getUsername());
            } catch (BizException e) {
                log.error("【手动下单】批量推送失败：发货流水号：{} 错误：{}", logisticsSn, e.getErrorMsg());
            }
        });
    }

    public void deleteImportRecord(ManualLogisticsImportDetailDeleteDTO deleteDTO) {
        ImportManualLogisticsDetail detail = detailService.getById(deleteDTO.getImportDetailId());
        if (ObjectUtils.isEmpty(detail)) {
            ToolsHelper.throwException("导入记录不存在");
        }

        if (!detail.getRecordStatus().equals(ImportManualLogisticsRecordStatusEnum.DEFAULT.getValue())) {
            ToolsHelper.throwException("记录状态不允许删除");
        }

        if (detail.getStatus().equals(ImportManualLogisticsStatusEnum.DELETED.getValue())) {
            return;
        }

        detail.setStatus(ImportManualLogisticsStatusEnum.DELETED.getValue());
        detail.setUpdatedAt(LocalDateTime.now());
        detailService.updateById(detail);
    }
}
