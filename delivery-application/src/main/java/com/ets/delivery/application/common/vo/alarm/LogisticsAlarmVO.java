package com.ets.delivery.application.common.vo.alarm;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class LogisticsAlarmVO {
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 昨天发货量
     */
    private Double yesterdayCount;

    /**
     * 平均天数
     */
    private Integer avgDay;

    /**
     * 平均发货量
     */
    private Double avgCount;

    /**
     * 涨幅
     */
    private BigDecimal floatPercent = BigDecimal.valueOf(0);

    /**
     * 上涨、下跌
     */
    private String floatTypeStr;
}
