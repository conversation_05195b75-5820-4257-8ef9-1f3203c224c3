package com.ets.delivery.application.app.factory.task.impl;

import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.NotifyBusiness;
import com.ets.delivery.application.app.business.SendBackBusiness;
import com.ets.delivery.application.app.thirdservice.feign.NotifyFeign;
import com.ets.delivery.application.common.bo.notify.SendBackNotifyBO;
import com.ets.delivery.application.common.consts.sendback.SendBackOrderTypeEnum;
import com.ets.delivery.application.common.consts.sendback.SendBackReceiveStatusEnum;
import com.ets.delivery.application.common.consts.sendback.SendBackStatusEnum;
import com.ets.delivery.application.common.consts.storageRecord.GoodsTypeEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.delivery.application.infra.entity.LogisticsSendBack;
import com.ets.delivery.application.infra.entity.StorageRecord;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.LogisticsSendBackService;
import com.ets.delivery.application.infra.service.StorageRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;

@Component
@Slf4j
public class TaskSendBackCheck extends TaskBase {

    @Autowired
    private NotifyBusiness notifyBusiness;

    @Autowired
    private SendBackBusiness sendBackBusiness;

    @Autowired
    private NotifyFeign notifyFeign;

    @Autowired
    private StorageRecordService storageRecordService;

    @Autowired
    private LogisticsSendBackService sendBackService;

    @Override
    public void childExec(TaskRecord taskRecord) {

        LogisticsSendBack sendBack = sendBackBusiness.getBySendBackSn(taskRecord.getReferSn());

        if (! Arrays.asList(SendBackReceiveStatusEnum.RECEIVE_STATUS_NORMAL.getValue(),
                SendBackReceiveStatusEnum.RECEIVE_STATUS_ABNORMAL.getValue()
        ).contains(sendBack.getReviceStatus())) {
            ToolsHelper.throwException("状态异常");
        }

        // 寄回件已取消 无需通知
        if (sendBack.getStatus().equals(SendBackStatusEnum.STATUS_CANCEL.getValue())) {
            ToolsHelper.throwException("寄回件已取消", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        if (StringUtils.isEmpty(sendBack.getNotifyBackUrl())) {
            return;
        }

        String goodsTypeString = "";
        StorageRecord storageRecord = storageRecordService.getByExpressNumber(sendBack.getExpressNumber());
        if (storageRecord != null) {
            goodsTypeString = GoodsTypeEnum.typeMap.getOrDefault(storageRecord.getGoodsType(), "");
        }

        SendBackNotifyBO bo = new SendBackNotifyBO();

        bo.setOrder_sn(sendBack.getOrderSn());
        bo.setStatus(sendBack.getReviceStatus().equals(SendBackReceiveStatusEnum.RECEIVE_STATUS_NORMAL.getValue()) ? 1 : 2);
        bo.setRemark(sendBack.getReviceRemark());
        bo.setReceive_time(ToolsHelper.localDateTimeToString(sendBack.getReviceTime()));
        bo.setOrder_type(1);
        bo.setGoods_type(StringUtils.isEmpty(goodsTypeString) ? "card_obu" : goodsTypeString);

        String signType = "MD5";
        if (Arrays.asList(
                SendBackOrderTypeEnum.AFTER_SALES_MAINTAIN_EXCHANGE.getValue(),
                SendBackOrderTypeEnum.AFTER_SALES_MAINTAIN_EXCHANGE_APPLY.getValue(),
                SendBackOrderTypeEnum.AFTER_SALES_EXCHANGE.getValue(),
                SendBackOrderTypeEnum.AFTER_SALES_EXCHANGE_APPLY.getValue(),
                SendBackOrderTypeEnum.AFTER_SALES_RECALL_EXCHANGE.getValue(),
                SendBackOrderTypeEnum.UPGRADE.getValue(),
                SendBackOrderTypeEnum.AFTER_SALES_RECOVERY.getValue()
        ).contains(sendBack.getOrderType())) {
            signType = "custom";
        }
        HashMap<String, String> headers = notifyBusiness.getNotifyHeaders(sendBack.getAppId(), bo, signType);

        try {
            String jsonRet = notifyFeign.sendBackNotify(URI.create(sendBack.getNotifyBackUrl()), bo, headers);
            if (StringUtils.isNotEmpty(jsonRet)) {
                JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonRet, Object.class);
                result.checkError();
            }
            // 通知成功
            sendBackService.notifySuccess(sendBack.getSendbackSn());
        } catch (Exception e) {
            // 通知失败
            sendBackService.notifyFailed(sendBack.getSendbackSn(), "error: " + e.getMessage());
            ToolsHelper.throwException("【寄回件】通知失败：" + e.getMessage());
        }
    }

}
