package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.YundaApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(url = "${yunda.apiUrl}", name = "YundaApiFeign", fallbackFactory = YundaApiFallbackFactory.class)
public interface YundaApiFeign {

    @PostMapping(
            value = "/k02-oms-web-server/taoBao/erp/fastBack",
            consumes = MediaType.TEXT_PLAIN_VALUE
    )
    String DeliveryOrder(
            @RequestBody String xml,
            @RequestParam(value = "app_key") String appKey,
            @RequestParam(value = "customerId") String customerId,
            @RequestParam(value = "format") String format,
            @RequestParam(value = "method") String method,
            @RequestParam(value = "sign_method") String signMethod,
            @RequestParam(value = "timestamp") String timestamp,
            @RequestParam(value = "v") String version,
            @RequestParam(value = "sign") String sign,
            @RequestParam(value = "channel") Integer channel
    );

    @PostMapping(
            value = "/k02-oms-web-server/shangmei/stock-list"
    )
    String StockList(MultiValueMap<String, Object> body);
}
