package com.ets.delivery.application.common.consts.stock;

import com.ets.delivery.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum StockCarTypeEnum {

    CAR(1, "客车"),

    TRUCK(2, "货车");

    private final Integer code;
    private final String description;

    public static String getDescByCode(int code) {

        for (StockCarTypeEnum node : StockCarTypeEnum.values()) {

            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static StockCarTypeEnum getByCode(int code) {

        for (StockCarTypeEnum node : StockCarTypeEnum.values()) {

            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        List<SelectOptionsVO> selectOptionsVOList = new ArrayList<>();
        for (StockCarTypeEnum node : StockCarTypeEnum.values()) {
            selectOptionsVOList.add(new SelectOptionsVO(node.getCode().toString(),node.getDescription()));
        }

        return selectOptionsVOList;
    }
}
