package com.ets.delivery.application.common.consts.pickUp;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum PickUpCreateTypeEnum {
    TYPE_BUSINESS(1, "业务侧"),
    TYPE_ADMIN(2, "后台");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        PickUpCreateTypeEnum[] enums = PickUpCreateTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
