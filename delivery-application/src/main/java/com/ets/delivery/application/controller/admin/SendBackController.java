package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.SendBackBusiness;
import com.ets.delivery.application.common.dto.logisticsSendBack.*;
import com.ets.delivery.application.common.vo.logisticsSendBack.SendBackDetailVO;
import com.ets.delivery.application.common.vo.logisticsSendBack.SendBackListVO;
import com.ets.delivery.application.common.vo.logisticsSendBack.SendBackLogVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/admin/sendBack")
public class SendBackController {

    @Autowired
    private SendBackBusiness sendBackBusiness;
    
    @PostMapping("/getList")
    public JsonResult<IPage<SendBackListVO>> getList(@RequestBody @Valid SendBackListDTO listDTO) {
        return JsonResult.ok(sendBackBusiness.getList(listDTO));
    }

    @PostMapping("/getDetail")
    public JsonResult<SendBackDetailVO> getDetail(@RequestBody @Valid SendBackDetailDTO detailDTO) {
        return JsonResult.ok(sendBackBusiness.getDetail(detailDTO));
    }

    @PostMapping("/getLog")
    public JsonResult<IPage<SendBackLogVO>> getLog(@RequestBody @Valid SendBackLogDTO logDTO) {
        return JsonResult.ok(sendBackBusiness.getLog(logDTO));
    }

    @PostMapping("/check")
    public JsonResult<?> check(@RequestBody @Valid SendBackCheckDTO checkDTO) {
        sendBackBusiness.check(checkDTO);
        return JsonResult.ok();
    }

    @PostMapping("/done")
    public JsonResult<?> done(@RequestBody @Valid SendBackDoneDTO doneDTO) {
        sendBackBusiness.done(doneDTO);
        return JsonResult.ok();
    }

    @PostMapping("/notifyResult")
    public JsonResult<?> notifyResult(@RequestBody @Valid SendBackNotifyDTO notifyDTO) {
        sendBackBusiness.notifyResult(notifyDTO);
        return JsonResult.ok();
    }
}
