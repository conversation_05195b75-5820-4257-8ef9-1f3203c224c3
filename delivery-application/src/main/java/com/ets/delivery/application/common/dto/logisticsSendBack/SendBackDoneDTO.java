package com.ets.delivery.application.common.dto.logisticsSendBack;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Data
public class SendBackDoneDTO {

    @NotNull(message = "id不能为空")
    private Integer id;

    @NotNull(message = "checkStatus不能为空")
    private Integer checkStatus;

    @Size(max = 200, message = "备注长度不能超过200")
    private String receiveRemark;
}
