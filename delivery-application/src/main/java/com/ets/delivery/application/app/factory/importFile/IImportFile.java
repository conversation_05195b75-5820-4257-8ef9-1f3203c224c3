package com.ets.delivery.application.app.factory.importFile;

import com.ets.delivery.application.infra.entity.ImportFileRecord;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IImportFile<T> {

    void importFileCheck(MultipartFile file);

    ImportFileRecord initImportRecord(String filename, String importType);

    void importFile(MultipartFile file, ImportFileRecord fileRecord);

    void saveImportFileData(String batchNo, List<T> dataList);
}
