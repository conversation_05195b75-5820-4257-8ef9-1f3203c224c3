package com.ets.delivery.application.common.vo.logisticsSendBack;

import com.ets.delivery.application.common.consts.sendback.SendBackNotifyStatusEnum;
import com.ets.delivery.application.common.consts.sendback.SendBackOrderTypeEnum;
import com.ets.delivery.application.common.consts.sendback.SendBackReceiveStatusEnum;
import com.ets.delivery.application.common.consts.sendback.SendBackStatusEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Arrays;

@Data
public class SendBackListVO {

    private Integer id;

    /**
     * 寄回件流水号
     */
    private String sendbackSn;

    /**
     * 订单流水号
     */
    private String orderSn;

    /**
     * 对接方id
     */
    private Integer issuerId;

    private String issuerIdStr;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 订单类型
     */
    private String orderType;

    private String orderTypeStr;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 所属仓储
     */
    private String storageCode;

    private String storageCodeStr;

    /**
     * 货品编号
     */
    private String goodsCode;

    private String goodsName;

    /**
     * 发件人
     */
    private String sendName;

    /**
     * 发件人联系手机
     */
    private String sendPhone;

    /**
     * 发件地区
     */
    private String sendArea;

    /**
     * 发件地址
     */
    private String sendAddress;

    /**
     * 接收人
     */
    private String reviceName;

    /**
     * 接收人联系手机
     */
    private String revicePhone;

    /**
     * 接收地区
     */
    private String reviceArea;

    /**
     * 接收地址
     */
    private String reviceAddress;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 原发货快递单号
     */
    private String originExpressNumber;

    private String expressStateStr;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 状态：1正常2取消3暂停
     */
    private Integer status;

    private String statusStr;

    /**
     * 接收状态,0待接收，1已入库，2检查正常，3检查异常
     */
    private Integer reviceStatus;

    private String reviceStatusStr;

    /**
     * 接收备注
     */
    private String reviceRemark;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime reviceTime;

    /**
     * 通知状态,0待通知，1通知中，2通知成功，3通知失败，4通知取消
     */
    private Integer notifyStatus;

    private String notifyStatusStr;

    /**
     * 通知备注
     */
    private String notifyRemark;

    /**
     * 通知时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime notifyTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    private Boolean showDealAbnormalButton;
    private Boolean showDealNormalButton;
    private Boolean showNotifyButton;
    private Boolean showPushAgainButton;

    public String getOrderTypeStr() {
        return SendBackOrderTypeEnum.map.getOrDefault(orderType, "--");
    }

    public String getStorageCodeStr() {
        return StorageCodeEnum.map.getOrDefault(storageCode, storageCode);
    }

    public String getStatusStr() {
        return SendBackStatusEnum.map.getOrDefault(status, "--");
    }

    public String getReviceStatusStr() {
        return SendBackReceiveStatusEnum.map.getOrDefault(reviceStatus, "--");
    }

    public String getNotifyStatusStr() {
        return SendBackNotifyStatusEnum.map.getOrDefault(notifyStatus, "--");
    }

    public Boolean getShowDealAbnormalButton() {
        return reviceStatus.equals(SendBackReceiveStatusEnum.RECEIVE_STATUS_ENTER.getValue());
    }

    public Boolean getShowDealNormalButton() {
        return Arrays.asList(SendBackReceiveStatusEnum.RECEIVE_STATUS_ENTER.getValue(),
                SendBackReceiveStatusEnum.RECEIVE_STATUS_ABNORMAL.getValue()).contains(reviceStatus);
    }

    public Boolean getShowNotifyButton() {
        return notifyStatus.equals(SendBackNotifyStatusEnum.NOTIFY_STATUS_FAIL.getValue());
    }

    public Boolean getShowPushAgainButton() {
        return notifyStatus.equals(SendBackNotifyStatusEnum.NOTIFY_STATUS_PROCESSING.getValue());
    }
}
