package com.ets.delivery.application.common.vo.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class StockLogVO {

    private Integer id;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    private String operateTypeStr;

}
