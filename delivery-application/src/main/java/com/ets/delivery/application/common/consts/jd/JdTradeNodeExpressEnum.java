package com.ets.delivery.application.common.consts.jd;

import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeStatusEnum;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Getter
public enum JdTradeNodeExpressEnum {

    // 物流单状态
    EXPRESS_ASSIGN("揽收任务分配", ExpressStateEnum.ACCEPT.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "揽收任务分配"),
    EXPRESS_PICKUP("快递接货", ExpressStateEnum.ACCEPT.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "快递接货"),
    EXPRESS_PACKING("站点装箱发货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "站点装箱发货"),
    EXPRESS_SORTING_INSPECT("分拣验货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "分拣验货"),
    EXPRESS_SORTING_PICKUP("分拣中心接货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "分拣中心接货"),
    EXPRESS_SORTING_DELIVER("分拣中心发货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "分拣中心发货"),
    EXPRESS_SITE_RECEIVE("站点收货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "站点收货"),
    EXPRESS_DELIVERYMAN_RECEIVE("配送员收货", ExpressStateEnum.SENDING.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "配送员收货"),
    EXPRESS_DELIVER_AGAIN("再投", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "再投"),
    EXPRESS_REJECT("拒收", ExpressStateEnum.RECEIVER_REJECT.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBE_SHUTDOWN.getValue(), "拒收"),
    EXPRESS_DELIVERED("妥投", ExpressStateEnum.RECEIVED.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBE_SHUTDOWN.getValue(), "妥投"),
    EXPRESS_REVERSE_DELIVERED("逆向妥投", ExpressStateEnum.SEND_BACK.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "逆向妥投"),
    EXPRESS_INTERCEPT("拦截成功", ExpressStateEnum.SEND_BACK.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBE_ABORT.getValue(), "拦截成功"),
    EXPRESS_REVERSE_DELIVER("逆向发货", ExpressStateEnum.SEND_BACK.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "逆向发货"),
    EXPRESS_REVERSE_EXCHANGE("逆向换单", ExpressStateEnum.SEND_ANOTHER.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "逆向换单"),
    EXPRESS_DELIVERED_FINISH("投递完成", ExpressStateEnum.RECEIVED.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBE_SHUTDOWN.getValue(), "投递完成"),
    EXPRESS_DELIVERYMAN_RECYCLE("快递员回收", ExpressStateEnum.SEND_BACK.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "快递员回收"),
    EXPRESS_OVERTIME("超时催提", ExpressStateEnum.ON_TROUBLE.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "超时催提"),
    EXPRESS_TERMINATION("终止揽收", ExpressStateEnum.ON_TROUBLE.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBE_ABORT.getValue(), "终止揽收");

    private final String value;
    private final Integer state;
    private final Integer subscribeStatus;
    private final String desc;
    public static final Map<String, String> map;
    public static final Map<String, Integer> stateMap;
    public static final Map<String, Integer> subscribeStatusMap;
    public static final List<String> list;

    JdTradeNodeExpressEnum(String value, Integer state, Integer subscribeStatus, String desc) {
        this.value = value;
        this.state = state;
        this.subscribeStatus = subscribeStatus;
        this.desc = desc;
    }

    static {
        JdTradeNodeExpressEnum[] enums = JdTradeNodeExpressEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        stateMap = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getState()),
                Map::putAll);
        subscribeStatusMap = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getSubscribeStatus()),
                Map::putAll);
        list = Stream.of(enums)
                .map(JdTradeNodeExpressEnum::getValue)
                .collect(Collectors.toList());
    }
}
