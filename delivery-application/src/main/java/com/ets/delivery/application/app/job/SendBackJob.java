package com.ets.delivery.application.app.job;

import com.ets.delivery.application.app.business.SendBackBusiness;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SendBackJob {

    @Autowired
    private SendBackBusiness sendBackBusiness;

    @XxlJob("checkOvertimeHandler")
    public ReturnT<String> checkOvertimeHandler(String params) {
        log.info("【检查寄回件超时未签收】超时时间：{}小时", params);
        Integer overtimeHour = Integer.valueOf(params);
        sendBackBusiness.checkReceiveOvertime(overtimeHour);
        return ReturnT.SUCCESS;
    }
}
