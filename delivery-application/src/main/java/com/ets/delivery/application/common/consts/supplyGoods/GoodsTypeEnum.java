package com.ets.delivery.application.common.consts.supplyGoods;

import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
public enum GoodsTypeEnum {

    CARD("card", "单卡"),
    OBU("obu", "单obu"),
    CARD_OBU("card_obu", "整套");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;

    GoodsTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        GoodsTypeEnum[] enums = GoodsTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Arrays.stream(enums).map(GoodsTypeEnum::getValue).collect(Collectors.toList());
    }
}
