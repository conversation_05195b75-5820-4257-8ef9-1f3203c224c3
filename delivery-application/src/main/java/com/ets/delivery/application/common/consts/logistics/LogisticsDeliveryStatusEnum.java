package com.ets.delivery.application.common.consts.logistics;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum LogisticsDeliveryStatusEnum {
    DELIVERY_STATUS_WAIT(0, "待发货"),
    DELIVERY_STATUS_PROCESSING(1, "处理中"),
    DELIVERY_STATUS_SHIPPED(2, "已发货"),
    DELIVERY_STATUS_CANCEL(3, "发货取消"),
    DELIVERY_STATUS_STOP(4, "发货暂停");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    LogisticsDeliveryStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        LogisticsDeliveryStatusEnum[] enums = LogisticsDeliveryStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
