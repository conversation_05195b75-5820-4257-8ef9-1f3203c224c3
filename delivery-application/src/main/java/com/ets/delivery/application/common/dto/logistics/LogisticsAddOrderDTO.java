package com.ets.delivery.application.common.dto.logistics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogisticsAddOrderDTO {

    @NotNull(message = "仓储编码不能为空")
    String storageCode;

    @NotNull(message = "发货单号不能为空")
    String logisticsSn;

    @NotNull(message = "收货地址省份不能为空")
    String province;

    @NotNull(message = "收货地址城市不能为空")
    String city;

    String area = "";

    @NotNull(message = "收货地址不能为空")
    String detailAddress;

    @NotNull(message = "收货人姓名不能为空")
    @Size(max = 20, message = "收件人姓名不能超过20个字符")
    String name;

    @NotNull(message = "收货人手机号码不能为空")
    String mobile;

    String remark = "";

    String logisticsCode;

    @NotNull(message = "发货商品列表不能为空")
    List<Goods> goodsList;

    @Data
    public static class Goods {
        @NotNull(message = "商品编号不能为空")
        String goodsCode;

        Integer quantity = 1;
    }
}
