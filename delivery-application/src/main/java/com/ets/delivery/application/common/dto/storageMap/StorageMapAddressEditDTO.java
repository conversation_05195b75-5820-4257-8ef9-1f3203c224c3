package com.ets.delivery.application.common.dto.storageMap;

import com.ets.delivery.application.common.bo.StorageMapAddressRuleBO;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class StorageMapAddressEditDTO {

    /**
     * 配置ID
     */
    @NotNull(message = "配置ID不能为空")
    private Integer configId;

    /**
     * 配置名称
     */
    @NotEmpty(message = "配置名称不能为空")
    private String configName;

    /**
     * 默认仓储代号
     */
    @NotEmpty(message = "默认仓储代号不能为空")
    private String defaultStorageCode;

    private String logisticsCode;

    private List<StorageMapAddressRuleBO> rules;
}
