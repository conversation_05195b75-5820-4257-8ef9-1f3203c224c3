package com.ets.delivery.application.common.vo.logistics;

import lombok.Data;

import java.util.List;

/**
 * 发货单SKU数量总额返回结果
 */
@Data
public class LogisticsSkuSummaryVO {

    /**
     * 发货单流水号
     */
    private String logisticsSn;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * SKU总数量
     */
    private Integer totalSkuCount;

    /**
     * SKU详细信息列表
     */
    private List<SkuDetailVO> skuDetails;

    /**
     * SKU详细信息
     */
    @Data
    public static class SkuDetailVO {
        /**
         * 货品编号
         */
        private String sku;

        /**
         * 商品名称
         */
        private String goodsName;

        /**
         * 仓库对应sku的编码
         */
        private String storageSku;

        /**
         * 发货数量
         */
        private Integer nums;
    }
}
