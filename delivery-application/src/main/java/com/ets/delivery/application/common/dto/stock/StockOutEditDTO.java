package com.ets.delivery.application.common.dto.stock;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class StockOutEditDTO {

    @NotBlank(message = "出库单号不能为空")
    private String stockOutSn;

    /**
     * 备注
     */
    private String remark;

    /**
     * 图片，逗号分隔
     */
    private String images;

    private @Valid List<GoodsEditInfo> goodsEditInfo;

    @Data
    public static class GoodsEditInfo {

        @NotNull
        private Integer id;

        private List<StockGoodsNumberDTO> numberInfoList;

    }

}
