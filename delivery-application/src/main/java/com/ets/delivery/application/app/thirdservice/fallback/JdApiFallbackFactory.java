package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.JdApiFeign;
import org.springframework.cloud.openfeign.FallbackFactory;

import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.Map;

@Component
public class JdApiFallbackFactory implements FallbackFactory<JdApiFeign> {
    @Override
    public JdApiFeign create(Throwable throwable) {
        return new JdApiFeign() {
            @Override
            public String RouterJson(MultiValueMap<String, String> body, String method, String appKey, String accessToken, String timestamp, String v, String sign) {
                return JsonResult.error("请求京东接口【" + method + "】失败：" + throwable.getLocalizedMessage()).toString();
            }
        };
    }
}
