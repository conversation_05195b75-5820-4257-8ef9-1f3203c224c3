package com.ets.delivery.application.app.job;

import com.alibaba.fastjson.JSON;
import com.ets.delivery.application.app.thirdservice.business.KdBusiness;
import com.ets.delivery.application.common.bo.express.ExpressBO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class ExpressJob {

    @Autowired
    private KdBusiness kdBusiness;

    @XxlJob("expressSubscribe")
    public ReturnT<String> expressSubscribe(String params){

        ExpressBO expressBO = JSON.parseObject(params, ExpressBO.class);

        kdBusiness.subscribe(expressBO);

        return ReturnT.SUCCESS;
    }
}
