package com.ets.delivery.application.common.consts.yunda;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum YundaSourceOrderEnum {

    SYSTEM_ORDER(11, 1, "系统订单"),
    JUSHUITAN_ORDER(0, 2, "聚水潭订单");

    private final Integer value;
    private final Integer orderType;
    private final String desc;
    public static final Map<Integer, String> map;
    public static final Map<Integer, Integer> orderTypeMap;

    static {
        map = Arrays.stream(YundaSourceOrderEnum.values()).collect(Collectors.toMap(YundaSourceOrderEnum::getValue, YundaSourceOrderEnum::getDesc));
        orderTypeMap = Arrays.stream(YundaSourceOrderEnum.values()).collect(Collectors.toMap(YundaSourceOrderEnum::getValue, YundaSourceOrderEnum::getOrderType));
    }
}
