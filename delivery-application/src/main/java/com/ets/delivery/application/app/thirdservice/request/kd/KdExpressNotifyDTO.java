package com.ets.delivery.application.app.thirdservice.request.kd;

import com.ets.delivery.application.app.thirdservice.response.kd.KdExpressData;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class KdExpressNotifyDTO {
    ResultParam param;

    @Data
    public static class ResultParam {
        String billstatus;
        String message;
        String status;
        Integer autoCheck;
        String comNew;
        @NotNull(message = "查询结果不能为空")
        LastResult lastResult;

        @Data
        public static class LastResult {
            String com;
            String condition;
            Integer ischeck;
            String message;
            @NotBlank(message = "快递单号不能为空")
            String nu;
            @NotNull(message = "快递状态不能为空")
            Integer state;
            Integer status;
            List<KdExpressData> data;
        }
    }
}
