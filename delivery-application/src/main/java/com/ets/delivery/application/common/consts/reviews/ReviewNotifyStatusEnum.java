package com.ets.delivery.application.common.consts.reviews;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum ReviewNotifyStatusEnum {

    NOTIFY_STATUS_WAIT(0, "未通知"),
    NOTIFY_STATUS_SUCCESS(1, "通知成功"),
    NOTIFY_STATUS_FAIL(2, "通知失败");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    ReviewNotifyStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        ReviewNotifyStatusEnum[] enums = ReviewNotifyStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
