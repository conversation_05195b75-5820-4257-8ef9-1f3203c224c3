package com.ets.delivery.application.app.business;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ets.delivery.application.common.bo.ReviewDataBO;
import com.ets.delivery.application.common.consts.EmergencyTypeEnum;
import com.ets.delivery.application.common.consts.ReviewQueueCacheKey;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
public class PostReviewQueueBusiness {

    @Resource(name = "defaultRedisTemplate")
    StringRedisTemplate redisTemplate;

    public List<String> getUserPostReviewList(String username) {
        List<String> list = new ArrayList<>();
        String userPostReviewSnStr = redisTemplate.opsForValue().get(ReviewQueueCacheKey.getUserPostReviewKey(username));
        if (StringUtils.isNotEmpty(userPostReviewSnStr)) {
            list = JSON.parseArray(userPostReviewSnStr, String.class);
        }
        return list;
    }

    public void addUserPostReview(String username, String reviewSn) {
        List<String> list = new ArrayList<>();
        String key = ReviewQueueCacheKey.getUserPostReviewKey(username);
        String userPostReviewSnStr = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(userPostReviewSnStr)) {
            list = JSON.parseArray(userPostReviewSnStr, String.class);
        }
        list.add(reviewSn);
        redisTemplate.opsForValue().set(key, JSON.toJSONString(list));
    }

    public void removeUserPostReview(String username, String reviewSn) {
        String key = ReviewQueueCacheKey.getUserPostReviewKey(username);
        String userPostReviewSnStr = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(userPostReviewSnStr)) {
            List<String> list = JSON.parseArray(userPostReviewSnStr, String.class);
            list.remove(reviewSn);
            redisTemplate.opsForValue().set(key, JSON.toJSONString(list));
        }
    }


    public void pushToPostReviewQueueList(String reviewSn, Integer emergencyType) {
        String key = ReviewQueueCacheKey.getPostReviewQueueKey(emergencyType);
        redisTemplate.opsForList().leftPush(key, reviewSn);
    }

    public void rePushToPostReviewQueueList(String reviewSn, Integer emergencyType) {
        String key = ReviewQueueCacheKey.getPostReviewQueueKey(emergencyType);
        redisTemplate.opsForList().rightPush(key, reviewSn);
    }

    public String popFromPostReviewQueueList(Boolean isAsc, Integer emergencyType) {
        String sn = "";
        if (emergencyType != null && !Objects.equals(emergencyType, EmergencyTypeEnum.NO_NEED.getValue())) {
            String key = ReviewQueueCacheKey.getPostReviewQueueKey(emergencyType);
            if (isAsc) {
                // 正序
                sn = redisTemplate.opsForList().rightPop(key);
            } else {
                // 倒序
                sn = redisTemplate.opsForList().leftPop(key);
            }
        } else {
            for (Integer i : EmergencyTypeEnum.list) {
                String key = ReviewQueueCacheKey.getPostReviewQueueKey(i);
                if (isAsc) {
                    // 正序
                    sn = redisTemplate.opsForList().rightPop(key);
                } else {
                    // 倒序
                    sn = redisTemplate.opsForList().leftPop(key);
                }
                if (!StringUtils.isEmpty(sn)) {
                    break;
                }
            }
        }
        return sn;
    }

    /**
     * 缓存审核数据
     * @param reviewSn 审核单号
     * @param reviewDataBO 审核数据
     */
    public void setReviewData(String reviewSn, ReviewDataBO reviewDataBO) {
        if (ObjectUtil.isNotNull(reviewDataBO)) {
            String dataStr = JSON.toJSONString(reviewDataBO);
            String key = ReviewQueueCacheKey.getReviewDataKey(reviewSn);
            redisTemplate.opsForValue().set(key, dataStr, ReviewQueueCacheKey.REVIEW_DATA_CACHE_TIME, TimeUnit.SECONDS);
        }
    }

    /**
     * 获取缓存审核数据
     * @param reviewSn 审核单号
     * @return 审核数据
     */
    public ReviewDataBO getReviewData(String reviewSn) {
        ReviewDataBO reviewDataBO = new ReviewDataBO();
        String key = ReviewQueueCacheKey.getReviewDataKey(reviewSn);
        String reviewDataStr = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(reviewDataStr)) {
            reviewDataBO = JSON.parseObject(reviewDataStr, ReviewDataBO.class);
        }
        return reviewDataBO;
    }

    public void delReviewData(String reviewSn) {
        String key = ReviewQueueCacheKey.getReviewDataKey(reviewSn);
        redisTemplate.delete(key);
    }

    public List<String> getPostReviewQueueList(Integer emergencyType, long start, long end) {
        String key = ReviewQueueCacheKey.getPostReviewQueueKey(emergencyType);
        return redisTemplate.opsForList().range(key, start, end);
    }

    public void delPostReviewQueueList(Integer emergencyType) {
        String key = ReviewQueueCacheKey.getPostReviewQueueKey(emergencyType);
        redisTemplate.delete(key);
    }
}
