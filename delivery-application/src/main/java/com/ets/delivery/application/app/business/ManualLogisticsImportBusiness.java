package com.ets.delivery.application.app.business;

import com.ets.delivery.application.app.factory.importFile.ImportFileFactory;
import com.ets.delivery.application.app.factory.importFile.impl.ImportFileBase;
import com.ets.delivery.application.common.consts.importFile.ImportFileImportTypeEnum;
import com.ets.delivery.application.common.vo.manualLogistics.ManualLogisticsImportVO;
import com.ets.delivery.application.infra.entity.ImportFileRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Component
public class ManualLogisticsImportBusiness {

    public ManualLogisticsImportVO logisticsImport(MultipartFile file) {
        ImportFileBase manualLogistics = ImportFileFactory.create(ImportFileImportTypeEnum.MANUAL_LOGISTICS_IMPORT);

        // 检查文件
        manualLogistics.importFileCheck(file);

        // 初始化导入记录
        ImportFileRecord importFileRecord = manualLogistics.initImportRecord(file.getOriginalFilename(), ImportFileImportTypeEnum.MANUAL_LOGISTICS_IMPORT.getType());

        // 读取Excel文件
        manualLogistics.importFile(file, importFileRecord);

        ManualLogisticsImportVO importVO = new ManualLogisticsImportVO();
        importVO.setBatchNo(importFileRecord.getBatchNo());
        return importVO;
    }
}
