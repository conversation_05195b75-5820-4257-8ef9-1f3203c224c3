package com.ets.delivery.application.common.vo.manualLogistics;

import com.ets.delivery.application.common.consts.manualLogistics.ManualLogisticsReasonEnum;
import com.ets.delivery.application.common.consts.yunda.YundaLogiticsCodeEnum;
import lombok.Data;

@Data
public class ManualLogisticsImportListVO {

    private Integer id;

    /**
     * 原发货流水号
     */
    private String originLogisticsSn;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 下单原因
     */
    private String reason;

    private String reasonStr;

    /**
     * 发货快递编码
     */
    private String logisticsCode;

    private String logisticsCodeStr;

    /**
     * 商品编码
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品数量
     */
    private Integer nums;

    /**
     * 收货人姓名
     */
    private String sendName;

    /**
     * 收货人电话
     */
    private String sendPhone;

    /**
     * 收货人地区
     */
    private String sendArea;

    /**
     * 收货人详细地址
     */
    private String sendAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分析结果等级
     */
    private Integer resultLevel;

    /**
     * 分析结果说明
     */
    private String resultMsg;

    public String getReasonStr() {
        return ManualLogisticsReasonEnum.map.getOrDefault(reason, reason);
    }

    public String getLogisticsCodeStr() {
        return YundaLogiticsCodeEnum.map.getOrDefault(logisticsCode, logisticsCode);
    }
}
