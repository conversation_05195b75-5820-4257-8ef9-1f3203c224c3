package com.ets.delivery.application.common.consts;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum ChannelIdEnum {

    CHANNEL_ID_NORMAL(1, "", "普通客车订单"),
    CHANNEL_ID_OFFLINE(2, "99", "地推（自提）订单");

    private final Integer value;
    private final String channel;
    private final String desc;
    public static final Map<Integer, String> map;
    public static final Map<String, String> descMap;

    ChannelIdEnum(Integer value, String channel, String desc) {
        this.value = value;
        this.channel = channel;
        this.desc = desc;
    }

    static {
        ChannelIdEnum[] enums = ChannelIdEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getChannel()),
                Map::putAll);
        descMap = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getChannel(), enums[index].getDesc()),
                Map::putAll);
    }
}
