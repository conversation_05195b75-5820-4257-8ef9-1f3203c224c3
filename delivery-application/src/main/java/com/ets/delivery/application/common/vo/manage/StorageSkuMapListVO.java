package com.ets.delivery.application.common.vo.manage;

import com.ets.delivery.application.common.consts.storageSkuMap.StorageSkuMapStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
@Data
public class StorageSkuMapListVO {
    private Integer id;

    /**
     * 商品代码
     */
    private String sku;

    /**
     * 仓库代号
     */
    private String storageCode;

    /**
     * 仓库对应sku的编码
     */
    private String storageSku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 指定区域（json格式)
     */
    private String assignArea;

    /**
     * 状态：1正常2关闭
     */
    private Integer status;

    /*
     *  状态展示
     */
    private String statusStr;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;


    public String getStatusStr() {
        return StorageSkuMapStatusEnum.getDescByStatus(this.status);
    }
}
