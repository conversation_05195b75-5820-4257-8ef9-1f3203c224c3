package com.ets.delivery.application.infra.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.bo.PickUpLogBO;
import com.ets.delivery.application.infra.entity.PickupLog;
import com.ets.delivery.application.infra.mapper.PickupLogMapper;
import org.springframework.stereotype.Service;

import jakarta.validation.Valid;
import java.time.LocalDateTime;

/**
 * <p>
 * 上门取件单日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
@Service
@DS("db-issuer-admin")
public class PickupLogService extends BaseService<PickupLogMapper, PickupLog> {

    public void addLog(@Valid PickUpLogBO logBO) {
        PickupLog log = BeanUtil.copyProperties(logBO, PickupLog.class);
        log.setCreatedAt(LocalDateTime.now());
        log.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(log);
    }

    public IPage<PickupLog> getLogListById(Integer pickUpId, Integer pageNum, Integer pageSize) {
        Wrapper<PickupLog> wrapper = Wrappers.<PickupLog>lambdaQuery()
                .eq(PickupLog::getPickupId, pickUpId)
                .orderByDesc(PickupLog::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);
    }
}
