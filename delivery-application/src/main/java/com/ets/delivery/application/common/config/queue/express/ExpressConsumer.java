package com.ets.delivery.application.common.config.queue.express;

import com.ets.starter.base.BaseConsumer;
import com.ets.starter.interceptor.ApplicationContextHelper;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ExpressConsumer extends BaseConsumer implements MessageListenerConcurrently {
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        DefaultMQProducer defaultMQProducer = (DefaultMQProducer) ApplicationContextHelper.getBean(QueueExpressConfig.PRODUCER_BEAN_NAME);
        if (defaultMQProducer == null) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        return super.handleRocketMq(defaultMQProducer, list, consumeConcurrentlyContext);
    }
}
