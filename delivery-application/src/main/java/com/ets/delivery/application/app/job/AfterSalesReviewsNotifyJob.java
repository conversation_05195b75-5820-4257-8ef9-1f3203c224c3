package com.ets.delivery.application.app.job;

import com.ets.delivery.application.app.business.aftersalesreviews.AfterSalesReviewsBusiness;
import com.ets.delivery.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.delivery.application.infra.service.AftersalesReviewsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Arrays;

/**
 * 售后审核通知重试定时任务
 */
@Slf4j
@Component
public class AfterSalesReviewsNotifyJob {

    @Autowired
    private AftersalesReviewsService aftersalesReviewsService;

    @Autowired
    private AfterSalesReviewsBusiness afterSalesReviewsBusiness;

    /**
     * 重试通知失败的售后审核单
     *
     * @param params 参数格式：hours,limit 例如：24,100 表示查询24小时内最多100条失败记录
     * @return 执行结果
     */
    @XxlJob("retryAfterSalesReviewsNotifyHandler")
    public ReturnT<String> retryAfterSalesReviewsNotifyHandler(String params) {
        try {
            // 解析参数
            int hours = 24; // 默认24小时
            int limit = 100; // 默认100条

            if (StringUtils.isNotBlank(params)) {
                String[] paramArray = params.split(",");
                if (paramArray.length >= 1) {
                    hours = Integer.parseInt(paramArray[0].trim());
                }
                if (paramArray.length >= 2) {
                    limit = Integer.parseInt(paramArray[1].trim());
                }
            }

            // 限制参数范围
            hours = Math.max(1, Math.min(hours, 168)); // 1-168小时（7天）
            limit = Math.max(1, Math.min(limit, 1000)); // 1-1000条

            XxlJobLogger.log("开始重试售后审核通知，查询参数：{}小时内，最多{}条记录", hours, limit);

            // 查询通知失败的记录
            List<AftersalesReviews> failedRecords = aftersalesReviewsService.getNotifyFailedRecords(hours, limit);

            if (failedRecords.isEmpty()) {
                XxlJobLogger.log("没有找到需要重试的通知失败记录");
                return ReturnT.SUCCESS;
            }

            XxlJobLogger.log("找到{}条通知失败记录，开始重试", failedRecords.size());

            int successCount = 0;
            int failCount = 0;

            // 逐个重试
            for (AftersalesReviews record : failedRecords) {
                try {
                    XxlJobLogger.log("重试审核单号：{}", record.getReviewSn());
                    afterSalesReviewsBusiness.sendNotifyCallback(record);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    log.error("重试售后审核单通知失败，审核单号：{}", record.getReviewSn(), e);
                    XxlJobLogger.log("重试审核单号：{} 失败，错误：{}", record.getReviewSn(), e.getMessage());
                }
            }

            String resultMsg = String.format("重试完成，成功：%d条，失败：%d条", successCount, failCount);
            XxlJobLogger.log(resultMsg);
            log.info("售后审核通知重试任务完成，{}", resultMsg);

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            String errorMsg = "售后审核通知重试任务执行异常：" + e.getMessage();
            log.error(errorMsg, e);
            XxlJobLogger.log(errorMsg);
            return new ReturnT<>(ReturnT.FAIL_CODE, errorMsg);
        }
    }

    /**
     * 手动通知审核结果
     *
     * @param params 审核单号，多个单号用逗号分隔
     * @return 执行结果
     */
    @XxlJob("manualNotifyAfterSalesReviewHandler")
    public ReturnT<String> manualNotifyAfterSalesReviewHandler(String params) {
        try {
            if (StringUtils.isBlank(params)) {
                String errorMsg = "参数不能为空，请提供审核单号";
                XxlJobLogger.log(errorMsg);
                return new ReturnT<>(ReturnT.FAIL_CODE, errorMsg);
            }

            XxlJobLogger.log("开始手动通知审核结果，审核单号：{}", params);

            // 分割多个审核单号
            String[] reviewSnArray = params.split(",");
            int successCount = 0;
            int failCount = 0;
            int notFoundCount = 0;

            for (String reviewSn : reviewSnArray) {
                reviewSn = reviewSn.trim();
                if (StringUtils.isBlank(reviewSn)) {
                    continue;
                }

                XxlJobLogger.log("处理审核单号：{}", reviewSn);

                // 查询审核单记录
                AftersalesReviews review = aftersalesReviewsService.getByReviewSn(reviewSn);
                if (review == null) {
                    XxlJobLogger.log("审核单号：{} 不存在", reviewSn);
                    notFoundCount++;
                    continue;
                }

                // 检查审核单状态
                if (!Arrays.asList(
                        AftersalesReviewsStatusEnum.APPROVED.getValue(),
                        AftersalesReviewsStatusEnum.REJECTED.getValue()
                ).contains(review.getReviewStatus())) {
                    XxlJobLogger.log("审核单号：{} 未完成审核，当前状态：{}", reviewSn, review.getReviewStatus());
                    failCount++;
                    continue;
                }

                try {
                    // 发送通知
                    XxlJobLogger.log("发送审核单号：{} 的通知", reviewSn);
                    afterSalesReviewsBusiness.sendNotifyCallback(review);
                    XxlJobLogger.log("审核单号：{} 通知成功", reviewSn);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    String errorMsg = String.format("审核单号：%s 通知失败，错误：%s", reviewSn, e.getMessage());
                    log.error(errorMsg, e);
                    XxlJobLogger.log(errorMsg);
                }
            }

            String resultMsg = String.format("手动通知完成，成功：%d条，失败：%d条，未找到：%d条",
                    successCount, failCount, notFoundCount);
            XxlJobLogger.log(resultMsg);
            log.info("手动通知审核结果任务完成，{}", resultMsg);

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            String errorMsg = "手动通知审核结果任务执行异常：" + e.getMessage();
            log.error(errorMsg, e);
            XxlJobLogger.log(errorMsg);
            return new ReturnT<>(ReturnT.FAIL_CODE, errorMsg);
        }
    }

}
