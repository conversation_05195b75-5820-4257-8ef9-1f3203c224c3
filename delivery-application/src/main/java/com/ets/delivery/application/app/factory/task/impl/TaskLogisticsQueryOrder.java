package com.ets.delivery.application.app.factory.task.impl;

import com.alibaba.fastjson.JSON;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.storage.StorageFactory;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsNotifyStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOperateTypeEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsStatusEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeConstant;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.ExWarehouseService;
import com.ets.delivery.application.infra.service.LogisticsLogService;
import com.ets.delivery.application.infra.service.LogisticsService;
import com.ets.delivery.application.infra.service.TaskRecordService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TaskLogisticsQueryOrder extends TaskBase {

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private LogisticsLogService logisticsLogService;

    @Autowired
    private ExWarehouseService exWarehouseService;

    @Autowired
    private TaskRecordService taskRecordService;

    @Override
    public void childExec(TaskRecord taskRecord) {
        // 查询发货单
        Logistics logistics = logisticsService.getByLogisticsSn(taskRecord.getReferSn());
        if (ObjectUtils.isEmpty(logistics) || logistics.getStatus().equals(LogisticsStatusEnum.STATUS_CANCEL.getValue())) {
            ToolsHelper.throwException("发货单不存在或已取消", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        // 查询出库单
        ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(taskRecord.getReferSn());
        if (ObjectUtils.isEmpty(exWarehouse) || StringUtils.isEmpty(exWarehouse.getDeliverOrderNo())) {
            String msg = "发货单" + taskRecord.getReferSn() + "出库单数据异常";

            // 记录日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                    msg,
                    "system");

            ToolsHelper.throwException(msg);
        }

        // 查询物流出库单详情
        if (StringUtils.isEmpty(exWarehouse.getWayBill())) {
            ExWarehouse afterExWarehouse = StorageFactory.create(exWarehouse.getStorageCode()).orderQuery(exWarehouse);
            String expressNumber = "";
            // 出库单是否拆单
            if (afterExWarehouse.getSplitFlag() == 1) {
                // 获取拆单记录
                List<ExWarehouse> splitOrderList = exWarehouseService.getSplitOrderList(afterExWarehouse.getDeliverOrderNo());
                if (ObjectUtils.isNotEmpty(splitOrderList)) {
                    // 临时取第一个运单号
                    for (ExWarehouse splitOrder: splitOrderList) {
                        if (StringUtils.isNotEmpty(splitOrder.getWayBill())) {
                            expressNumber = splitOrder.getWayBill();
                        }
                    }
                }
            } else {
                expressNumber = afterExWarehouse.getWayBill();
            }

            // 更新发货单
            logistics.setExpressCorp("京东物流");
            logistics.setExpressNumber(expressNumber);
            logistics.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue());
            logistics.setDeliveryTime(LocalDateTime.now());
            logistics.setDeliveryRemark("京东发货成功");
            logistics.setNotifyStatus(LogisticsNotifyStatusEnum.NOTIFY_STATUS_PROCESSING.getValue());
            logistics.setNotifyTime(null);
            logisticsService.updateById(logistics);

            // 记录日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                    "京东物流单号：" + expressNumber,
                    "system");
        }

        // 创建发货通知任务
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("logistics_id", logistics.getId());
        String content = JSON.toJSONString(contentMap);

        TaskRecord task = taskRecordService.getOneByCondition(logistics.getLogisticsSn(),
                TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType(),
                content
        );

        if (ObjectUtils.isEmpty(task)) {
            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType());
            taskRecordDTO.setReferSn(logistics.getLogisticsSn());
            taskRecordDTO.setNotifyContent(content);

            TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP).addAndPush(taskRecordDTO);
        }

    }
}
