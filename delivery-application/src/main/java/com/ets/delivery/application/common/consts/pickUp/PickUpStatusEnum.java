package com.ets.delivery.application.common.consts.pickUp;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum PickUpStatusEnum {

    //取件状态[-1-取消 0-默认 1-下单成功 2-下单失败 3-待上门取件 4-取件完成 5-取件妥投 6-取件异常 7-取件终止]
    STATUS_CANCEL(-1, "取消"),
    STATUS_DEFAULT(0, "默认"),
    STATUS_CREATE_SUCCESS(1, "下单成功"),
    STATUS_CREATE_FAIL(2, "下单失败"),
    STATUS_WAIT_PICKUP(3, "待上门取件"),
    STATUS_PICKUP_FINISH(4, "取件完成"),
    STATUS_PICKUP_DELIVERED(5, "取件妥投"),
    STATUS_PICKUP_ERROR(6, "取件异常"),
    STATUS_PICKUP_TERMINAL(7, "取件终止");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    public static final List<Integer> list;

    static {
        PickUpStatusEnum[] enums = PickUpStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Arrays.stream(enums).map(PickUpStatusEnum::getValue).collect(Collectors.toList());
    }
}
