package com.ets.delivery.application.infra.service;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.mapper.ExWarehouseMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 销售出库单列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Service
@DS("db-issuer-admin-proxy")
public class ExWarehouseService extends BaseService<ExWarehouseMapper, ExWarehouse> {

    public List<ExWarehouse> getNeedQueryDeliveryList(String storageCode, Integer days) {
        Wrapper<ExWarehouse> wrapper = Wrappers.<ExWarehouse>lambdaQuery()
                .eq(ExWarehouse::getStorageCode, storageCode)
                .eq(ExWarehouse::getSplitOrderNo, "")
                .ne(ExWarehouse::getDeliverOrderNo, "")
                .notIn(ExWarehouse::getCurrentStatus, Arrays.asList(10028, 10034, 10037, 10038))
                .gt(ExWarehouse::getCreatedAt, LocalDateTime.now().minusDays(days))
                .last("limit 3000");
        return this.baseMapper.selectList(wrapper);
    }

    public ExWarehouse getOneByIsvUuid(String isvUuid) {
        Wrapper<ExWarehouse> wrapper = Wrappers.<ExWarehouse>lambdaQuery()
                .eq(ExWarehouse::getIsvUuid, isvUuid)
                .eq(ExWarehouse::getSplitOrderNo, "")
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public ExWarehouse getOneByWayBill(String wayBill) {
        Wrapper<ExWarehouse> wrapper = Wrappers.<ExWarehouse>lambdaQuery()
                .eq(ExWarehouse::getWayBill, wayBill)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public ExWarehouse getOneBySplitOrderNo(String splitOrderNo) {
        Wrapper<ExWarehouse> wrapper = Wrappers.<ExWarehouse>lambdaQuery()
                .eq(ExWarehouse::getSplitOrderNo, splitOrderNo)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public List<ExWarehouse> getSplitOrderList(String deliverOrderNo) {
        Wrapper<ExWarehouse> wrapper = Wrappers.<ExWarehouse>lambdaQuery()
                .eq(ExWarehouse::getDeliverOrderNo, deliverOrderNo)
                .eq(ExWarehouse::getSplitFlag, 0);
        return this.baseMapper.selectList(wrapper);
    }
}
