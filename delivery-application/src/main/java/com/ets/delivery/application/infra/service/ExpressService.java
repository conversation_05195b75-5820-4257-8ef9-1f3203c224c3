package com.ets.delivery.application.infra.service;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.Express;
import com.ets.delivery.application.infra.mapper.ExpressMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 物流单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Service
@DS("db-service")
public class ExpressService extends BaseService<ExpressMapper, Express> {

    public Express getOneByExpressNumber(String expressNumber) {
        Wrapper<Express> wrapper = Wrappers.<Express>lambdaQuery()
                .eq(Express::getExpressNumber, expressNumber)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public List<Express> getListByExpressNumber(List<String> expressNumberList) {
        Wrapper<Express> wrapper = Wrappers.<Express>lambdaQuery()
                .in(Express::getExpressNumber, expressNumberList);
        return this.baseMapper.selectList(wrapper);
    }

    public Express getByNumberAndPhone(String number, String phone) {
        LambdaQueryWrapper<Express> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Express::getExpressNumber, number)
                .eq(Express::getPhone, phone);

        return getOneByWrapper(wrapper);
    }

}
