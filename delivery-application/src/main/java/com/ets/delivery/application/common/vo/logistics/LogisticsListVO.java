package com.ets.delivery.application.common.vo.logistics;

import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.logistics.*;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.infra.entity.User;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Data
public class LogisticsListVO {

    private Integer id;

    private Integer issuerId;
    private String issuerIdStr;

    private String logisticsSn;

    private String orderSn;

    private String plateNo;

    private String orderType;
    private String orderTypeStr;

    private String orderSource;

    private String sendName;

    private String sendPhone;

    private String sendArea;

    private String sendAddress;

    private String expressNumber;

    private String expressCorp;

    private Integer expressStatus;
    private String expressStatusStr;

    private Integer status;
    private String statusStr;

    private Integer deliveryStatus;
    private String deliveryStatusStr;

    private Integer notifyStatus;
    private String notifyStatusStr;

    private String operator;

    private String storageCode;
    private String storageCodeStr;

    private String deliveryRemark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deliveryTime;

    private List<LogisticsGoodsVO> goodsList;

    private Boolean showAllowGetButton;
    private Boolean showCancelGetButton;
    private Boolean showJdDeliveryButton = false;
    private Boolean showNormalDeliveryButton = false;
    private Boolean showDeliveryDetailButton;
    private Boolean showDeliveryButton;
    private Boolean showStopDeliveryButton;
    private Boolean showNotifyButton;
    private Boolean showRecoverDeliveryButton;
    private Boolean showPushButton;
    // 手动发货按钮，仅限开发测试
    private Boolean showManualDeliveryButton = false;
    private Boolean showManualAllowGetButton = false;

    public String getOrderTypeStr() {
        return LogisticsOrderTypeEnum.map.getOrDefault(orderType, "--");
    }

    public String getDeliveryStatusStr() {
        return LogisticsDeliveryStatusEnum.map.getOrDefault(deliveryStatus, "--");
    }

    public String getNotifyStatusStr() {
        return LogisticsNotifyStatusEnum.map.getOrDefault(notifyStatus, "--");
    }

    public String getExpressStatusStr() {
        return LogisticsExpressStatusEnum.map.getOrDefault(expressStatus, "--");
    }

    public String getStatusStr() {
        return LogisticsStatusEnum.map.getOrDefault(status, "--");
    }

    public String getStorageCodeStr() {
        return StorageCodeEnum.map.getOrDefault(storageCode, storageCode);
    }

    public Boolean getShowAllowGetButton() {
        return !Arrays.asList(StorageCodeEnum.JD_CLOUD.getValue(), StorageCodeEnum.YUNDA.getValue()).contains(storageCode) &&
                status.equals(LogisticsStatusEnum.STATUS_NORMAL.getValue()) &&
                StringUtils.isEmpty(operator) &&
                Arrays.asList(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),
                        LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue()).contains(deliveryStatus);
    }

    public Boolean getShowCancelGetButton() {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        return !Arrays.asList(StorageCodeEnum.JD_CLOUD.getValue(), StorageCodeEnum.YUNDA.getValue()).contains(storageCode) &&
                status.equals(LogisticsStatusEnum.STATUS_NORMAL.getValue()) &&
                ObjectUtils.isNotEmpty(user) &&
                user.getUsername().equals(operator) &&
                deliveryStatus.equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue()) &&
                notifyStatus.equals(LogisticsNotifyStatusEnum.NOTIFY_STATUS_WAIT.getValue());
    }

    public Boolean getShowDeliveryDetailButton() {
        return storageCode.equals(StorageCodeEnum.JD_CLOUD.getValue()) &&
                status.equals(LogisticsStatusEnum.STATUS_NORMAL.getValue()) &&
                deliveryStatus.equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue());
    }

    public Boolean getShowDeliveryButton() {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        return !Arrays.asList(StorageCodeEnum.JD_CLOUD.getValue(), StorageCodeEnum.YUNDA.getValue()).contains(storageCode) &&
                status.equals(LogisticsStatusEnum.STATUS_NORMAL.getValue()) &&
                ObjectUtils.isNotEmpty(user) &&
                user.getUsername().equals(operator) &&
                Arrays.asList(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),
                        LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue()).contains(deliveryStatus);
    }

    public Boolean getShowStopDeliveryButton() {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        return !Arrays.asList(StorageCodeEnum.JD_CLOUD.getValue(), StorageCodeEnum.YUNDA.getValue()).contains(storageCode) &&
                status.equals(LogisticsStatusEnum.STATUS_NORMAL.getValue()) &&
                ObjectUtils.isNotEmpty(user) &&
                user.getUsername().equals(operator) &&
                deliveryStatus < LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue();
    }

    public Boolean getShowNotifyButton() {
        return status.equals(LogisticsStatusEnum.STATUS_NORMAL.getValue()) &&
                deliveryStatus.equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue()) &&
                Arrays.asList(LogisticsNotifyStatusEnum.NOTIFY_STATUS_WAIT.getValue(),
                                LogisticsNotifyStatusEnum.NOTIFY_STATUS_FAIL.getValue()).contains(notifyStatus);
    }

    public Boolean getShowRecoverDeliveryButton() {
        return !Arrays.asList(StorageCodeEnum.JD_CLOUD.getValue(), StorageCodeEnum.YUNDA.getValue()).contains(storageCode) &&
                status.equals(LogisticsStatusEnum.STATUS_STOP.getValue()) &&
                deliveryStatus < LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue();
    }

    public Boolean getShowPushButton() {
        return deliveryStatus.equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue()) &&
                notifyStatus.equals(LogisticsNotifyStatusEnum.NOTIFY_STATUS_PROCESSING.getValue());
    }
}
