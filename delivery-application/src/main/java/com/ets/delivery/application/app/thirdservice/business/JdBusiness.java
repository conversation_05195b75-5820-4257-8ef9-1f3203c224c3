package com.ets.delivery.application.app.thirdservice.business;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.thirdservice.feign.JdOpenApiFeign;
import com.ets.delivery.application.app.thirdservice.request.jd.*;
import com.ets.delivery.application.app.thirdservice.response.jd.*;
import com.ets.delivery.application.common.config.jd.JdConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class JdBusiness {

    @Autowired
    private JdConfig jdConfig;

    @Autowired
    private JdOpenApiFeign jdOpenApiFeign;

    public JdStandardCalendarVO standardCalendar(JdStandardCalendarDTO standardCalendarDTO) {
        String body = JSON.toJSONString(standardCalendarDTO);
        String timestamp = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
        String method = "/api/MOGU/standardCalendar";
        String lopDn = "express";
        String sign = getSign(method, timestamp, body);
        log.info("【京东】【{}】预约日历请求参数：{} 签名：{}", method, body, sign);
        String result = jdOpenApiFeign.StandardCalendar(
                body,
                jdConfig.getAppKey(),
                jdConfig.getAccessToken(),
                timestamp,
                jdConfig.getV(),
                lopDn,
                sign
        );
        log.info("【京东】【{}】预约日历返回结果：{}", method, result);
        JdStandardCalendarVO standardCalendarVO = JSON.parseObject(result, JdStandardCalendarVO.class);
        if (ObjectUtils.isEmpty(standardCalendarVO)) {
            log.error("【京东】预约日历返回结果null");
            ToolsHelper.throwException("【京东】预约日历返回结果空");
        }
        if (!standardCalendarVO.getCode().equals(1)) {
            String message = standardCalendarVO.getMessage();
            log.warn("【京东】预约日历获取失败：{}", message);
            ToolsHelper.throwException(message);
        }
        return standardCalendarVO;
    }

    public JdCheckBlinkAreaVO checkBlinkArea(JdCheckBlinkAreaDTO checkBlinkAreaDTO) {
        String body = JSON.toJSONString(checkBlinkAreaDTO);
        String timestamp = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
        String method = "/api/MOGU/checkBlindArea";
        String lopDn = "express";
        String sign = getSign(method, timestamp, body);
        log.info("【京东】【{}】超区校验请求参数：{} 签名：{}", method, body, sign);
        String result = jdOpenApiFeign.CheckBlindArea(
                body,
                jdConfig.getAppKey(),
                jdConfig.getAccessToken(),
                timestamp,
                jdConfig.getV(),
                lopDn,
                sign
        );
        log.info("【京东】【{}】超区校验返回结果：{}", method, result);
        JdCheckBlinkAreaVO checkBlinkAreaVO = JSON.parseObject(result, JdCheckBlinkAreaVO.class);
        if (ObjectUtils.isEmpty(checkBlinkAreaVO)) {
            log.error("【京东】超区校验返回结果null");
            ToolsHelper.throwException("【京东】超区校验返回结果null");
        }
        if (!checkBlinkAreaVO.getResult().equals("1")) {
            String message = checkBlinkAreaVO.getError_msg();
            log.warn("【京东】超区校验失败：{}", message);
            ToolsHelper.throwException(message);
        }
        return checkBlinkAreaVO;
    }

    public JdQueryEstimatedFreightsVO queryFreights(JdQueryEstimatedFreightsDTO queryEstimatedFreightsDTO) {
        List<JdQueryEstimatedFreightsDTO> dto = Collections.singletonList(queryEstimatedFreightsDTO);
        String body = JSON.toJSONString(dto);
        String timestamp = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
        String method = "/query/estimatedfreights";
        String lopDn = "express";
        String sign = getSign(method, timestamp, body);
        log.info("【京东】【{}】查询预估运费和时效请求参数：{} 签名：{}", method, body, sign);
        String result = jdOpenApiFeign.QueryEstimatedFreights(
                body,
                jdConfig.getAppKey(),
                jdConfig.getAccessToken(),
                timestamp,
                jdConfig.getV(),
                lopDn,
                sign
        );
        log.info("【京东】【{}】查询预估运费和时效返回结果：{}", method, result);
        JdQueryEstimatedFreightsVO queryEstimatedFreightsVO = JSON.parseObject(result, JdQueryEstimatedFreightsVO.class);
        if (ObjectUtils.isEmpty(queryEstimatedFreightsVO)) {
            log.error("【京东】查询预估运费和时效返回结果null");
            ToolsHelper.throwException("【京东】查询预估运费和时效返回结果null");
        }
        if (!queryEstimatedFreightsVO.getStatusCode().equals(0)) {
            String message = queryEstimatedFreightsVO.getStatusMessage();
            log.warn("【京东】查询预估运费和时效失败：{}", message);
            ToolsHelper.throwException(message);
        }
        return queryEstimatedFreightsVO;
    }

    public JdPickUpOrderCreateVO receivePickUpOrder(JdPickUpOrderCreateDTO pickUpOrderCreateDTO) {
        List<JdPickUpOrderCreateDTO> dto = Collections.singletonList(pickUpOrderCreateDTO);
        String body = JSON.toJSONString(dto);
        String timestamp = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
        String method = "/PickupReceiveApi/receivePickUpOrder";
        String lopDn = "express";
        String sign = getSign(method, timestamp, body);
        log.info("【京东】【{}】取件单下单请求参数：{} 签名：{}", method, body, sign);
        String result = jdOpenApiFeign.ReceivePickUpOrder(
                body,
                jdConfig.getAppKey(),
                jdConfig.getAccessToken(),
                timestamp,
                jdConfig.getV(),
                lopDn,
                sign
        );
        log.info("【京东】【{}】取件单下单返回结果：{}", method, result);
        JdPickUpOrderCreateVO pickUpOrderCreateVO = JSON.parseObject(result, JdPickUpOrderCreateVO.class);
        if (ObjectUtils.isEmpty(pickUpOrderCreateVO)) {
            log.error("【京东】取件单下单返回结果null");
            ToolsHelper.throwException("【京东】取件单下单返回结果null");
        }
        if (!pickUpOrderCreateVO.getCode().equals(100)) {
            String message = pickUpOrderCreateVO.getMesssage();
            log.error("【京东】取件单下单失败：{}", message);
            ToolsHelper.throwException(message);
        }
        return pickUpOrderCreateVO;
    }

    public JdPickUpOrderCancelVO pickUpCancel(JdPickUpOrderCancelDTO pickUpOrderCancelDTO) {
        List<JdPickUpOrderCancelDTO> dto = Collections.singletonList(pickUpOrderCancelDTO);
        String body = JSON.toJSONString(dto);
        String timestamp = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
        String method = "/pickupordercancel";
        String lopDn = "express";
        String sign = getSign(method, timestamp, body);
        log.info("【京东】【{}】取消取件单请求参数：{} 签名：{}", method, body, sign);
        String result = jdOpenApiFeign.PickupOrderCancel(
                body,
                jdConfig.getAppKey(),
                jdConfig.getAccessToken(),
                timestamp,
                jdConfig.getV(),
                lopDn,
                sign
        );
        log.info("【京东】【{}】取消取件单返回结果：{}", method, result);
        JdPickUpOrderCancelVO pickUpOrderCancelVO = JSON.parseObject(result, JdPickUpOrderCancelVO.class);
        if (ObjectUtils.isEmpty(pickUpOrderCancelVO)) {
            log.error("【京东】取消取件单返回结果null");
            ToolsHelper.throwException("【京东】取消取件单返回结果null");
        }
        if (!pickUpOrderCancelVO.getStatusCode().equals(0)) {
            String message = pickUpOrderCancelVO.getStatusMessage();
            log.error("【京东】取消取件单下单失败：{}", message);
            ToolsHelper.throwException(message);
        }
        return pickUpOrderCancelVO;
    }

    public JdQueryTraceInfoVO queryDynamicTraceInfo(JdQueryTraceInfoDTO jdQueryTraceInfoDTO) {
        List<String> dto = new ArrayList<>();
        dto.add(jdQueryTraceInfoDTO.getCustomerCode());
        dto.add(jdQueryTraceInfoDTO.getWaybillCode());
        dto.add(jdQueryTraceInfoDTO.getJosPin());
        String body = JSON.toJSONString(dto);
        String timestamp = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
        String method = "/query/dynamictraceinfo";
        String lopDn = "express";
        String sign = getSign(method, timestamp, body);
        log.info("【京东】【{}】查询物流轨迹请求参数：{} 签名：{}", method, body, sign);
        String result = jdOpenApiFeign.QueryDynamicTraceInfo(
                body,
                jdConfig.getAppKey(),
                jdConfig.getAccessToken(),
                timestamp,
                jdConfig.getV(),
                lopDn,
                sign
        );
        log.info("【京东】【{}】查询物流轨迹返回结果：{}", method, result);
        JdQueryTraceInfoVO queryTraceInfoVO = JSON.parseObject(result, JdQueryTraceInfoVO.class);
        if (ObjectUtils.isEmpty(queryTraceInfoVO)) {
            log.error("【京东】查询物流轨迹返回结果null");
            ToolsHelper.throwException("【京东】查询物流轨迹返回结果null");
        }
        if (!queryTraceInfoVO.getCode().equals(100)) {
            String message = queryTraceInfoVO.getMsg();
            log.error("【京东】查询物流轨迹失败：{}", message);
            ToolsHelper.throwException(message);
        }
        return queryTraceInfoVO;
    }

    private String getSign(String method, String timestamp, String paramJson) {
        String content = String.join("", new String[]{
                jdConfig.getAppSecret(),
                "access_token", jdConfig.getAccessToken(),
                "app_key", jdConfig.getAppKey(),
                "method", method,
                "param_json", paramJson,
                "timestamp", timestamp,
                "v", "2.0",
                jdConfig.getAppSecret()});
        return SecureUtil.md5(content);
    }
}
