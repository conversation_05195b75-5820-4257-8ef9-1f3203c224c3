package com.ets.delivery.application.common.consts.pickUp;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;


@AllArgsConstructor
@Getter
public enum PickUpSmsStatusEnum {

    //短信状态[0-无需发送 1-待发送 2-发送成功 3-发送失败]
    NO_NEED_SEND(0, "无需发送"),
    WAIT_SEND(1, "待发送"),
    SEND_SUCCESS(2, "发送成功"),
    SEND_FAIL(3, "发送失败");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        PickUpSmsStatusEnum[] enums = PickUpSmsStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
