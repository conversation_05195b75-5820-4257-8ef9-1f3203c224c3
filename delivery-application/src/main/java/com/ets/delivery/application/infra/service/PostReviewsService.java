package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.bo.PostReviewListBO;
import com.ets.delivery.application.common.consts.postReviews.PostReviewExceptionStatusEnum;
import com.ets.delivery.application.common.consts.postReviews.PostReviewStatusEnum;
import com.ets.delivery.application.common.dto.postReviews.PostReviewAbnormalListDTO;
import com.ets.delivery.application.common.dto.postReviews.PostReviewListDTO;
import com.ets.delivery.application.common.vo.postReviews.PostReviewDateCountVO;
import com.ets.delivery.application.infra.entity.PostReviews;
import com.ets.delivery.application.infra.mapper.PostReviewsMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 自动审核后审记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
@DS("db-issuer-admin")
public class PostReviewsService extends BaseService<PostReviewsMapper, PostReviews> {

    @Autowired
    private PostReviewsMapper mapper;

    public IPage<PostReviews> getDefaultPage(PostReviewListDTO listDTO) {
        Wrapper<PostReviews> wrapper = Wrappers.<PostReviews>lambdaQuery()
                .eq(listDTO.getChannel() != null, PostReviews::getChannel, listDTO.getChannel())
                .eq(StringUtils.isNotEmpty(listDTO.getOrderSn()), PostReviews::getOrderSn, listDTO.getOrderSn())
                .eq(StringUtils.isNotEmpty(listDTO.getPlateNo()), PostReviews::getPlateNo, listDTO.getPlateNo())
                .eq(listDTO.getEmergencyType() != null, PostReviews::getEmergencyType, listDTO.getEmergencyType())
                .in(PostReviews::getReviewStatus, Arrays.asList(
                        PostReviewStatusEnum.REVIEW_STATUS_DEFAULT.getValue(),
                        PostReviewStatusEnum.REVIEW_STATUS_PROCESSING.getValue()
                    )
                )
                .ge(ObjectUtils.isNotEmpty(listDTO.getCreateStartTime()), PostReviews::getCreatedAt, listDTO.getCreateStartTime())
                .le(ObjectUtils.isNotEmpty(listDTO.getCreateEndTime()), PostReviews::getCreatedAt, listDTO.getCreateEndTime())
                .orderBy(true, listDTO.getSort().equals("asc"), PostReviews::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }

    public IPage<PostReviews> getAbnormalPage(PostReviewAbnormalListDTO listDTO) {
        Wrapper<PostReviews> wrapper = Wrappers.<PostReviews>lambdaQuery()
                .eq(StringUtils.isNotEmpty(listDTO.getOrderSn()), PostReviews::getOrderSn, listDTO.getOrderSn())
                .eq(StringUtils.isNotEmpty(listDTO.getPlateNo()), PostReviews::getPlateNo, listDTO.getPlateNo())
                .eq(listDTO.getIssuerId() != null, PostReviews::getIssuerId, listDTO.getIssuerId())
                .eq(listDTO.getEmergencyType() != null, PostReviews::getEmergencyType, listDTO.getEmergencyType())
                .eq(PostReviews::getReviewStatus, PostReviewStatusEnum.REVIEW_STATUS_EXCEPTION.getValue())
                .ne(PostReviews::getExceptionStatus, PostReviewExceptionStatusEnum.EXCEPTION_STATUS_DEFAULT.getValue())
                .orderByAsc(PostReviews::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }

    public List<PostReviews> getDefaultListLimitDate(PostReviewListBO listBO) {
        Wrapper<PostReviews> wrapper = Wrappers.<PostReviews>lambdaQuery()
                .eq(PostReviews::getReviewStatus, PostReviewStatusEnum.REVIEW_STATUS_DEFAULT.getValue())
                .eq(PostReviews::getOperator, "")
                .eq(listBO.getEmergencyType() != null, PostReviews::getEmergencyType, listBO.getEmergencyType())
                .gt(PostReviews::getCreatedAt, LocalDateTime.now().minusDays(listBO.getDaysRange()))
                .orderByAsc(PostReviews::getCreatedAt);
        return this.baseMapper.selectList(wrapper);
    }

    public List<PostReviews> getOverTimeList(long hour) {
        Wrapper<PostReviews> wrapper = Wrappers.<PostReviews>lambdaQuery()
                .eq(PostReviews::getReviewStatus, PostReviewStatusEnum.REVIEW_STATUS_PROCESSING.getValue())
                .lt(PostReviews::getDrawTime, LocalDateTime.now().minusHours(hour));
        return this.baseMapper.selectList(wrapper);
    }

    public PostReviews getOneByReviewSn(String reviewSn) {
        Wrapper<PostReviews> wrapper = Wrappers.<PostReviews>lambdaQuery()
                .eq(PostReviews::getReviewSn, reviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public List<PostReviewDateCountVO> getAllDefaultGroupByDate() {
        return mapper.getAllDefaultGroupByDate(LocalDate.now());
    }

    public List<PostReviews> getAllReviewedRecords(LocalDateTime s, LocalDateTime e) {
        Wrapper<PostReviews> wrapper = Wrappers.<PostReviews>lambdaQuery()
                .in(PostReviews::getReviewStatus, Arrays.asList(
                        PostReviewStatusEnum.REVIEW_STATUS_PASS.getValue(),
                        PostReviewStatusEnum.REVIEW_STATUS_EXCEPTION.getValue()
                ))
                .ge(PostReviews::getReviewTime, s)
                .le(PostReviews::getReviewTime, e);
        return this.baseMapper.selectList(wrapper);
    }

}
