package com.ets.delivery.application.common.consts.reviews;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum ReviewReviewStatusEnum {

    REVIEW_STATUS_WAIT(0, "待审核"),
    REVIEW_STATUS_PROCESSING(1, "审核中"),
    REVIEW_STATUS_PASS(2, "审核通过"),
    REVIEW_STATUS_REFUSE(3, "审核拒绝"),
    REVIEW_STATUS_CANCEL(4, "审核取消");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    ReviewReviewStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        ReviewReviewStatusEnum[] enums = ReviewReviewStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
