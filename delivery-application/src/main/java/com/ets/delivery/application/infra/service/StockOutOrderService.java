package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.thirdservice.response.yunda.StockResponseVO;
import com.ets.delivery.application.common.consts.stock.StockOutStatusEnum;
import com.ets.delivery.application.common.dto.stock.StockOutEditDTO;
import com.ets.delivery.application.infra.entity.StockOutOrder;
import com.ets.delivery.application.infra.mapper.StockOutOrderMapper;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 出库单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Service
@DS("db-issuer-admin-minor")
public class StockOutOrderService extends BaseService<StockOutOrderMapper, StockOutOrder> {

    @Override
    public int updateByWrapper(LambdaUpdateWrapper<StockOutOrder> lambdaUpdateWrapper) {
        lambdaUpdateWrapper.set(StockOutOrder::getUpdatedAt, ToolsHelper.getDateTime());

        return super.updateByWrapper(lambdaUpdateWrapper);
    }

    public void edit(StockOutEditDTO dto) {

        LambdaUpdateWrapper<StockOutOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StockOutOrder::getStockOutSn, dto.getStockOutSn())
                .set(dto.getRemark() != null, StockOutOrder::getRemark, dto.getRemark())
                .set(dto.getImages() != null, StockOutOrder::getImages, dto.getImages());

        updateByWrapper(wrapper);
    }

    public StockOutOrder getBySn(String stockOutSn) {

        return getOneByColumn(stockOutSn, StockOutOrder::getStockOutSn);
    }

    public void cancel(String stockOutSn) {

        LambdaUpdateWrapper<StockOutOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StockOutOrder::getStockOutSn, stockOutSn)
                .set(StockOutOrder::getStatus, StockOutStatusEnum.CANCELED.getCode());

        updateByWrapper(wrapper);
    }

    public void updateStatus(String stockOutSn, Integer status, String outTime, String expressCode, String extra) {

        LambdaUpdateWrapper<StockOutOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StockOutOrder::getStockOutSn, stockOutSn)
                .set(StockOutOrder::getStatus, status)
                .set(StringUtils.isNotEmpty(outTime), StockOutOrder::getOutTime, outTime)
                .set(StringUtils.isNotEmpty(expressCode), StockOutOrder::getExpressCode, expressCode)
                .set(StockOutOrder::getExtra, extra);

        updateByWrapper(wrapper);
    }

    public void applySuccess(String stockOutSn, StockResponseVO response) {

        LambdaUpdateWrapper<StockOutOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StockOutOrder::getStockOutSn, stockOutSn)
                .set(StockOutOrder::getApplyTime, ToolsHelper.getDateTime())
                .set(StockOutOrder::getStatus, StockOutStatusEnum.NEW.getCode());

        updateByWrapper(wrapper);
    }

}
