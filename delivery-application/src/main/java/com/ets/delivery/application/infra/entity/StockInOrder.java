package com.ets.delivery.application.infra.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import com.ets.delivery.application.common.bo.stock.StockInExtraBO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 入库单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_stock_in_order")
public class StockInOrder extends BaseEntity<StockInOrder> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 入库单号
     */
    private String stockInSn;

    /**
     * 库存编号
     */
    private String storageCode;

    /**
     * 入库类型
     */
    private Integer type;

    /**
     * 商品属性
     */
    private Integer goodsQuality;

    /**
     * 入库状态
     * NEW-未开始处理,  ACCEPT-仓库接单 , PARTFULFILLED-部分收货完成,  FULFILLED-收货完成,  EXCEPTION-异常,  CANCELED-取消,  CLOSED-关闭,  REJECT-拒单,  CANCELEDFAIL-取消失败
     */
    private Integer status;

    /**
     * 实际入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inTime;

    /**
     * 申请入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 图片，逗号分隔
     */
    private String images;

    private String extra;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    public List<String> getImageList() {

        if (StringUtils.isEmpty(images)) {
            return null;
        }

        return Arrays.asList(images.split(","));
    }

    /**
     * 商品信息
     */
    @TableField(exist = false)
    private List<StockGoodsInfo> goodsInfo;

    public StockInExtraBO getExtraBO() {

        if (StringUtils.isNotEmpty(extra)) {

            return JSON.parseObject(extra, StockInExtraBO.class);
        } else {
            return new StockInExtraBO();
        }
    }


}
