package com.ets.delivery.application.common.dto.riskreview;

import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 风控审核提交DTO
 */
@Data
public class RiskReviewSubmitDTO {

    /**
     * 风控单号
     */
    @NotBlank(message = "风控单号不能为空")
    private String riskReviewSn;

    /**
     * 风控审核状态[0-待审核 1-审核通过 2-审核驳回取消 3-审核驳回重新上传 4-补传资料 5-审核取消]
     */
    @NotNull(message = "审核状态不能为空")
    private Integer riskReviewStatus;

    /**
     * 驳回原因id
     */
    private Integer rejectReasonId;

    private String rejectReason;

    /**
     * 是否需要获取下一条待审核数据
     */
    private Boolean needNext = false;

    /**
     * 验证审核状态是否有效
     */
    @AssertTrue(message = "审核状态不正确")
    public boolean isValidReviewStatus() {
        return RiskReviewStatusEnum.isValidValue(this.riskReviewStatus);
    }

    /**
     * 验证驳回状态时必须提供驳回原因
     */
    @AssertTrue(message = "审核驳回时必须选择驳回原因")
    public boolean isValidRejectReason() {
        // 如果是驳回状态，必须提供驳回原因ID
        if (riskReviewStatus != null &&
            (riskReviewStatus.equals(RiskReviewStatusEnum.REJECTED_CANCEL.getValue()) ||
             riskReviewStatus.equals(RiskReviewStatusEnum.REJECTED_REUPLOAD.getValue()) ||
             riskReviewStatus.equals(RiskReviewStatusEnum.SUPPLEMENT_MATERIAL.getValue()))) {
            return rejectReasonId != null && rejectReasonId > 0;
        }
        return true;
    }
}
