package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.fallback.RefundFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        url = "${microUrls.saleafter:http://etc-saleafter-main:6610}",
        name = "saleafterMainRefundFeign",
        contextId = "refund",
        path = "/saleafter/refunds",
        fallbackFactory = RefundFallbackFactory.class
)
public interface RefundFeign {

    @RequestMapping("/subscribe-failed")
    JsonResult<?> subscribeFailed(@RequestParam("order_sn") String orderSn);
}
