package com.ets.delivery.application.common.utils;

import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.infra.entity.User;

public class AdminUserUtil {

    public static User getOperator() {

        return (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
    }

    public static String getOperatorName() {

        return getOperator().getRealName();
    }

}
