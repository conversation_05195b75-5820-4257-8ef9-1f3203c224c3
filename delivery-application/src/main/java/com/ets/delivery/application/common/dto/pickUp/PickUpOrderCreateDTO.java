package com.ets.delivery.application.common.dto.pickUp;

import com.ets.delivery.feign.request.pickup.PickupGoodsBO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class PickUpOrderCreateDTO {
    @NotEmpty(message = "业务订单号不能为空")
    String orderSn;

    @NotEmpty(message = "订单类型不能为空")
    String orderType;

    @NotEmpty(message = "订单来源不能为空")
    String orderSource = "admin";

    String plateNo;

    @NotNull(message = "商品不能为空")
    List<PickupGoodsBO> goodsList;

    @NotNull(message = "预约开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime pickUpStartTime;

    @NotNull(message = "预约结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime pickUpEndTime;

    @NotEmpty(message = "寄件人姓名不能为空")
    String sendName;

    @NotEmpty(message = "寄件人省市区不能为空")
    String sendArea;

    @NotEmpty(message = "寄件人地址不能为空")
    String sendAddress;

    @NotEmpty(message = "寄件人手机号码不能为空")
    String sendMobile;

    @NotEmpty(message = "收件人姓名不能为空")
    String receiveName;

    @NotEmpty(message = "收件人手机号码不能为空")
    String receiveMobile;

    @NotEmpty(message = "收件人省市区不能为空")
    String receiveArea;

    @NotEmpty(message = "收件人地址不能为空")
    String receiveAddress;

    @NotEmpty(message = "取件原因不能为空")
    @Size(max = 100, message = "取件原因不能超过100个字")
    String pickUpReason;

    String remark;
    String desp = "ETC设备，内含电池";
    Double weight = 0.50;
    Double volume = 360.00;

    @Data
    public static class Goods {
        @NotEmpty(message = "商品编码不能为空")
        String goodsCode;

        String goodsName;

        @NotNull(message = "商品数量不能为空")
        Integer quantity = 1;

        Integer deviceType = 0;
    }
}
