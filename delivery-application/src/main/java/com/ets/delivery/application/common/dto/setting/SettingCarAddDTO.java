package com.ets.delivery.application.common.dto.setting;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class SettingCarAddDTO {

    private Integer type = 1;

    /**
     * 大类别
     */
    @NotBlank(message = "category不能为空")
    private String category;

    /**
     * 键值key
     */
    @NotBlank(message = "key不能为空")
    private String key;

    /**
     * 渠道
     */
    @NotBlank(message = "错误码不能为空")
    private String value;

    /**
     * 操作参数
     */
    @NotBlank(message = "错误语不能为空")
    private String params;

    /**
     * 引导
     */
    private String guide = "default";

    /**
     * 排序
     */
    private Integer sort = 0;

}