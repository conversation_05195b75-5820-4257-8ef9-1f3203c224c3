package com.ets.delivery.application.common.utils;

import com.nbf.component.aliyun.sdk.sign.Jetty9UrlEncodedCopy;
import com.nbf.component.aliyun.sdk.sign.constants.enums.DigestAlgorithmEnum;
import com.nbf.component.aliyun.sdk.sign.constants.enums.HmacAlgorithmEnum;
import com.nbf.component.aliyun.sdk.sign.constants.enums.SignProtocolEnum;
import jakarta.servlet.http.HttpServletRequest;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class PopSignValidator {
    private static Map<String, String> akSkMap = new HashMap(4);
    private static final ThreadLocal<MessageDigest> LOCAL_DIGEST = ThreadLocal.withInitial(() -> {
        try {
            return MessageDigest.getInstance(DigestAlgorithmEnum.SHA_256.getValue());
        } catch (NoSuchAlgorithmException var1) {
            throw new RuntimeException(var1);
        }
    });
    private static final ThreadLocal<Mac> LOCAL_HMAC = ThreadLocal.withInitial(() -> {
        try {
            return Mac.getInstance(HmacAlgorithmEnum.HMAC_SHA_256.getValue());
        } catch (NoSuchAlgorithmException var1) {
            throw new RuntimeException(var1);
        }
    });
    private static final char[] DIGITS_LOWER = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    public PopSignValidator() {
    }

    public static void addAccessKey(String accessKeyId, String accessKeySecret) {
        if (accessKeyId != null && accessKeySecret != null) {
            akSkMap.put(accessKeyId, accessKeySecret);
        } else {
            throw new RuntimeException("参数为空");
        }
    }

    public static void clearAccessKey() {
        akSkMap.clear();
    }

    public static void validateSignature(HttpServletRequest request) {
        if (request == null) {
            throw new RuntimeException("param: request is null");
        } else if (akSkMap.isEmpty()) {
            throw new RuntimeException("accessKeyId / accessKeySecret have not been set");
        } else {
            String authorization = request.getHeader("Authorization").trim();
            String acs3HmacSha256 = SignProtocolEnum.ACS3_HMAC_SHA256.getValue();
            String[] elementsArr = authorization.substring(authorization.indexOf(acs3HmacSha256) + acs3HmacSha256.length() + 1).split(",");
            HashMap<String, String> signingElements = new HashMap(4);
            String[] var5 = elementsArr;
            int var6 = elementsArr.length;

            String clientSignature;
            for(int var7 = 0; var7 < var6; ++var7) {
                clientSignature = var5[var7];
                String[] arr = clientSignature.split("=", -1);
                signingElements.put(arr[0].trim(), arr[1].trim());
            }

            String credential = (String)signingElements.get("Credential");
            String accessKeySecret = (String)akSkMap.get(credential);
            if (accessKeySecret == null) {
                throw new RuntimeException("invalid accessKeyId=" + credential);
            } else {
                String signedHeaders = (String)signingElements.get("SignedHeaders");
                if (signedHeaders == null) {
                    signedHeaders = "";
                }

                clientSignature = (String)signingElements.get("Signature");
                String canonicalRequest = buildCanonicalRequest(request, signedHeaders);
                String stringToSign = generateStringToSign(canonicalRequest);
                String serverSign = doSign(stringToSign, accessKeySecret);
                if (!serverSign.equals(clientSignature)) {
                    throw new RuntimeException("SignValidateFail. serverStringToSign=[" + stringToSign + "], serverCanonicalRequest=[" + canonicalRequest + "]");
                }
            }
        }
    }

    private static String generateStringToSign(String canonicalRequest) {
        return SignProtocolEnum.ACS3_HMAC_SHA256.getValue() + "\n" + hexEncodedHash(canonicalRequest.getBytes(StandardCharsets.UTF_8));
    }

    private static String doSign(String stringToSign, String accessKeySecret) {
        Mac mac = (Mac)LOCAL_HMAC.get();
        mac.reset();

        try {
            byte[] secret = accessKeySecret.getBytes(StandardCharsets.UTF_8);
            mac.init(new SecretKeySpec(secret, HmacAlgorithmEnum.HMAC_SHA_256.getValue()));
        } catch (InvalidKeyException var4) {
            throw new RuntimeException(var4);
        }

        return encodeHex(mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8)));
    }

    private static String buildCanonicalRequest(HttpServletRequest request, String signedHeaders) {
        return request.getMethod().toUpperCase() + "\n" + getCanonicalUri(request.getRequestURI()) + "\n" + getCanonicalQueryString(request.getQueryString()) + "\n" + getCanonicalHeaders(request, signedHeaders) + "\n" + signedHeaders + "\n" + request.getHeader("x-acs-content-sha256");
    }

    private static String getCanonicalQueryString(String queryString) {
        if (isBlank(queryString)) {
            return "";
        } else {
            Map<String, List<String>> queryParams = new HashMap();
            Jetty9UrlEncodedCopy.decodeUtf8To(queryString, queryParams);
            SortedMap<String, List<String>> sortedEncodedParams = new TreeMap();

            String encodedParamName;
            ArrayList encodedValues;
            for(Iterator var3 = queryParams.entrySet().iterator(); var3.hasNext(); sortedEncodedParams.put(encodedParamName, encodedValues)) {
                Map.Entry<String, List<String>> entry = (Map.Entry)var3.next();
                encodedParamName = percentEncodeParam((String)entry.getKey());
                List<String> paramValues = (List)entry.getValue();
                encodedValues = new ArrayList(paramValues.size());
                ArrayList finalEncodedValues = encodedValues;
                paramValues.forEach((value) -> {
                    finalEncodedValues.add(value == null ? "" : percentEncodeParam(value));
                });
                if (encodedValues.size() > 1) {
                    Collections.sort(encodedValues);
                }
            }

            StringBuilder result = new StringBuilder();
            sortedEncodedParams.forEach((key, values) -> {
                values.forEach((value) -> {
                    if (result.length() > 0) {
                        result.append("&");
                    }

                    result.append(key);
                    if (value != null) {
                        result.append("=");
                        result.append(value);
                    }

                });
            });
            return result.toString();
        }
    }

    private static String getCanonicalUri(String requestUri) {
        if (isBlank(requestUri)) {
            return "/";
        } else {
            try {
                return percentEncodeUri(URLDecoder.decode(requestUri, StandardCharsets.UTF_8.name()));
            } catch (UnsupportedEncodingException var2) {
                throw new RuntimeException(var2);
            }
        }
    }

    private static String getCanonicalHeaders(HttpServletRequest request, String signedHeaders) {
        if (isBlank(signedHeaders)) {
            return "";
        } else {
            String[] signedHeaderList = signedHeaders.split(";");
            StringBuilder result = new StringBuilder();
            String[] var4 = signedHeaderList;
            int var5 = signedHeaderList.length;

            for(int var6 = 0; var6 < var5; ++var6) {
                String headerName = var4[var6];
                result.append(headerName).append(":");
                Enumeration<String> headerValueEnum = request.getHeaders(headerName);
                if (headerValueEnum != null && headerValueEnum.hasMoreElements()) {
                    String headerValue = ((String)headerValueEnum.nextElement()).trim();
                    if (!headerValueEnum.hasMoreElements()) {
                        result.append(headerValue);
                    } else {
                        List<String> values = new ArrayList(4);
                        values.add(headerValue);

                        do {
                            values.add(((String)headerValueEnum.nextElement()).trim());
                        } while(headerValueEnum.hasMoreElements());

                        Collections.sort(values);
                        result.append(String.join(",", values));
                    }
                }

                result.append("\n");
            }

            return result.toString();
        }
    }

    private static String hexEncodedHash(byte[] input) {
        MessageDigest md = (MessageDigest)LOCAL_DIGEST.get();
        md.reset();
        md.update(input);
        return encodeHex(md.digest());
    }

    private static String encodeHex(byte[] data) {
        int l = data.length;
        char[] out = new char[l << 1];
        int i = 0;

        for(int j = 0; i < l; ++i) {
            out[j++] = DIGITS_LOWER[(240 & data[i]) >>> 4];
            out[j++] = DIGITS_LOWER[15 & data[i]];
        }

        return new String(out);
    }

    private static String percentEncodeParam(String param) {
        try {
            return URLEncoder.encode(param, StandardCharsets.UTF_8.name()).replace("+", "%20").replace("*", "%2A").replace("%7E", "~");
        } catch (Exception var2) {
            throw new RuntimeException(var2);
        }
    }

    private static String percentEncodeUri(String uri) {
        return percentEncodeParam(uri).replace("%2F", "/");
    }

    private static boolean isBlank(String str) {
        int strLen = str == null ? 0 : str.length();
        if (strLen != 0) {
            for(int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(str.charAt(i))) {
                    return false;
                }
            }
        }

        return true;
    }
}
