package com.ets.delivery.application.common.bo.erp;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ErpLogisticsSkuBO {
    // 产品包
    private String packageSn;
    private String packageName;

    // sku
    private String sku;
    private String skuName;

    private Integer count;

    /**
     * 商品金额
     */
    private BigDecimal itemAmount;

    /**
     * 渠道订单号（子订单号）
     */
    private String thirdOrderSn;

}
