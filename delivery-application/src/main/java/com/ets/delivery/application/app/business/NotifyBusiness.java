package com.ets.delivery.application.app.business;

import com.alibaba.fastjson.JSON;
import com.ets.common.BeanHelper;
import com.ets.common.SignHelper;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.utils.Md5SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Slf4j
@Component
public class NotifyBusiness {


    @Autowired
    private DeliveryConfig deliveryConfig;

    public HashMap<String, String> getNotifyHeaders(String appId, Object params, String signType) {
        HashMap<String, String> headers = new HashMap<>();
        if (StringUtils.isNotEmpty(appId)) {
            if (deliveryConfig.getAppKeyParams().containsKey(appId)) {
                String secret = deliveryConfig.getAppKeyParams().get(appId).getAppSecret();
                switch (signType) {
                    case "MD5":
                        headers.put("x-app-sign", Md5SignUtil.getSign(JSON.toJSONString(params), secret));
                        break;
                    case "custom":
                        headers.put("x-app-sign", SignHelper.generateSignKeepEmpty(BeanHelper.objectToMap(params), "key", secret));
                        break;
                }
                headers.put("x-app-id", appId);
            } else {
                log.error("密钥配置不存在：" + appId);
            }
        }
        return headers;
    }
}
