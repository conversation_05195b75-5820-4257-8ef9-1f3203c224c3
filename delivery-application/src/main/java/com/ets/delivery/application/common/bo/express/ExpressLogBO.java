package com.ets.delivery.application.common.bo.express;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class ExpressLogBO {

    @NotNull(message = "物流单号不能为空")
    String expressNumber;

    @NotNull(message = "快递编号不能为空")
    String expressSn;

    @NotNull(message = "订阅状态不能为空")
    Integer subscribeStatus;

    @NotBlank(message = "日志内容不能为空")
    String content;
}
