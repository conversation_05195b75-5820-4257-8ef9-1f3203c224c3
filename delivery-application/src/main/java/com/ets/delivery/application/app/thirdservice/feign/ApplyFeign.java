package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.fallback.ApplyFallbackFactory;
import com.ets.delivery.application.app.thirdservice.request.apply.ApplyPackageListDTO;
import com.ets.delivery.application.app.thirdservice.request.apply.ProductOrderExternalCreateDTO;
import com.ets.delivery.application.common.vo.productOrder.ProductOrderExternalCreateVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.validation.Valid;

@FeignClient(
        url = "${microUrls.apply:http://apply-application:20070}",
        name = "ApplyFeign",
        contextId = "ApplyFeign",
        fallbackFactory = ApplyFallbackFactory.class
)
public interface ApplyFeign {

    @PostMapping("/package/getListByAddressConfigId")
    JsonResult<?> getProductPackageList(@RequestBody ApplyPackageListDTO listDTO);

    @RequestMapping("/third/productOrder/createOrderFromExternal")
    JsonResult<ProductOrderExternalCreateVO> createOrderFromExternal(@Valid @RequestBody ProductOrderExternalCreateDTO dto);

}
