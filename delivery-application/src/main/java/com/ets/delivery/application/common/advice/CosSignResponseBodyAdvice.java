package com.ets.delivery.application.common.advice;

import com.ets.base.feign.feign.CosFeign;
import com.ets.base.feign.request.CosGetSignDTO;
import com.ets.base.feign.response.PrivateCosVO;
import com.ets.common.JsonResult;
import com.ets.starter.advice.AbstractCosSignResponseBodyAdvice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ControllerAdvice;

import java.util.HashMap;

@ControllerAdvice
@Slf4j
public class CosSignResponseBodyAdvice extends AbstractCosSignResponseBodyAdvice {

    @Autowired
    private CosFeign cosFeign;

    @Override
    protected HashMap<String, String> getSignedUrls(HashMap<String, String> matchedUrls) {

        CosGetSignDTO dto = new CosGetSignDTO();
        dto.setUrls(matchedUrls);

        JsonResult<PrivateCosVO> result = cosFeign.getSignUrl(dto);

        return result.getDataWithCheckError().getUrls();
    }

}
