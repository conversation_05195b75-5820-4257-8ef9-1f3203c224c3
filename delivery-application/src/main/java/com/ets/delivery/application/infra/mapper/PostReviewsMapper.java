package com.ets.delivery.application.infra.mapper;

import com.ets.delivery.application.common.vo.postReviews.PostReviewDateCountVO;
import com.ets.delivery.application.infra.entity.PostReviews;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 自动审核后审记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Mapper
public interface PostReviewsMapper extends CommonBaseMapper<PostReviews> {

    @Select("select DATE_FORMAT(created_at,'%Y-%m-%d') as date, emergency_type, count(*) as count " +
            "from etc_post_reviews " +
            "where created_at < '${date}'  and review_status in(0, 1) " +
            "group by DATE_FORMAT(created_at,'%Y-%m-%d'),emergency_type;")
    List<PostReviewDateCountVO> getAllDefaultGroupByDate(LocalDate date);

}
