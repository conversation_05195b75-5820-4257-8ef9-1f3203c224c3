package com.ets.delivery.application.app.thirdservice.response.jd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class JdQueryGoodsInfoVO extends JdErrorResponseVO {

    @JSONField(name = "jingdong_eclp_goods_queryGoodsInfo_responce")
    private JdQueryGoodsInfoResponseVO jdQueryGoodsInfoResponse;

    @Data
    public static class JdQueryGoodsInfoResponseVO {
        private String code;
        private List<GoodsInfo> goodsInfoList;

        @Data
        public static class GoodsInfo {

        }
    }
}
