package com.ets.delivery.application.app.thirdservice.response.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

@Data
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.FIELD)
public class DeliveryOrderQueryXmlVO implements Serializable {

    private String flag;
    private Integer code;
    private String message;

    @XmlElementWrapper(name = "orders")
    @XmlElement(name = "order")
    private List<Order> order;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Order {

        @XmlElementWrapper(name = "orderLines")
        @XmlElement(name = "orderLine")
        private List<OrderLine> orderLine;

        private Integer totalLines;

        @XmlElement(name = "deliveryOrder")
        private DeliveryOrder deliveryOrder;

        @XmlElementWrapper(name = "packages")
        @XmlElement(name = "package")
        private List<Package> aPackage;

        @Data
        @XmlAccessorType(XmlAccessType.FIELD)
        public static class OrderLine {
            private String orderLineNo;
            private String inventoryType;
            private String planQty;
            private String ownerCode;
            private String itemCode;
            private String batchCode;
            private String subSourceCode;
            private Integer actualQty;
            private String productDate;
            private String itemId;
            private String produceCode;
            private String itemName;
            private String extCode;
            private String qrCode;
            private String ossId;
            private String orderSourceCode;
            private String expireDate;
        }

        @Data
        @XmlAccessorType(XmlAccessType.FIELD)
        public static class DeliveryOrder {
            private Integer sourceOrder;
            private String orderType;
            private String storageFee;
            private String outBizCode;
            private String operateTime;
            private String payTime;
            private String payNo;
            private String arAmount;
            private String shopNick;
            private String sourcePlatformCode;
            private String orderSourceCode;
            private String orderConfirmTime;
            private String deliveryOrderCode;
            private String operatorCode;
            private String confirmType;
            private String operatorName;
            private String warehouseCode;
            private String deliveryOrderId;
            private String status;

            @XmlElement(name = "receiver")
            private Receiver receiver;

            @Data
            public static class Receiver {
                private String receiverName;
                private String receiverMobile;
                private String receiverCityCode;
                private String receiverProvince;
                private String receiverArea;
                private String receiverDetailAddress;
                private String receiverCity;
                private String receiverAreaCode;
                private String receiverProvinceCode;
            }
        }

        @Data
        @XmlAccessorType(XmlAccessType.FIELD)
        public static class Package {
            private String theoreticalWeight;
            private String length;
            private String logisticsCode;
            private String weight;
            private String packingType;
            private String logisticsName;
            private String volume;
            private String externalLogisticsCode;
            private String mainExpressCode;
            private String packageCode;
            private String ossId;
            private String expressCode;
            private String width;
            private String invoiceNo;
            private String height;
            private String boxCode;
            private String skuCount;
        }
    }

}