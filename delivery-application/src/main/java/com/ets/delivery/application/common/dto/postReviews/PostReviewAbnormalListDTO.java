package com.ets.delivery.application.common.dto.postReviews;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

@Data
public class PostReviewAbnormalListDTO {

    /**
     * 发卡方id
     */
    private Integer issuerId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 优先级[1-正常 2-优先 3-紧急]
     */
    private Integer emergencyType;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
