package com.ets.delivery.application.infra.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.consts.pickUp.PickUpCreateTypeEnum;
import com.ets.delivery.application.common.consts.pickUp.PickUpOrderStatusEnum;
import com.ets.delivery.application.common.dto.pickUp.PickUpListDTO;
import com.ets.delivery.application.infra.entity.Pickup;
import com.ets.delivery.application.infra.mapper.PickupMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 上门取件订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
@Service
@DS("db-issuer-admin-proxy")
public class PickupService extends BaseService<PickupMapper, Pickup> {

    public Pickup getOneByOrderSn(String orderSn) {
        Wrapper<Pickup> wrapper = Wrappers.<Pickup>lambdaQuery()
                .eq(Pickup::getOrderSn, orderSn)
                .eq(Pickup::getOrderStatus, PickUpOrderStatusEnum.STATUS_NORMAL.getValue())
                .orderByDesc(Pickup::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public Pickup getOneByPickUpSn(String pickUpSn) {
        Wrapper<Pickup> wrapper = Wrappers.<Pickup>lambdaQuery()
                .eq(Pickup::getPickupSn, pickUpSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public IPage<Pickup> getPage(PickUpListDTO listDTO) {
        Wrapper<Pickup> wrapper = Wrappers.<Pickup>lambdaQuery()
                .eq(StringUtils.isNotEmpty(listDTO.getOrderType()), Pickup::getOrderType, listDTO.getOrderType())
                .eq(ObjectUtil.isNotNull(listDTO.getPickupStatus()), Pickup::getPickupStatus, listDTO.getPickupStatus())
                .eq(ObjectUtil.isNotNull(listDTO.getOrderStatus()), Pickup::getOrderStatus, listDTO.getOrderStatus())
                .eq(StringUtils.isNotEmpty(listDTO.getOrderSn()), Pickup::getOrderSn, listDTO.getOrderSn())
                .eq(StringUtils.isNotEmpty(listDTO.getPickupCode()), Pickup::getPickupCode, listDTO.getPickupCode())
                .eq(StringUtils.isNotEmpty(listDTO.getPlateNo()), Pickup::getPlateNo, listDTO.getPlateNo())
                .eq(StringUtils.isNotEmpty(listDTO.getExpressNumber()), Pickup::getExpressNumber, listDTO.getExpressNumber())
                .ge(ObjectUtil.isNotEmpty(listDTO.getPickUpStartTime()), Pickup::getPickupTime, listDTO.getPickUpStartTime())
                .le(ObjectUtil.isNotEmpty(listDTO.getPickUpEndTime()), Pickup::getPickupTime, listDTO.getPickUpEndTime())
                .ge(ObjectUtil.isNotEmpty(listDTO.getCreateStartTime()), Pickup::getCreatedAt, listDTO.getCreateStartTime())
                .le(ObjectUtil.isNotEmpty(listDTO.getCreateEndTime()), Pickup::getCreatedAt, listDTO.getCreateEndTime())
                .eq(ObjectUtil.isNotNull(listDTO.getCreateType())
                        && listDTO.getCreateType().equals(PickUpCreateTypeEnum.TYPE_ADMIN.getValue()),
                        Pickup::getOrderSource, "admin")
                .ne(ObjectUtil.isNotNull(listDTO.getCreateType())
                                && !listDTO.getCreateType().equals(PickUpCreateTypeEnum.TYPE_ADMIN.getValue()),
                        Pickup::getOrderSource, "admin")
                .orderByDesc(Pickup::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }
}
