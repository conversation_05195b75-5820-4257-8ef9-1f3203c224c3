package com.ets.delivery.application.app.factory.express;

import cn.hutool.extra.spring.SpringUtil;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.express.impl.ExpressBase;
import com.ets.delivery.application.app.factory.express.impl.JdCloudExpressManage;
import com.ets.delivery.application.app.factory.express.impl.KdExpressManage;
import com.ets.delivery.application.app.factory.express.impl.YundaExpressManage;
import com.ets.delivery.application.common.consts.express.ExpressCodeConstant;

public class ExpressFactory {

    public static ExpressBase create(String expressCode) {
        ExpressBase expressBase = null;

        switch (expressCode) {
            case ExpressCodeConstant.JD_CLOUD:
                return SpringUtil.getBean(JdCloudExpressManage.class);
            case ExpressCodeConstant.YUNDA:
                return SpringUtil.getBean(YundaExpressManage.class);
            case ExpressCodeConstant.KD100:
                return SpringUtil.getBean(KdExpressManage.class);
            default:
                ToolsHelper.throwException("暂不支持此物流公司");
        }
        return expressBase;
    }
}
