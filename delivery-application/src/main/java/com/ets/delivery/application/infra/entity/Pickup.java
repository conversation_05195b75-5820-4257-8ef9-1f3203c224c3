package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 上门取件订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_pickup")
public class Pickup extends BaseEntity<Pickup> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 上门取件单订单号
     */
    private String pickupSn;

    /**
     * 取件单号
     */
    private String pickupCode;

    /**
     * 业务订单号
     */
    private String orderSn;

    /**
     * 订单类型[upgrade-设备升级]
     */
    private String orderType;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 预约时间段开始
     */
    private LocalDateTime pickupStartTime;

    /**
     * 预约时间段结束
     */
    private LocalDateTime pickupEndTime;

    /**
     * 取件快递员姓名
     */
    private String pickupName;

    /**
     * 取件快递员电话
     */
    private String pickupMobile;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 寄件人姓名
     */
    private String sendName;

    /**
     * 寄件人电话
     */
    private String sendMobile;

    /**
     * 寄件地区
     */
    private String sendArea;

    /**
     * 寄件地址
     */
    private String sendAddress;

    /**
     * 收件人姓名
     */
    private String receiveName;

    /**
     * 收件人电话
     */
    private String receiveMobile;

    /**
     * 收件地区
     */
    private String receiveArea;

    /**
     * 收件地址
     */
    private String receiveAddress;

    /**
     * 订单状态[-1-取消 1-正常]
     */
    private Integer orderStatus;

    /**
     * 取件状态[-1-取消 0-默认 1-下单成功 2-下单失败 3-待上门取件 4-取件完成 5-取件妥投 6-取件异常]
     */
    private Integer pickupStatus;

    /**
     * 取件成功时间
     */
    private LocalDateTime pickupTime;

    /**
     * 取件备注
     */
    private String pickupRemark;

    /**
     * 取件原因
     */
    private String pickupReason;

    /**
     * 回调通知地址
     */
    private String notifyBackUrl;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 短信状态[0-无需发送 1-待发送 2-发送成功 3-发送失败]
     */
    private Integer smsStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
