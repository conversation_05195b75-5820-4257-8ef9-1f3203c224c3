package com.ets.delivery.application.common.consts.sendback;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SendBackIssuerEnum {

    DEFAULT(0, ""),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(1, "江苏-苏通卡"),
    <PERSON><PERSON><PERSON><PERSON>(2, "北京-速通卡"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(10, "天津-速通卡"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(25, "广西-八桂行卡"),
    <PERSON><PERSON><PERSON>(26, "青海-青通卡"),
    <PERSON><PERSON><PERSON><PERSON>(28, "内蒙古-蒙通卡"),
    <PERSON>TongZY(30, "青海中远-青通卡"),
    <PERSON>u<PERSON>ongTruck(31, "北京货车-速通卡"),
    JSuTongTruck(32, "江苏货车-苏通卡"),
    <PERSON><PERSON><PERSON>ongTruck(33, "内蒙货车-蒙通卡"),
    J<PERSON>uTongTaxi(35, "苏通卡-出租车"),
    TSuTongTruck(37, "天津货车-速通卡"),
    WangLuZl(38, "网路智联-客车"),
    JSuTongWl(39, "天江苏通行宝9901-E路智行"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(98, "江苏货车-运政卡");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (SendBackIssuerEnum node : SendBackIssuerEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
