package com.ets.delivery.application.app.factory.task.impl;

import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.disposer.TaskDisposer;
import com.ets.delivery.application.app.factory.task.ITask;
import com.ets.delivery.application.common.config.queue.task.QueueTask;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordStatusEnum;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.TaskRecordLogService;
import com.ets.delivery.application.infra.service.TaskRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

@Slf4j
public abstract class TaskBase implements ITask {

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private TaskRecordLogService taskRecordLogService;

    @Autowired
    private TaskRecordService taskRecordService;

    @Autowired
    private QueueTask queueTask;

    @Override
    public void addAndPush(TaskRecordDTO taskRecordDTO) {
        String taskSn = ToolsHelper.genNum(redisPermanentTemplate, "task_record", ACTIVE, 8);
        taskRecordDTO.setTaskSn(taskSn);
        taskRecordDTO.setStatus(TaskRecordStatusEnum.TASK_STATUS_WAIT.getCode());
        TaskRecord taskRecord = taskRecordService.addNew(taskRecordDTO);
        queueTask.push(new TaskDisposer(taskRecord), taskRecordDTO.getDelayLevel());
    }

    @Override
    public void beforeExec(TaskRecord taskRecord) {
        //任务是否已执行成功
        if (taskRecord.getStatus().equals(TaskRecordStatusEnum.TASK_STATUS_STOP.getCode()) ||
                taskRecord.getNextExecTime() == null ||
                taskRecord.getNextExecTime().isAfter(LocalDateTime.now())
        ) {
            String msg = "任务已暂停或者还没到执行时间";
            taskRecordLogService.addLog(taskRecord.getTaskSn(), msg);
            ToolsHelper.throwException(msg, TaskRecordErrorCodeConstant.ERROR_CODE_NO_NEED_DEAL);
        }
        //非待处理状态
        if (Arrays.asList(new Integer[]{
                TaskRecordStatusEnum.TASK_STATUS_PROCESS.getCode(),
                TaskRecordStatusEnum.TASK_STATUS_FINISH.getCode(),
                TaskRecordStatusEnum.TASK_STATUS_STOP.getCode(),
        }).contains(taskRecord.getStatus())) {
            String msg = "任务为非待处理状态";
            taskRecordLogService.addLog(taskRecord.getTaskSn(), msg);
            ToolsHelper.throwException(msg, TaskRecordErrorCodeConstant.ERROR_CODE_NO_NEED_DEAL);
        }

        //执行超过20次的任务自动改成暂停状态，需要手动清零再处理
        if (taskRecord.getExecTimes() > 20) {
            String msg = "执行超过20次的任务自动改成暂停状态，需要手动清零再处理";
            taskRecordLogService.addLog(taskRecord.getTaskSn(), msg);
            ToolsHelper.throwException(msg, TaskRecordErrorCodeConstant.ERROR_CODE_NEED_STOP);
        }
        //加锁
        if (!ToolsHelper.addLock(redisPermanentTemplate, "beforeExec:" + taskRecord.getTaskSn(), 20)) {
            ToolsHelper.throwException("任务处理中，请稍后！", TaskRecordErrorCodeConstant.ERROR_CODE_NO_NEED_DEAL);
        }
        //次数加1
        taskRecord.setStatus(TaskRecordStatusEnum.TASK_STATUS_PROCESS.getCode());
        taskRecord.setExecTimes(taskRecord.getExecTimes() + 1);
        taskRecord.setUpdatedAt(LocalDateTime.now());
        taskRecordService.saveOrUpdate(taskRecord);
        taskRecordLogService.addLog(taskRecord.getTaskSn(), "开始执行任务");
    }

    @Override
    public void afterExec(TaskRecord taskRecord, Integer status, String msg) {
        if (msg.isEmpty()) {
            msg = "无报错";
        }
        //普通计算下次执行时间
        if (Arrays.asList(new Integer[]{
                        TaskRecordStatusEnum.TASK_STATUS_WAIT.getCode(),
                        TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode()
                }).contains(taskRecord.getStatus())
        ) {
            if (taskRecord.getExecTimes() < 5) {
                taskRecord.setNextExecTime(LocalDateTime.now().plusMinutes(15));
            } else if (taskRecord.getExecTimes() < 10) {
                taskRecord.setNextExecTime(LocalDateTime.now().plusMinutes(30));
            } else if (taskRecord.getExecTimes() < 15) {
                taskRecord.setNextExecTime(LocalDateTime.now().plusMinutes(60));
            } else {
                taskRecord.setNextExecTime(LocalDateTime.now().plusMinutes(90));
            }
        }
        taskRecord.setStatus(status);
        taskRecord.setExecError(msg.length() > 255 ? msg.substring(0, 255) : msg);
        taskRecordService.saveOrUpdate(taskRecord);
        taskRecordLogService.addLog(taskRecord.getTaskSn(), "结束执行任务：" + msg);
    }

    /*
     *  统一执行入口
     */
    public void execute(String taskSn){
        TaskRecord taskRecord = taskRecordService.getOneByTaskSn(taskSn);
        try{
            // 执行任务前校验
            beforeExec(taskRecord);
            // 执行任务
            childExec(taskRecord);
            // 任务执行成功
            afterExec(taskRecord, TaskRecordStatusEnum.TASK_STATUS_FINISH.getCode(), "任务执行处理无报错");
        } catch (BizException e) {
            switch (e.getErrorCode()) {
                case TaskRecordErrorCodeConstant.ERROR_CODE_NO_NEED_DEAL:
                    // 无需处理
                    break;
                case TaskRecordErrorCodeConstant.ERROR_CODE_NEED_STOP:
                    // 超过20次 暂停处理
                    afterExec(taskRecord, TaskRecordStatusEnum.TASK_STATUS_STOP.getCode(), e.getMessage());
                    break;
                case TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH:
                    // 报错完结
                    afterExec(taskRecord, TaskRecordStatusEnum.TASK_STATUS_FINISH.getCode(), e.getMessage());
                    break;
                default:
                    afterExec(taskRecord, TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode(), "任务执行报错:" + e.getMessage());
                    break;
            }
        } catch (Throwable e) {
            log.error("任务{}执行报错：{}", taskSn, e.getStackTrace());
            afterExec(taskRecord, TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode(), "任务执行报错:" + e.getLocalizedMessage());
        }
    }
}
