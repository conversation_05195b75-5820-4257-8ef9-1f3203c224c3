package com.ets.delivery.application.common.dto.setting;

import com.ets.delivery.application.common.consts.setting.SettingStatusEnum;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
public class SettingChangeStatusDTO {

    /**
     * 主键ID
     */
    @NotNull(message = "id不能为空")
    private Integer id;

    /**
     * 状态：1正常2取消
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    public boolean checkStatus() {
        return SettingStatusEnum.list.contains(status);
    }
}
