package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.ReviewsLicences;
import com.ets.delivery.application.infra.mapper.ReviewsLicencesMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 审核营业执照数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
@DS("db-issuer-admin-proxy")
public class ReviewsLicencesService extends BaseService<ReviewsLicencesMapper, ReviewsLicences> {

    public ReviewsLicences getOneByReviewSn(String reviewSn) {
        Wrapper<ReviewsLicences> wrapper = Wrappers.<ReviewsLicences>lambdaQuery()
                .eq(ReviewsLicences::getReviewSn, reviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
