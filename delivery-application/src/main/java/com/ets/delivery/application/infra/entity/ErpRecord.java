package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * erp记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_erp_record")
public class ErpRecord extends BaseEntity<ErpRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * erp流水号
     */
    private String erpSn;

    /**
     * erp订单类型[1-普通订单 2-售后订单]
     */
    private Integer erpOrderType;

    /**
     * erp订单来源[Yunda-韵达 MaoChao-天猫超市]
     */
    private String erpOrderSource;

    /**
     * 来源平台编码
     */
    private String orderPlatform;

    /**
     * 来源订单流水号
     */
    private String thirdOrderSn;

    /**
     * 支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 支付单号
     */
    private String paymentSn;

    /**
     * 支付时间
     */
    private LocalDateTime paidAt;

    /**
     * 收件人
     */
    private String sendName;

    /**
     * 联系手机
     */
    private String sendPhone;

    /**
     * 发货地区
     */
    private String sendArea;

    /**
     * 收货地址
     */
    private String sendAddress;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 发货备注
     */
    private String deliveryRemark;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 发货sku内容
     */
    private String logisticsSku;

    /**
     * 记录原始数据
     */
    private String rawData;

    /**
     * 状态[0-待处理 1-处理成功 2-处理失败]
     */
    private Integer status;

    /**
     * 错误记录
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
