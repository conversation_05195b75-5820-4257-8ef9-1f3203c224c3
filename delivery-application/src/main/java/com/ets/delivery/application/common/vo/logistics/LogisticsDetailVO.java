package com.ets.delivery.application.common.vo.logistics;

import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOrderSourceEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOrderTypeEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class LogisticsDetailVO {

    private ReceiveInfo receiveInfo;
    private GoodsInfo goodsInfo;
    private DeliveryInfo deliveryInfo;

    /**
     * 收货信息
     */
    @Data
    public static class ReceiveInfo {
        private String storageCode;
        private String storageCodeStr;
        private String sendName;
        private String sendPhone;
        private String sendArea;
        private String sendAddress;

        public String getStorageCodeStr() {
            return StorageCodeEnum.map.getOrDefault(storageCode, storageCode);
        }
    }

    /**
     * 商品信息
     */
    @Data
    public static class GoodsInfo {
        private Integer issuerId;
        private String issuerIdStr;
        private Integer cardId;
        private String orderType;
        private String orderTypeStr;
        private String orderSource;
        private String orderSourceStr;
        private String plateNo;
        private List<LogisticsGoodsVO> goodsList;

        @Data
        public static class Goods {
            private String goodsName;
            private String goodsCode;
            private Integer goodsNum;
        }

        public String getOrderTypeStr() {
            return LogisticsOrderTypeEnum.map.getOrDefault(orderType, "--");
        }

        public String getOrderSourceStr() {
            return LogisticsOrderSourceEnum.map.getOrDefault(orderSource, "--");
        }
    }

    /**
     * 发货单信息
     */
    @Data
    public static class DeliveryInfo {
        private String orderSn;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime createdAt;
        private Integer deliveryStatus;
        private String deliveryStatusStr;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime deliveryTime;
        private String deliveryRemark;
        private String expressCorp;
        private String expressNumber;
        private String notifyRemark;

        public String getDeliveryStatusStr() {
            return LogisticsDeliveryStatusEnum.map.getOrDefault(deliveryStatus, "--");
        }
    }
}
