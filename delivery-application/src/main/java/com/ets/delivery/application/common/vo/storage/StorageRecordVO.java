package com.ets.delivery.application.common.vo.storage;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class StorageRecordVO {

    /**
     * 入库件流水号
     */
    private String recordSn;

    /**
     * 仓储
     */
    private String storageCode;

    /**
     * 寄回数量
     */
    private Integer nums;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 操作人员
     */
    private String operator;

    /**
     * 状态：1待确认2已确认
     */
    private Integer status;

    /**
     * 0-默认, 1-ETC卡+OBU, 2-ETC单卡, 3-ETC单OBU, 4-ETC设备破损, 5-非高灯设备或非设备
     */
    private Integer goodsType;

    /**
     * 货物图片
     */
    private String goodsImages;

    /**
     * 接收备注
     */
    private String reviceRemark;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime reviceTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    private String goodsTypeStr;

    private String statusStr;

    private String storageName;

    private String storageImage;

    private JSONObject skuInfoObj;

}
