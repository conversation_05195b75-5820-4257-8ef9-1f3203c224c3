package com.ets.delivery.application.common.vo.rejectExpress;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.ets.delivery.application.common.consts.rejectExpress.RejectExpressMatchResultEnum;
import lombok.Data;

@Data
@HeadRowHeight(20)
@ContentRowHeight(15)
@ColumnWidth(25)
public class RejectExpressExportVO {

    @ExcelProperty("导入结果")
    private String resultMsg;

    @ExcelProperty("寄回快递单号")
    private String sendbackExpressNumber;

    @ExcelProperty("入库快递单号")
    private String storageExpressNumber;

    @ExcelProperty("匹配业务订单结果")
    private String matchResultStr;

    @ExcelIgnore
    private Integer matchResult;

    public String getMatchResultStr() {
        return RejectExpressMatchResultEnum.map.getOrDefault(matchResult, "");
    }
}
