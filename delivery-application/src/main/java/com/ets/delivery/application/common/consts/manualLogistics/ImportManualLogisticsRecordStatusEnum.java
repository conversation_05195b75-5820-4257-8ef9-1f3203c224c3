package com.ets.delivery.application.common.consts.manualLogistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum ImportManualLogisticsRecordStatusEnum {

    DEFAULT(0, "初始化"),
    SAVED(1, "已保存"),
    PUSHED(2, "已推送");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        ImportManualLogisticsRecordStatusEnum[] enums = ImportManualLogisticsRecordStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
