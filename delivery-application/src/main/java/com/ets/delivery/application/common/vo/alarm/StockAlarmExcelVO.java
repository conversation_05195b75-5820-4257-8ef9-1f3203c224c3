package com.ets.delivery.application.common.vo.alarm;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@HeadRowHeight(20)
@ContentRowHeight(15)
@ColumnWidth(25)
public class StockAlarmExcelVO {

    @ExcelProperty(value = "我司商品编码")
    private String goodsUnionCode;

    @ExcelProperty(value = "韵达仓商品编码")
    private String goodsSku;

    @ExcelProperty(value = "商品名称")
    private String goodsName;

    @ExcelProperty(value = "库存数量")
    private Integer stock;

    @ExcelProperty(value = "昨天发货量")
    private Integer yesterdayLogisticsNum;

    @ExcelProperty(value = "过去3天发货量")
    private Integer threeDayLogisticsNum;

    @ExcelProperty(value = "过去7天发货量")
    private Integer sevenDayLogisticsNum;

    @ExcelProperty(value = "预计可发货天数")
    private Integer predictDayNum;
}


