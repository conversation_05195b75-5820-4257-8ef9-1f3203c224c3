package com.ets.delivery.application.common.consts.aftersalesreviews;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum AftersalesReviewsAutoAuditEnum {

    /**
     * 人工审核
     */
    HUMAN_AUDIT(0, "人工审核"),

    /**
     * 系统审核
     */
    SYSTEM_AUDIT(1, "系统审核"),
    ;

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    static {
        map = Arrays.stream(AftersalesReviewsAutoAuditEnum.values()).collect(Collectors.toMap(AftersalesReviewsAutoAuditEnum::getValue, AftersalesReviewsAutoAuditEnum::getDesc));
    }

    public static List<Map<String, String>> getSelectOptions() {
        return Arrays.stream(AftersalesReviewsAutoAuditEnum.values())
                .map(enumValue -> Map.of("label", enumValue.getDesc(), "value", enumValue.getValue().toString()))
                .collect(Collectors.toList());
    }
}
