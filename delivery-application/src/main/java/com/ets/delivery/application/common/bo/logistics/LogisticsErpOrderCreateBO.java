package com.ets.delivery.application.common.bo.logistics;

import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsNotifyStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOrderSourceEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class LogisticsErpOrderCreateBO {

    private String originOrderSn = "";

    private String erpSn;

    /**
     * 发货流水号
     */
    private String logisticsSn;

    /**
     * 各方原始订单号
     */
    private String orderSn;

    /**
     * 订单类型（商城订单）
     */
    private String orderType;

    /**
     * 各方原始订单来源（erp）
     */
    private String orderSource = LogisticsOrderSourceEnum.YUNDA_ERP.getValue();

    /**
     * 冗余车牌号码
     */
    private String plateNo = "";

    /**
     * 冗余车牌号码
     */
    private Integer plateColor = 0;

    /**
     * 仓库代码
     */

    private String storageCode = "";

    /**
     * 地区
     */
    private String sendArea = "";

    /**
     * 收货人
     */
    private String sendName;

    /**
     * 收货人手机号码
     */
    private String sendPhone;

    /**
     * 收货人地址
     */
    private String sendAddress;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 发货状态（已发货）
     */
    private Integer deliveryStatus = LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue();

    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 回调通知路径
     */
    private String notifyBackUrl = "";

    /**
     * 通知状态 已通知
     */
    private Integer notifyStatus = LogisticsNotifyStatusEnum.NOTIFY_STATUS_SUCCESS.getValue();

    private String notifyRemark = "Erp订单无需通知";

    private LocalDateTime notifyTime = LocalDateTime.now();

    /**
     * 快递公司编码
     */
    private String logisticsCode;

    private String shipperNo;

    private String warehouseNo;

    /**
     * 商品集合
     */
    private List<Sku> skuList;

    private List<Sku> goodsSkuList;

    /**
     * issuerId
     */
    private Integer issuerId = 0;

    private String issuerName = "";

    private String goodsType = "";

    private String operator = "admin";

    private LocalDateTime drawTime = LocalDateTime.now();

    /**
     * 下单备注
     */
    private String remark;

    @Data
    public static class Sku {
        /**
         * 商品编码 sku
         */
        private String sku;
        /**
         * 发货数量
         */
        private Integer nums;

        private String goodsName = "";

        private String storageSku = "";
    }
}
