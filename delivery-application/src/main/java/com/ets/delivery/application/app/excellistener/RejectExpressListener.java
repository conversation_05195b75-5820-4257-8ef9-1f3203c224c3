package com.ets.delivery.application.app.excellistener;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.importFile.impl.ImportFileRejectExpress;
import com.ets.delivery.application.common.consts.rejectExpress.RejectExpressMatchResultEnum;
import com.ets.delivery.application.common.consts.rejectExpress.RejectExpressRecordStatusEnum;
import com.ets.delivery.application.common.consts.rejectExpress.RejectExpressResultLevelEnum;
import com.ets.delivery.application.common.dto.rejectExpress.RejectExpressImportDTO;
import com.ets.delivery.application.infra.entity.ImportRejectExpressDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

@Slf4j
public class RejectExpressListener extends AnalysisEventListener<RejectExpressImportDTO> {

    private List<ImportRejectExpressDetail> dataList;

    private final String batchNo;

    ImportFileRejectExpress importFileRejectExpress;

    public RejectExpressListener(String batchNo, ImportFileRejectExpress importFileRejectExpress) {
        this.batchNo = batchNo;
        this.importFileRejectExpress = importFileRejectExpress;
        this.dataList = new ArrayList<>();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("headMap:{}", headMap);

        // 校验表头
        Collection<String> excelHead = headMap.values();
        List<String> head = Arrays.asList("原快递单号", "入库快递单号");
        head.forEach(key -> {
            if (!excelHead.contains(key)) {
                ToolsHelper.throwException("上传文件列项与模板不一致");
            }
        });

        // 行数
        Integer total = context.readSheetHolder().getApproximateTotalRowNumber();
        if (total == 1 || total > 101) {
            ToolsHelper.throwException("上传文件超过行数限制，请上传最小为1条最大为100条数据");
        }
    }

    @Override
    public void invoke(RejectExpressImportDTO rejectExpressImportDTO, AnalysisContext analysisContext) {

        if (ObjectUtils.isEmpty(rejectExpressImportDTO)) {
            return;
        }

        // 预处理数据
        if (StringUtils.isNotEmpty(rejectExpressImportDTO.getSendbackExpressNumber())) {
            rejectExpressImportDTO.setSendbackExpressNumber(rejectExpressImportDTO.getSendbackExpressNumber().trim().toUpperCase(Locale.ROOT));
        }
        if (StringUtils.isNotEmpty(rejectExpressImportDTO.getStorageExpressNumber())) {
            rejectExpressImportDTO.setStorageExpressNumber(rejectExpressImportDTO.getStorageExpressNumber().trim().toUpperCase(Locale.ROOT));
        }

        // 组装数据
        ImportRejectExpressDetail detail = BeanUtil.copyProperties(rejectExpressImportDTO, ImportRejectExpressDetail.class);
        detail.setBatchNo(batchNo);
        detail.setResultLevel(RejectExpressResultLevelEnum.LEVEL_NORMAL.getValue());
        detail.setResultMsg("");
        detail.setRecordStatus(RejectExpressRecordStatusEnum.SUCCESS.getValue());
        detail.setMatchResult(RejectExpressMatchResultEnum.DEFAULT.getValue());
        detail.setCreatedAt(LocalDateTime.now());
        detail.setUpdatedAt(LocalDateTime.now());

        // 检查失败
        String errorMsg = importFileRejectExpress.checkImportError(rejectExpressImportDTO);
        if (StringUtils.isNotEmpty(errorMsg)) {
            detail.setResultLevel(RejectExpressResultLevelEnum.LEVEL_ERROR.getValue());
            detail.setRecordStatus(RejectExpressRecordStatusEnum.FAIL.getValue());
            detail.setResultMsg(errorMsg);
        }

        if (StringUtils.isEmpty(errorMsg)) {
            // 检查匹配结果
            if (importFileRejectExpress.checkMatchResult(rejectExpressImportDTO)) {
                detail.setMatchResult(RejectExpressMatchResultEnum.MATCH.getValue());
                detail.setResultMsg("导入成功");
            } else {
                detail.setMatchResult(RejectExpressMatchResultEnum.NOT_MATCH.getValue());
                detail.setResultLevel(RejectExpressResultLevelEnum.LEVEL_WARNING.getValue());
                detail.setResultMsg("导入成功");
            }
        }

        dataList.add(detail);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 查找重复项
        Set<String> sendbackExpressNumberSet = new HashSet<>();
        Set<String> storageExpressNumberSet = new HashSet<>();
        dataList.forEach(item -> {
            sendbackExpressNumberSet.add(item.getSendbackExpressNumber());
            storageExpressNumberSet.add(item.getStorageExpressNumber());
        });
        if (sendbackExpressNumberSet.size() != dataList.size() || storageExpressNumberSet.size() != dataList.size()) {
            dataList.forEach(item -> {
                if (Collections.frequency(sendbackExpressNumberSet, item.getSendbackExpressNumber()) > 1) {
                    item.setResultLevel(RejectExpressResultLevelEnum.LEVEL_ERROR.getValue());
                    item.setRecordStatus(RejectExpressRecordStatusEnum.FAIL.getValue());
                    item.setResultMsg(item.getResultMsg() + "寄回快递单号重复；");
                }
                if (Collections.frequency(storageExpressNumberSet, item.getStorageExpressNumber()) > 1) {
                    item.setResultLevel(RejectExpressResultLevelEnum.LEVEL_ERROR.getValue());
                    item.setRecordStatus(RejectExpressRecordStatusEnum.FAIL.getValue());
                    item.setResultMsg(item.getResultMsg() + "入库快递单号重复；");
                }
            });
        }

        log.info("【入库拒收】导入成功 批次号：{} 条数：{}", batchNo, dataList.size());
        importFileRejectExpress.saveImportFileData(batchNo, dataList);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("【入库拒收】导入失败：", exception);
        throw exception;
    }
}
