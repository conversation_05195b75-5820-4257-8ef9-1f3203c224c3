package com.ets.delivery.application.common.consts.stock;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StockTypeEnum {

    IN(1, "入库"),

    OUT(2, "出库");

    private final Integer code;
    private final String description;

    public static String getDescByCode(int code) {

        for (StockTypeEnum node : StockTypeEnum.values()) {

            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static StockTypeEnum getByCode(int code) {

        for (StockTypeEnum node : StockTypeEnum.values()) {

            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }
}
