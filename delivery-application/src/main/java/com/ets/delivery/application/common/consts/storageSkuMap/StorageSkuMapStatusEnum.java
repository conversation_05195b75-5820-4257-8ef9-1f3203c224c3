package com.ets.delivery.application.common.consts.storageSkuMap;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StorageSkuMapStatusEnum {
    TASK_STATUS_NORMAL(1, "正常"),
    TASK_STATUS_CLOSED(2, "无效");

    private final int code;
    private final String description;

    public static String getDescByStatus(int status) {
        for (StorageSkuMapStatusEnum node : StorageSkuMapStatusEnum.values()) {
            if (node.getCode() == status) {
                return node.getDescription();
            }
        }
        return "";
    }
}
