package com.ets.delivery.application.infra.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.bo.SendBackLogBO;
import com.ets.delivery.application.infra.entity.LogisticsSendbackLog;
import com.ets.delivery.application.infra.mapper.LogisticsSendbackLogMapper;
import org.springframework.stereotype.Service;

import jakarta.validation.Valid;
import java.time.LocalDateTime;

/**
 * <p>
 * 寄回件日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-08
 */
@Service
public class LogisticsSendbackLogService extends BaseService<LogisticsSendbackLogMapper, LogisticsSendbackLog> {

    public void addLog(@Valid SendBackLogBO logBO) {
        LogisticsSendbackLog log = BeanUtil.copyProperties(logBO, LogisticsSendbackLog.class);
        log.setCreatedAt(LocalDateTime.now());
        log.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(log);
    }

    public IPage<LogisticsSendbackLog> getPage(Integer sendBackId, Integer pageNum, Integer pageSize) {
        Wrapper<LogisticsSendbackLog> wrapper = Wrappers.<LogisticsSendbackLog>lambdaQuery()
                .eq(LogisticsSendbackLog::getSendbackId, sendBackId)
                .orderByDesc(LogisticsSendbackLog::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);
    }
}
