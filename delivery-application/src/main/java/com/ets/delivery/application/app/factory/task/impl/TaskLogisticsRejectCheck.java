package com.ets.delivery.application.app.factory.task.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.SendBackBusiness;
import com.ets.delivery.application.common.bo.express.ExpressDataBO;
import com.ets.delivery.application.common.bo.task.TaskLogisticsRejectCheckBO;
import com.ets.delivery.application.common.consts.jd.JdOpeTitleEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class TaskLogisticsRejectCheck extends TaskBase {

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private LogisticsSendBackService sendBackService;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private StorageRecordService storageRecordService;

    @Autowired
    private ImportRejectExpressDetailService rejectExpressDetailService;

    @Autowired
    private SendBackBusiness sendBackBusiness;

    @Override
    public void childExec(TaskRecord taskRecord) {
        TaskLogisticsRejectCheckBO rejectCheckBO = JSON.parseObject(taskRecord.getNotifyContent(), TaskLogisticsRejectCheckBO.class);

        StorageRecord storageRecord = null;

        // 根据快递单号查发货单
        Logistics logistics = logisticsService.getByExpressNumber(rejectCheckBO.getExpressNumber());
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException(rejectCheckBO.getExpressNumber() + " 找不到发货单信息");
        }

        // 查询寄回件
        LogisticsSendBack sendBack = sendBackService.getInfoBySn(rejectCheckBO.getOrderSn());
        if (ObjectUtils.isEmpty(sendBack)) {
            ToolsHelper.throwException(rejectCheckBO.getExpressNumber() + " 找不到寄回件信息");
        }

        // 京东仓发货
        if (logistics.getStorageCode().equals(StorageCodeEnum.JD_CLOUD.getValue())) {
            // 查物流轨迹
            Express express = expressService.getOneByExpressNumber(rejectCheckBO.getExpressNumber());
            if (ObjectUtils.isEmpty(express)) {
                ToolsHelper.throwException(rejectCheckBO.getExpressNumber() + " 找不到物流轨迹信息");
            }

            if (StringUtils.isEmpty(express.getData())) {
                ToolsHelper.throwException(rejectCheckBO.getExpressNumber() + " 物流轨迹信息为空");
            }

            // 循环物流轨迹，找到换单记录
            AtomicReference<String> newExpressNumber = new AtomicReference<>("");
            List<ExpressDataBO> expressDataList = JSON.parseObject(express.getData(), new TypeReference<List<ExpressDataBO>>() {});
            expressDataList.forEach(expressDataBO -> {
                if (expressDataBO.getStatus().equals(JdOpeTitleEnum.EXPRESS_EXCHANGE.getValue())) {
                    String pattern = "换单打印，新运单号([A-Za-z0-9]+)";
                    Pattern r = Pattern.compile(pattern);
                    Matcher m = r.matcher(expressDataBO.getContext());

                    if (m.find()) {
                        newExpressNumber.set(m.group(1));
                    }
                }
            });

            if (StringUtils.isEmpty(newExpressNumber.get())) {
                ToolsHelper.throwException(rejectCheckBO.getExpressNumber() + " 物流轨迹未找到换单记录");
            }

            // 更新快递单号
            sendBack = sendBackBusiness.updateOriginExpressNumber(sendBack,
                    newExpressNumber.get(),
                    sendBack.getExpressNumber(),
                    false
            );

            // 查询入库记录
            storageRecord = storageRecordService.getByExpressNumber(newExpressNumber.get());

        } else {
            // 查询导入记录
            ImportRejectExpressDetail detail = rejectExpressDetailService.getOneBySendBackExpressNumber(rejectCheckBO.getExpressNumber());
            if (ObjectUtils.isNotEmpty(detail)) {
                // 更新快递单号
                sendBack = sendBackBusiness.updateOriginExpressNumber(sendBack,
                        detail.getStorageExpressNumber(),
                        detail.getSendbackExpressNumber(),
                        true
                );

                // 查询入库记录
                storageRecord = storageRecordService.getByExpressNumber(detail.getStorageExpressNumber());
            }

        }

        if (ObjectUtils.isNotEmpty(storageRecord)) {
            // 更新寄回件状态
            sendBackBusiness.checkAndUpdate(sendBack, storageRecord);
        }
    }
}
