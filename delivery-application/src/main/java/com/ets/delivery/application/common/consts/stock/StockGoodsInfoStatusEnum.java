package com.ets.delivery.application.common.consts.stock;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StockGoodsInfoStatusEnum {

    NORMAL(0, "正常"),

    DELETED(2, "已删除");

    private final Integer code;
    private final String description;

    public static String getDescByCode(int code) {

        for (StockGoodsInfoStatusEnum node : StockGoodsInfoStatusEnum.values()) {

            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static StockGoodsInfoStatusEnum getByCode(int code) {

        for (StockGoodsInfoStatusEnum node : StockGoodsInfoStatusEnum.values()) {

            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }
}
