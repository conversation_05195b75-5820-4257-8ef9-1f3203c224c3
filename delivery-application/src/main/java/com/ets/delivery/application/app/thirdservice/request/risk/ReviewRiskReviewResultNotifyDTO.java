package com.ets.delivery.application.app.thirdservice.request.risk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ReviewRiskReviewResultNotifyDTO {

    @JsonProperty("review_sn")
    private String reviewSn;

    @JsonProperty("risk_review_status")
    private Integer riskReviewStatus;

    @JsonProperty("reject_code")
    private String rejectCode;

    private String msg;

    private String operator;
}
