package com.ets.delivery.application.app.job;

import com.ets.delivery.application.app.disposer.TaskDisposer;
import com.ets.delivery.application.common.config.queue.task.QueueTask;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.TaskRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class TaskJob {
    @Autowired
    private TaskRecordService taskRecordService;
    @Autowired
    private QueueTask queueTask;
    /*
     *  轮询 未完成的任务管理
     */
    @XxlJob("reExecHandler")
    public ReturnT<String> reExecHandler(String params){
        //获取未完成的数据
        List<TaskRecord> taskRecordList = taskRecordService.getListByCreatedAt(7);
        if(taskRecordList != null){
            taskRecordList.forEach(v ->{
                //推送到队列
                queueTask.push(new TaskDisposer(v));
            });
        }
        return ReturnT.SUCCESS;
    }
}
