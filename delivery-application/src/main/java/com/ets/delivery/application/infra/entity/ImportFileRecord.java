package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 文件上传导入记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_import_file_record")
public class ImportFileRecord extends BaseEntity<ImportFileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 导入类型
     */
    private String importType;

    /**
     * 文件名
     */
    private String filename;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 总条数
     */
    private Integer total;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 上传状态[0-默认 1-上传成功 2-上传失败]
     */
    private Integer uploadStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
