package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.delivery.application.common.consts.setting.SettingCategoryEnum;
import com.ets.delivery.application.common.consts.setting.SettingKeyEnum;
import com.ets.delivery.application.common.dto.setting.*;
import com.ets.delivery.application.common.vo.setting.AfterSalesReviewReasonListVO;
import com.ets.delivery.application.infra.entity.Setting;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SettingAfterSalesReviewBusiness extends SettingBusiness {

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    public IPage<AfterSalesReviewReasonListVO> getAfterSalesReviewReasonList(AfterSalesReviewReasonListDTO listDTO) {
        SettingListDTO settingListDTO = BeanUtil.copyProperties(listDTO, SettingListDTO.class);
        settingListDTO.setCategoryList(SettingCategoryEnum.afterSalesList);
        settingListDTO.setValue(listDTO.getReason());
        return settingService.getPageByCondition(settingListDTO).convert(
                setting -> {
                    AfterSalesReviewReasonListVO listVO = BeanUtil.copyProperties(setting, AfterSalesReviewReasonListVO.class);
                    listVO.setReason(setting.getValue());
                    listVO.setReasonId(setting.getParams());
                    return listVO;
                }
        );
    }

    public void addAfterSalesReviewReason(AfterSalesReviewReasonAddDTO addDTO) {
        SettingAddDTO settingAddDTO = new SettingAddDTO();
        settingAddDTO.setType(0);
        settingAddDTO.setCategory(addDTO.getCategory());
        settingAddDTO.setKey(SettingKeyEnum.AFTER_SALES_REVIEW.getValue());
        settingAddDTO.setValue(addDTO.getReason());
        // 先检查参数 再自增原因id
        checkSetting(settingAddDTO);
        settingAddDTO.setParams(getReasonId().toString());
        addSetting(settingAddDTO);
    }

    public void editAfterSalesReviewReason(AfterSalesReviewReasonEditDTO editDTO) {
        SettingEditDTO settingEditDTO = new SettingEditDTO();
        settingEditDTO.setId(editDTO.getId());
        settingEditDTO.setCategory(editDTO.getCategory());
        settingEditDTO.setKey(SettingKeyEnum.AFTER_SALES_REVIEW.getValue());
        settingEditDTO.setValue(editDTO.getReason());
        editSetting(settingEditDTO);
    }

    public Long getReasonId() {
        String redisKey = "afterSalesReviewReasonId:" + SettingKeyEnum.AFTER_SALES_REVIEW.getValue();
        if (!redisPermanentTemplate.hasKey(redisKey)) {
            // 查询数据库 获取最大的值
            LambdaQueryWrapper<Setting> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(Setting::getParams)
                            .eq(Setting::getKey, SettingKeyEnum.AFTER_SALES_REVIEW.getValue());
            List<Setting> settingList = settingService.getListByWrapper(queryWrapper);
            Long maxId = 0L;
            if (ObjectUtils.isNotEmpty(settingList)) {
                maxId = settingList.stream()
                        .filter(s -> StringUtils.isNotBlank(s.getParams()))
                        .mapToLong(s -> Long.parseLong(s.getParams()))
                        .max()
                        .orElse(0L);
            }
            redisPermanentTemplate.opsForValue().set(redisKey, String.valueOf(maxId));
        }
        return redisPermanentTemplate.opsForValue().increment(redisKey);
    }
}
