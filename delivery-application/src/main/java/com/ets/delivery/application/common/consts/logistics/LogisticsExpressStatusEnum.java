package com.ets.delivery.application.common.consts.logistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum LogisticsExpressStatusEnum {

    CANCEL(-1, "取消"),
    DEFAULT(0, "未发货"),
    SENT(1, "已发货"),
    PICKED(2, "已揽收"),
    TRANSIT(3, "运输中"),
    DELIVERY(6, "派件中"),
    SIGNED(4, "已签收"),
    EXCEPTION(5, "问题件");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(LogisticsExpressStatusEnum.values())
                .collect(Collectors.toMap(LogisticsExpressStatusEnum::getValue, LogisticsExpressStatusEnum::getDesc));
    }
}
