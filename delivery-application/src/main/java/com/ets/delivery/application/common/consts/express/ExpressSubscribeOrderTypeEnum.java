package com.ets.delivery.application.common.consts.express;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ExpressSubscribeOrderTypeEnum {
    ORDER_TYPE_LOGISTICS(1, "发货单"),
    ORDER_TYPE_SEND_BACK(2, "寄回件");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(ExpressSubscribeOrderTypeEnum.values()).collect(Collectors.toMap(ExpressSubscribeOrderTypeEnum::getValue, ExpressSubscribeOrderTypeEnum::getDesc));
    }
}
