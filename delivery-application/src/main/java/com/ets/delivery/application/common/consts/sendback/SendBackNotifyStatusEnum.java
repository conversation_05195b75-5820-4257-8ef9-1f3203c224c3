package com.ets.delivery.application.common.consts.sendback;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum SendBackNotifyStatusEnum {

    NOTIFY_STATUS_WAIT(0, "待通知"),
    NOTIFY_STATUS_PROCESSING(1, "通知中"),
    NOTIFY_STATUS_SUCCESS(2, "通知成功"),
    NOTIFY_STATUS_FAIL(3, "通知失败"),
    NOTIFY_STATUS_CANCEL(4, "通知取消");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(SendBackNotifyStatusEnum.values()).collect(Collectors.toMap(SendBackNotifyStatusEnum::getValue, SendBackNotifyStatusEnum::getDesc));
    }
}
