package com.ets.delivery.application.common.micro;

import cn.hutool.core.util.ObjectUtil;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.infra.entity.User;
import com.ets.delivery.application.infra.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;


@Slf4j
@Component(value = "CommonInterceptor")
public class CommonInterceptor implements HandlerInterceptor {

    @Autowired
    UserService userService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 跳过用户登录校验
        String url = request.getRequestURI();

        if (! url.contains("/admin")) {
            return true;
        }
        String token = request.getHeader("token");

        User user = userService.findByToken(token);
        if (ObjectUtil.isNull(user)) {
            log.warn("用户不存在");
            ToolsHelper.throwException("用户不存在");
        }
        ThreadLocalUtil.setData(ThreadLocalCacheKey.LOGIN_USER_KEY, user);

        return true;
    }
}
