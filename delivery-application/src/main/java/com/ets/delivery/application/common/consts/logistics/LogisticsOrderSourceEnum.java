package com.ets.delivery.application.common.consts.logistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum LogisticsOrderSourceEnum {

    APPLY("apply", "申办"),
    AFTER_SALES("after_sales", "售后"),
    RE_DELIVERY("re_delivery", "非标"),
    MANUAL("manual", "手动下单"),
    YUNDA_ERP("yunda_erp", "韵达ERP"),;

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        LogisticsOrderSourceEnum[] enums = LogisticsOrderSourceEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
