package com.ets.delivery.application.app.thirdservice.response.jd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class JdQueryOrderVO extends JdErrorResponseVO {

    @JSONField(name = "jingdong_eclp_order_queryOrder_responce")
    private JdQueryOrderResponseVO jdQueryOrderResponse;

    @Data
    public static class JdQueryOrderResponseVO {
        private String code;

        @JSONField(name = "queryorder_result")
        private JdQueryOrderResult result;

        @Data
        public static class JdQueryOrderResult {
            private String eclpSoNo;
            private String isvUUID;
            private String shopNo;
            private String departmentNo;
            private String warehouseNo;
            private String shipperNo;
            private String shipperName;
            private String salesPlatformOrderNo;
            private String salePlatformSource;
            private Date salesPlatformCreateTime;
            private String consigneeName;
            private String consigneeMobile;
            private Date expectDate;
            private String consigneeAddress;
            private String orderMark;
            private String pinAccount;
            private String splitFlag;
            private String currentStatus;
            private String wayBill;
            private List<OrderDetail> orderDetailList;
            private List<OrderPackage> orderPackageList;
            private List<OrderStatus> orderStatusList;
            private Number transType;
            private String soWeight;
            private List<BatchDetail> batchDetailList;
            private String splitEclpSoNos;

            @Data
            public static class OrderDetail {
                private String isvGoodsNo;
                private String goodsNo;
                private Number price;
                private Number quantity;
                private Number realOutQty;
            }

            @Data
            public static class OrderPackage {
                private String packageNo;
                private Number packWeight;
                private Number packVolume;
            }

            @Data
            public static class OrderStatus {
                private Number soStatusCode;
                private String soStatusName;
                private String operateTime;
                private String operateUser;
            }

            @Data
            public static class BatchDetail {
                private Number batchQty;
                private String goodsNo;
                private String isvGoodsNo;
            }
        }
    }
}
