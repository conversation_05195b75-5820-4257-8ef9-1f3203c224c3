package com.ets.delivery.application.common.consts.yunda;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum YundaLogisticsActionEnum {

    ACCEPT("ACCEPT", "收件扫描"),
    GOT("GOT", "揽件扫描"),
    ARRIVAL("ARRIVAL", "入中转"),
    DEPARTURE("DEPARTURE", "出中转"),
    SENT("SENT", "派件中"),
    INBOUND("INBOUND", "第三方代收入库"),
    SIGNED("SIGNED", "已签收"),
    OUTBOUND("OUTBOUND", "第三方代收快递员取出"),
    SIGNFAIL("SIGNFAIL", "签收失败"),
    RETURN("RETURN", "退回件"),
    ISSUE("ISSUE", "问题件"),
    REJECTION("REJECTION", "拒收"),
    OTHER("OTHER", "其他"),
    OVERSEA_IN("OVERSEA_IN", "入库扫描"),
    OVERSEA_OUT("OVERSEA_OUT", "出库扫描"),
    CLEARANCE_START("CLEARANCE_START", "清关开始"),
    CLEARANCE_FINISH("CLEARANCE_FINISH", "清关结束"),
    CLEARANCE_FAIL("CLEARANCE_FAIL", "清关失败"),
    OVERSEA_ARRIVAL("OVERSEA_ARRIVAL", "干线到达"),
    OVERSEA_DEPARTURE("OVERSEA_DEPARTURE", "干线离开"),
    TRANSFER("TRANSFER", "转单");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    YundaLogisticsActionEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        YundaLogisticsActionEnum[] enums = YundaLogisticsActionEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
