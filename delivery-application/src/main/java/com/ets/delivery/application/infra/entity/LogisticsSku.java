package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 发货单商品列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("etc_logistics_sku")
public class LogisticsSku extends BaseEntity<LogisticsSku> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 发货单流水号
     */
    private String logisticsSn;

    /**
     * 货品编号
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 仓库对应sku的编码
     */
    private String storageSku;

    /**
     * 发货数量
     */
    private Integer nums;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 统计发货量
     */
    @TableField(exist = false)
    private Double sumGoodsNums;
}
