package com.ets.delivery.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.utils.SubAroundUtils;
import com.ets.delivery.application.infra.entity.LogisticsLog;
import com.ets.delivery.application.infra.mapper.LogisticsLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <p>
 * 物流单日志 业务处理类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@Service
@Slf4j
public class LogisticsLogService extends BaseService<LogisticsLogMapper, LogisticsLog> {
    @Autowired
    private SubAroundUtils subAroundUtils;
    /*
     * 增加日志
     */
    public LogisticsLog addLog(Integer logisticsId,String type,String content,String operator) {
        try {
            LogisticsLog logisticsLog = new LogisticsLog();
            logisticsLog.setOperator(operator);
            logisticsLog.setLogisticsId(logisticsId);
            logisticsLog.setType(type);
            logisticsLog.setOperateContent(subAroundUtils.around(content,0,255));
            logisticsLog.setCreatedAt(LocalDateTime.now());
            logisticsLog.setUpdatedAt(LocalDateTime.now());
            this.baseMapper.insert(logisticsLog);
            return logisticsLog;
        }catch (Exception e) {
            //ToolsHelper.throwException("校验异常:"+e.getMessage());
            log.error("增加日志失败："+logisticsId+","+e.getMessage());
            return null;
        }
    }

    public IPage<LogisticsLog> getPage(Integer logisticsId, Integer pageNum, Integer pageSize) {
        Wrapper<LogisticsLog> wrapper = Wrappers.<LogisticsLog>lambdaQuery()
                .eq(LogisticsLog::getLogisticsId, logisticsId)
                .orderByDesc(LogisticsLog::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);
    }
}
