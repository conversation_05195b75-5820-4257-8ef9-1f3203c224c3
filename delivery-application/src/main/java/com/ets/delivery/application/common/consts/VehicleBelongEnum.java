package com.ets.delivery.application.common.consts;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum VehicleBelongEnum {
    PERSONAL(1, "个人车"),
    COMPANY(2, "公司车");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    VehicleBelongEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        VehicleBelongEnum[] enums = VehicleBelongEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
