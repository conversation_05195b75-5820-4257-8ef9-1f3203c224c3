package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.consts.setting.SettingCategoryEnum;
import com.ets.delivery.application.common.consts.setting.SettingKeyEnum;
import com.ets.delivery.application.common.consts.setting.SettingStatusEnum;
import com.ets.delivery.application.common.dto.setting.SettingListDTO;
import com.ets.delivery.application.infra.entity.Setting;
import com.ets.delivery.application.infra.mapper.SettingMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 配置信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-24
 */
@Service
@DS("db-issuer-admin")
public class SettingService extends BaseService<SettingMapper, Setting> {

    public IPage<Setting> getPageByCategoryKey(SettingListDTO listDTO) {
        Wrapper<Setting> wrapper = Wrappers.<Setting>lambdaQuery()
                .eq(Setting::getCategory, listDTO.getCategory())
                .eq(Setting::getKey, listDTO.getKey())
                .orderByAsc(Setting::getStatus)
                .orderByDesc(Setting::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }

    public IPage<Setting> getPageByCondition(SettingListDTO listDTO) {
        Wrapper<Setting> wrapper = Wrappers.<Setting>lambdaQuery()
                .in(listDTO.getCategoryList() != null, Setting::getCategory, listDTO.getCategoryList())
                .eq(listDTO.getCategory() != null, Setting::getCategory, listDTO.getCategory())
                .eq(listDTO.getKey() != null, Setting::getKey, listDTO.getKey())
                .like(ObjectUtils.isNotEmpty(listDTO.getValue()), Setting::getValue, listDTO.getValue())
                .eq(listDTO.getStatus() != null, Setting::getStatus, listDTO.getStatus())
                .orderByAsc(Setting::getStatus)
                .orderByDesc(Setting::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }

    public List<Setting> getAfterSalesReviewReasonList() {
        Wrapper<Setting> wrapper = Wrappers.<Setting>lambdaQuery()
                .eq(Setting::getKey, SettingKeyEnum.AFTER_SALES_REVIEW.getValue())
                .eq(Setting::getStatus, SettingStatusEnum.STATUS_NORMAL.getValue())
                .orderByAsc(Setting::getSort);
        return this.baseMapper.selectList(wrapper);
    }

    public List<Setting> getAddressStorageMapList() {
        Wrapper<Setting> wrapper = Wrappers.<Setting>lambdaQuery()
                .eq(Setting::getCategory, SettingCategoryEnum.ADDRESS_STORAGE_MAP.getValue())
                .eq(Setting::getStatus, SettingStatusEnum.STATUS_NORMAL.getValue())
                .orderByDesc(Setting::getCreatedAt);
        return this.baseMapper.selectList(wrapper);
    }

    public List<Setting> getListByCategoryKey(String category, String key) {
        Wrapper<Setting> wrapper = Wrappers.<Setting>lambdaQuery()
                .eq(Setting::getCategory, category)
                .eq(Setting::getKey, key)
                .eq(Setting::getStatus, SettingStatusEnum.STATUS_NORMAL.getValue())
                .orderByDesc(Setting::getCreatedAt);
        return this.baseMapper.selectList(wrapper);
    }

    public List<Setting> getRiskReviewReasonList() {
        Wrapper<Setting> wrapper = Wrappers.<Setting>lambdaQuery()
                .eq(Setting::getKey, SettingKeyEnum.RISK_REVIEW.getValue())
                .eq(Setting::getStatus, SettingStatusEnum.STATUS_NORMAL.getValue())
                .orderByAsc(Setting::getSort);
        return this.baseMapper.selectList(wrapper);
    }

    public Setting getOneByCategoryKeyValue(String category, String key, String value) {
        Wrapper<Setting> wrapper = Wrappers.<Setting>lambdaQuery()
                .eq(Setting::getCategory, category)
                .eq(Setting::getKey, key)
                .eq(Setting::getValue, value)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public Setting getOneByParams(String params, String key) {
        Wrapper<Setting> wrapper = Wrappers.<Setting>lambdaQuery()
                .eq(Setting::getParams, params)
                .eq(Setting::getKey, key)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
