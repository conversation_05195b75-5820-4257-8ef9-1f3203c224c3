package com.ets.delivery.application.app.thirdservice.response.jd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class JdAddOrderVO extends JdErrorResponseVO {

    @JSONField(name = "jingdong_eclp_order_addOrder_responce")
    private JdAddOrderResponseVO jdAddOrderResponse;

    @Data
    public static class JdAddOrderResponseVO {
        private String code;
        private String eclpSoNo;
    }
}
