package com.ets.delivery.application.app.factory.task.impl;

import com.alibaba.fastjson2.JSON;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.RiskReviewBusiness;
import com.ets.delivery.application.app.thirdservice.feign.PhpIssuerAdminFeign;
import com.ets.delivery.application.app.thirdservice.request.risk.ReviewRiskResultNotifyDTO;
import com.ets.delivery.application.common.bo.ReviewLogBO;
import com.ets.delivery.application.common.config.RiskReviewConfig;
import com.ets.delivery.application.common.consts.ReviewLogTypeEnum;
import com.ets.delivery.application.common.consts.reviews.ReviewReviewStatusEnum;
import com.ets.delivery.application.common.consts.reviews.ReviewsRiskStatusEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewRiskTypeEnum;
import com.ets.delivery.application.common.dto.reviews.RiskResultNotifyDTO;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewCreateDTO;
import com.ets.delivery.application.infra.entity.Reviews;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.ReviewsLogService;
import com.ets.delivery.application.infra.service.ReviewsService;
import com.ets.risk.application.common.consts.risk.RiskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TaskRiskResultNotify extends TaskBase {

    @Autowired
    private RiskReviewConfig riskReviewConfig;

    @Autowired
    private ReviewsService reviewsService;

    @Autowired
    private ReviewsLogService reviewsLogService;

    @Autowired
    private PhpIssuerAdminFeign phpIssuerAdminFeign;

    @Autowired
    private RiskReviewBusiness riskReviewBusiness;

    @Override
    public void childExec(TaskRecord taskRecord) {
        RiskResultNotifyDTO notifyDTO = JSON.parseObject(taskRecord.getNotifyContent(), RiskResultNotifyDTO.class);

        // 通过业务单号查询待风控审核单
        Reviews reviews = findReviewByBusinessSn(notifyDTO);
        if (reviews == null) {
            return;
        }

        // 配置关闭风控处理
        if (!riskReviewConfig.isEnabled()) {
            return;
        }

        // 审核单已取消
        if (reviews.getReviewStatus().equals(ReviewReviewStatusEnum.REVIEW_STATUS_CANCEL.getValue())) {
            return;
        }

        // 风控通过
        if (notifyDTO.getRiskStatus().equals(RiskStatusEnum.TASK_STATUS_PASS.getStatus())) {
            handleRiskPass(notifyDTO, reviews);
            return;
        }

        // 风控不通过，且允许处理风控结果时才创建风控初审单
        if (notifyDTO.getRiskStatus().equals(RiskStatusEnum.TASK_STATUS_REJECT.getStatus())) {
            handleRiskReject(notifyDTO, reviews);
            return;
        }

        log.info("风控结果通知处理结束，businessSn:{} riskStatus:{}", notifyDTO.getBusinessSn(), notifyDTO.getRiskStatus());
    }

    /**
     * 通过业务单号查询待风控审核单
     */
    private Reviews findReviewByBusinessSn(RiskResultNotifyDTO notifyDTO) {
        Reviews reviews = reviewsService.getOneByOrderSnAndRiskStatus(notifyDTO.getBusinessSn(), ReviewsRiskStatusEnum.IN_PROGRESS.getValue());
        if (reviews == null) {
            log.warn("未找到对应的审核单，businessSn:{} riskStatus:{}", notifyDTO.getBusinessSn(), notifyDTO.getRiskStatus());
        }
        return reviews;
    }

    /**
     * 处理风控通过的情况
     */
    private void handleRiskPass(RiskResultNotifyDTO notifyDTO, Reviews reviews) {
        log.info("风控通过，已更新审核单状态并待通知，业务单号:{} 原因:{}", notifyDTO.getBusinessSn(), notifyDTO.getRiskRemark());

        // 请求申办审核处理审核单
        notifyReviewRiskResult(reviews.getReviewSn());

        // 记录日志
        addReviewLog(reviews.getId(), "风控通过，原因：" + notifyDTO.getRiskRemark());
    }

    /**
     * 处理风控不通过的情况
     */
    private void handleRiskReject(RiskResultNotifyDTO notifyDTO, Reviews reviews) {
        RiskReviewCreateDTO createDTO = buildRiskReviewCreateDTO(notifyDTO, reviews);
        riskReviewBusiness.createRiskReview(createDTO);
        log.info("风控不通过，已创建风控初审单，orderSn:{}", notifyDTO.getBusinessSn());

        // 记录日志
        addReviewLog(reviews.getId(), "风控不通过，创建风控初审单，触达风控原因：" + createDTO.getRiskRuleRemark());
    }

    /**
     * 构建风控初审单创建DTO
     */
    private RiskReviewCreateDTO buildRiskReviewCreateDTO(RiskResultNotifyDTO notifyDTO, Reviews reviews) {
        RiskReviewCreateDTO createDTO = new RiskReviewCreateDTO();
        createDTO.setBusinessSn(notifyDTO.getBusinessSn());
        createDTO.setBusinessType(notifyDTO.getBusinessType());
        createDTO.setRiskSn(notifyDTO.getRiskSn());

        // 处理风控规则信息
        buildRiskRuleInfo(createDTO, notifyDTO);

        createDTO.setRiskType(RiskReviewRiskTypeEnum.FIRST_REVIEW.getValue()); // 初审
        createDTO.setIssuerId(reviews.getIssuerId());
        createDTO.setUid(reviews.getUid());
        createDTO.setPlateNo(reviews.getPlateNo());
        createDTO.setPlateColor(parsePlateColor(reviews.getPlateColor()));

        return createDTO;
    }

    /**
     * 构建风控规则信息
     */
    private void buildRiskRuleInfo(RiskReviewCreateDTO createDTO, RiskResultNotifyDTO notifyDTO) {
        if (ObjectUtils.isNotEmpty(notifyDTO.getRuleItems())) {
            StringBuilder riskRuleIds = new StringBuilder();
            StringBuilder riskRuleRemark = new StringBuilder();
            for (RiskResultNotifyDTO.RuleItem ruleItem : notifyDTO.getRuleItems()) {
                riskRuleIds.append("\"").append(ruleItem.getRuleId()).append("\",");
                riskRuleRemark.append(ruleItem.getRuleName()).append(";");
            }
            createDTO.setRiskRuleIds(riskRuleIds.toString());
            createDTO.setRiskRuleRemark(riskRuleRemark.toString());
        }
    }

    /**
     * 解析车牌颜色
     */
    private int parsePlateColor(String plateColorStr) {
        int plateColor = 0;
        try {
            if (plateColorStr != null && !plateColorStr.isEmpty()) {
                plateColor = Integer.parseInt(plateColorStr);
            }
        } catch (Exception e) {
            log.warn("plateColor转换失败，原值:{}", plateColorStr);
        }
        return plateColor;
    }

    /**
     * 记录审核日志
     */
    private void addReviewLog(Integer reviewId, String operateContent) {
        ReviewLogBO logBO = new ReviewLogBO();
        logBO.setReviewId(reviewId);
        logBO.setOperator("system");
        logBO.setType(ReviewLogTypeEnum.TYPE_MODIFY.getValue());
        logBO.setOperateContent(operateContent);
        reviewsLogService.addLog(logBO);
    }

    /**
     * 通知申办审核处理审核单
     */
    private void notifyReviewRiskResult(String reviewSn) {
        ReviewRiskResultNotifyDTO riskResultNotifyDTO = new ReviewRiskResultNotifyDTO();
        riskResultNotifyDTO.setReviewSn(reviewSn);
        String result = phpIssuerAdminFeign.reviewRiskResultNotify(riskResultNotifyDTO);
        JsonResult<Object> jsonResult = JsonResult.convertFromJsonStr(result, Object.class);
        jsonResult.checkError();
    }
}
