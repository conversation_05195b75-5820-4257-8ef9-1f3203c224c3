package com.ets.delivery.application.app.business;

import com.alibaba.fastjson.JSON;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.common.bo.productOrder.CreateOrderFromExternalBO;
import com.ets.delivery.application.common.config.MaoChaoConfig;
import com.ets.delivery.application.common.consts.erpRecord.ErpRecordOrderTypeEnum;
import com.ets.delivery.application.common.consts.erpRecord.ErpRecordStatusEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaLogiticsCodeEnum;
import com.ets.delivery.application.common.dto.maoChao.MaoChaoDeliveryNotifyDTO;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.common.micro.CustomerHttpServletRequestWrapper;
import com.ets.delivery.application.common.utils.PopSignValidator;
import com.ets.delivery.application.common.vo.maoChao.MaoChaoResultVO;
import com.ets.delivery.application.common.vo.productOrder.ProductOrderExternalCreateVO;
import com.ets.delivery.application.infra.entity.ErpRecord;
import com.ets.delivery.application.infra.service.ErpRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class MaoChaoBusiness {

    @Autowired
    private MaoChaoConfig maoChaoConfig;

    @Autowired
    private ProductOrderBusiness productOrderBusiness;

    @Autowired
    private ErpRecordService erpRecordService;

    public void verifySign(HttpServletRequest request) {

        CustomerHttpServletRequestWrapper requestWrapper = new CustomerHttpServletRequestWrapper(request);

        PopSignValidator.addAccessKey(maoChaoConfig.getAccessKeyId(), maoChaoConfig.getAccessKeySecret());

        // 验签
        PopSignValidator.validateSignature(requestWrapper);
    }

    public MaoChaoResultVO deliveryOrderConfirm(MaoChaoDeliveryNotifyDTO requestDto, HttpServletRequest request) {

        MaoChaoResultVO vo = new MaoChaoResultVO();

        try {

            if (maoChaoConfig.getVerify()) {
                verifySign(request);
            }

            MaoChaoDeliveryNotifyDTO.RequestInfo dto = requestDto.getRequest();

            if (dto == null || dto.getOrderType() == null || dto.getOrderType() != 0) {
                vo.setSuccess(true);
                return vo;
            }

            createErpRecord(dto, request);

            // 创建电商订单
            //createExternalOrder(dto);

            vo.setSuccess(true);

        } catch (Exception e) {

            log.error("猫超通知处理失败：" + e.getMessage());

            vo.setSuccess(false);
            vo.setErrorCode(vo.getErrorCode());
            vo.setErrorMsg(e.getMessage());
        }

        return vo;
    }

    public void createErpRecord(MaoChaoDeliveryNotifyDTO.RequestInfo dto, HttpServletRequest request) {

        // 已存在则直接返回
        ErpRecord exists = erpRecordService.getExists(dto.getConsignOrderCode(), "MaoChao");
        if (exists != null) {
            log.info("猫超订单重复通知：" + dto.getConsignOrderCode());
            return;
        }

        ErpRecord erpRecord = new ErpRecord();
        erpRecord.setErpSn(dto.getConsignOrderCode());
        erpRecord.setErpOrderSource("MaoChao");
        erpRecord.setErpOrderType(ErpRecordOrderTypeEnum.NORMAL.getValue());
        erpRecord.setOrderPlatform(dto.getSourcePlatformCode());
        erpRecord.setThirdOrderSn("");
        erpRecord.setPaidAmount(dto.getTotalAmount());
        erpRecord.setPaymentSn("");
        erpRecord.setPaidAt(ToolsHelper.getLocalDateTime(dto.getPayTime()));
        // 记录收货人信息
        MaoChaoDeliveryNotifyDTO.ReceiverDO receiverDO = dto.getReceiverDO();

        erpRecord.setSendName(receiverDO.getReceiverName());
        erpRecord.setSendPhone(receiverDO.getReceiverMobile());
        erpRecord.setSendArea(getSendAreaFromReceiverDO(receiverDO));

        String[] addressInfo = receiverDO.getReceiverAddress().replace("   ", "").split(" ");
        String address = "";
        if (addressInfo.length == 2) {
            address = addressInfo[1];
        }
        erpRecord.setSendAddress(address);

        erpRecord.setExpressNumber(dto.getMailNo());
        erpRecord.setExpressCorp(YundaLogiticsCodeEnum.map.getOrDefault(dto.getTmsServiceCode(), "未知"));
        erpRecord.setDeliveryTime(ToolsHelper.getLocalDateTime(dto.getOperateTime()));
        erpRecord.setStatus(ErpRecordStatusEnum.WAITING.getValue());

        // 记录原数据
        erpRecord.setRawData(RequestHelper.getRequestBody(request));

        // 记录发货商品信息
        String itemInfo = JSON.toJSONString(getItems(dto));

        erpRecord.setLogisticsSku(itemInfo);

        erpRecordService.create(erpRecord);

        // 生成处理erp订单任务
        TaskRecordDTO recordDTO = new TaskRecordDTO();
        recordDTO.setReferType(TaskRecordReferTypeEnum.TASK_ERP_ORDER_HANDLE.getType());
        recordDTO.setReferSn(erpRecord.getErpSn());
        TaskFactory.create(TaskRecordReferTypeEnum.TASK_ERP_ORDER_HANDLE).addAndPush(recordDTO);
    }

    public String getSendAreaFromReceiverDO(MaoChaoDeliveryNotifyDTO.ReceiverDO receiverDO) {

        return receiverDO.getReceiverProvince() + " " + receiverDO.getReceiverCity() + " " + receiverDO.getReceiverArea();
    }

    public List<CreateOrderFromExternalBO.Item> getItems(MaoChaoDeliveryNotifyDTO.RequestInfo dto) {

        List<CreateOrderFromExternalBO.Item> items = new ArrayList<>();

        if (dto.getTmsOrderList() == null) {
            ToolsHelper.throwException("商品信息为空");
        }

        dto.getTmsOrderList().forEach(itemOrder -> {

            itemOrder.getTmsItems().forEach(itemExternal -> {

                CreateOrderFromExternalBO.Item item = new CreateOrderFromExternalBO.Item();

                String packageSn = "";
                if (maoChaoConfig.getPackageSnMap() != null
                        && maoChaoConfig.getPackageSnMap().get(itemExternal.getBarCode()) != null) {

                    packageSn = maoChaoConfig.getPackageSnMap().get(itemExternal.getBarCode());
                }

                //String[] packageInfo = itemExternal.getScItemName().split("#");
                //if (packageInfo.length > 1 && StringUtils.isNotEmpty(packageInfo[1])) {
                //    packageSn = packageInfo[1];
                //}

                if (StringUtils.isNotEmpty(packageSn)) {

                    item.setPackageSn(packageSn);
                    item.setCount(itemExternal.getItemActualQty());
                    item.setItemAmount(BigDecimal.valueOf(itemExternal.getItemAmount() / 100));

                    String thirdOrderSn = itemExternal.getTradeOrderId();
                    if (thirdOrderSn.length() < 5) {
                        thirdOrderSn = itemExternal.getSubTradeOrderId();
                    }

                    item.setThirdOrderSn(thirdOrderSn);
                    items.add(item);
                } else {
                    if (itemExternal.getScItemName().contains("etc")
                      || itemExternal.getScItemName().contains("ETC")
                    ) {
                        log.error("猫超商品未配置：" + itemExternal.getScItemName());
                    }
                }

            });
        });

        return items;
    }

    // 创建外部订单记录 todo
    public void createExternalOrder(MaoChaoDeliveryNotifyDTO.RequestInfo dto) {

        // 创建外部订单记录

        // 创建电商订单 以下是demo; 正常由task处理
        try {
            CreateOrderFromExternalBO bo = new CreateOrderFromExternalBO();
            MaoChaoDeliveryNotifyDTO.ReceiverDO receiverDO = dto.getReceiverDO();
            bo.setPhone(receiverDO.getReceiverMobile());
            bo.setPaidAmount(dto.getTotalAmount());
            bo.setThirdOrderSn(dto.getConsignOrderCode());
            bo.setSendName(receiverDO.getReceiverName());
            bo.setSendArea(getSendAreaFromReceiverDO(receiverDO));
            bo.setSendTime(ToolsHelper.getLocalDateTime(dto.getOperateTime()));
            bo.setLogisticCompany(YundaLogiticsCodeEnum.map.getOrDefault(dto.getTmsServiceCode(), "未知"));
            bo.setLogisticNumber(dto.getMailNo());

            bo.setItems(getItems(dto));

            List<ProductOrderExternalCreateVO> list = productOrderBusiness.createOrderFromExternal(bo);

            //log.info(JSON.toJSONString(list));

        } catch (Exception e) {
            // 异常记录
            log.error(e.getMessage());
        }
    }

}
