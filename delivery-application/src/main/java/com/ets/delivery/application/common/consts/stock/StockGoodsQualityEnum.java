package com.ets.delivery.application.common.consts.stock;

import com.ets.delivery.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum StockGoodsQualityEnum {

    GOOD(1, "良品", "ZP"),

    BAD(2, "次品", "CC");

    private final Integer code;
    private final String description;
    private final String inventoryType;

    public static String getDescByCode(int code) {

        for (StockGoodsQualityEnum node : StockGoodsQualityEnum.values()) {

            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static String getInventoryTypeByCode(int code) {

        for (StockGoodsQualityEnum node : StockGoodsQualityEnum.values()) {

            if (node.getCode() == code) {
                return node.getInventoryType();
            }
        }

        return "";
    }

    public static StockGoodsQualityEnum getByCode(int code) {

        for (StockGoodsQualityEnum node : StockGoodsQualityEnum.values()) {

            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        List<SelectOptionsVO> selectOptionsVOList = new ArrayList<>();
        for (StockGoodsQualityEnum node : StockGoodsQualityEnum.values()) {
            selectOptionsVOList.add(new SelectOptionsVO(node.getCode().toString(),node.getDescription()));
        }

        return selectOptionsVOList;
    }
}
