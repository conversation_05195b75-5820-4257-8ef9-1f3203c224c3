package com.ets.delivery.application.controller.admin;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.common.annotation.CosSignAnnotation;
import com.ets.delivery.application.app.business.PostReviewBusiness;
import com.ets.delivery.application.common.dto.postReviews.*;
import com.ets.delivery.application.common.vo.ReviewListVO;
import com.ets.delivery.application.common.vo.postReviews.PostReviewAbnormalListVO;
import com.ets.delivery.application.common.vo.postReviews.PostReviewVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 自动审核后审记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@RestController
@RequestMapping("/admin/postReviews")
public class PostReviewsController {

    @Autowired
    PostReviewBusiness postReviewBusiness;

    @RequestMapping("/getList")
    public JsonResult<IPage<ReviewListVO>> getList(@RequestBody @Validated PostReviewListDTO listDTO) {
        return JsonResult.ok(postReviewBusiness.getList(listDTO));
    }

    @RequestMapping("/batchReceive")
    @CosSignAnnotation
    public JsonResult<List<PostReviewVO>> batchReceive(@RequestBody PostReviewBatchReceiveDTO receiveDTO) {
        return JsonResult.ok(postReviewBusiness.batchReceive(receiveDTO));
    }

    @RequestMapping("/batchAuditPass")
    public JsonResult<List<PostReviewVO>> batchAuditPass(@RequestBody @Validated PostReviewBatchAuditPassDTO batchAuditPassDTO) {
        postReviewBusiness.batchAuditPass(batchAuditPassDTO);
        // 获取下一批
        List<PostReviewVO> postReviewVOList = new ArrayList<>();
        if (batchAuditPassDTO.isNeedNext()) {
            PostReviewBatchReceiveDTO receiveDTO = new PostReviewBatchReceiveDTO();
            receiveDTO.setSort(batchAuditPassDTO.getSort());
            receiveDTO.setEmergencyType(batchAuditPassDTO.getEmergencyType());
            postReviewVOList = postReviewBusiness.batchReceive(receiveDTO);
        }
        return JsonResult.ok(postReviewVOList);
    }

    @RequestMapping("/abnormal")
    public JsonResult<?> abnormal(@RequestBody @Validated PostReviewAbnormalDTO abnormalDTO) {
        postReviewBusiness.abnormal(abnormalDTO.getId());
        return JsonResult.ok();
    }

    @RequestMapping("/getAbnormalList")
    public JsonResult<IPage<PostReviewAbnormalListVO>> getAbnormalList(
            @RequestBody @Validated PostReviewAbnormalListDTO abnormalListDTO) {
        return JsonResult.ok(postReviewBusiness.getAbnormalList(abnormalListDTO));
    }

    @RequestMapping("/handleException")
    public JsonResult<?> handleException(@RequestBody @Validated PostReviewHandleDTO handleDTO) {
        postReviewBusiness.handleException(handleDTO);
        return JsonResult.ok();
    }
}

