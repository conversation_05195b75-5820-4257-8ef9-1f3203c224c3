package com.ets.delivery.application.infra.service;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.PostReviewUserSummary;
import com.ets.delivery.application.infra.mapper.PostReviewUserSummaryMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 审核人员后审统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-29
 */
@Service
public class PostReviewUserSummaryService extends BaseService<PostReviewUserSummaryMapper, PostReviewUserSummary> {

    public List<PostReviewUserSummary> getListByDate(LocalDate start, LocalDate end) {
        Wrapper<PostReviewUserSummary> wrapper = Wrappers.<PostReviewUserSummary>lambdaQuery()
                .ge(PostReviewUserSummary::getReviewDate, start)
                .le(PostReviewUserSummary::getReviewDate, end);

        return this.baseMapper.selectList(wrapper);
    }

    public PostReviewUserSummary getOneByNameAndDate(String username, LocalDate date) {
        Wrapper<PostReviewUserSummary> wrapper = Wrappers.<PostReviewUserSummary>lambdaQuery()
                .eq(PostReviewUserSummary::getUserName, username)
                .eq(PostReviewUserSummary::getReviewDate, date)
                .last("limit 1");

        return this.baseMapper.selectOne(wrapper);
    }
}
