package com.ets.delivery.application.app.factory.importFile.impl;

import com.alibaba.excel.EasyExcel;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.SendBackBusiness;
import com.ets.delivery.application.app.excellistener.RejectExpressListener;
import com.ets.delivery.application.common.consts.importFile.ImportFileUploadStatusEnum;
import com.ets.delivery.application.common.consts.sendback.SendBackOrderTypeEnum;
import com.ets.delivery.application.common.dto.rejectExpress.RejectExpressImportDTO;
import com.ets.delivery.application.infra.entity.ImportFileRecord;
import com.ets.delivery.application.infra.entity.ImportRejectExpressDetail;
import com.ets.delivery.application.infra.entity.LogisticsSendBack;
import com.ets.delivery.application.infra.entity.StorageRecord;
import com.ets.delivery.application.infra.service.ImportFileRecordService;
import com.ets.delivery.application.infra.service.ImportRejectExpressDetailService;
import com.ets.delivery.application.infra.service.LogisticsSendBackService;
import com.ets.delivery.application.infra.service.StorageRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
public class ImportFileRejectExpress extends ImportFileBase<ImportRejectExpressDetail> {

    @Autowired
    private ImportFileRecordService importFileRecordService;

    @Autowired
    private ImportRejectExpressDetailService importRejectExpressDetailService;

    @Autowired
    private StorageRecordService storageRecordService;

    @Autowired
    private LogisticsSendBackService sendBackService;

    @Autowired
    private SendBackBusiness sendBackBusiness;

    @Override
    public void importFile(MultipartFile file, ImportFileRecord fileRecord) {
        try {
            EasyExcel.read(file.getInputStream(),
                            RejectExpressImportDTO.class,
                            new RejectExpressListener(fileRecord.getBatchNo(), this))
                    .sheet()
                    .headRowNumber(1)
                    .doRead();
        } catch (IOException e) {
            log.error("导入文件失败：{}", e.getLocalizedMessage());
            // 更新导入失败
            fileRecord.setUploadStatus(ImportFileUploadStatusEnum.FAIL.getValue());
            fileRecord.setErrorMsg("导入文件失败");
            fileRecord.setUpdatedAt(LocalDateTime.now());
            importFileRecordService.updateById(fileRecord);
            ToolsHelper.throwException("导入文件失败");
        } catch (Throwable e) {
            fileRecord.setUploadStatus(ImportFileUploadStatusEnum.FAIL.getValue());
            fileRecord.setErrorMsg(e.getLocalizedMessage());
            fileRecord.setUpdatedAt(LocalDateTime.now());
            importFileRecordService.updateById(fileRecord);
            ToolsHelper.throwException(e.getLocalizedMessage());
        }
    }

    @Override
    public void saveImportFileData(String batchNo, List<ImportRejectExpressDetail> dataList) {
        // 批量保存数据
        importRejectExpressDetailService.saveBatch(dataList);

        // 更新导入结果
        ImportFileRecord importFileRecord = importFileRecordService.getByBatchNo(batchNo);
        importFileRecord.setTotal(dataList.size());
        importFileRecord.setUploadStatus(ImportFileUploadStatusEnum.SUCCESS.getValue());
        importFileRecord.setUpdatedAt(LocalDateTime.now());
        importFileRecordService.updateById(importFileRecord);
    }

    public String checkImportError(RejectExpressImportDTO rejectExpressImportDTO) {
        String msg = "";
        String pattern = "^[0-9A-Z]{1,30}$";

        // 寄回快递单号校验
        if (StringUtils.isEmpty(rejectExpressImportDTO.getSendbackExpressNumber())) {
            msg += "寄回快递单号不能为空；";
        } else {
            if (!rejectExpressImportDTO.getSendbackExpressNumber().trim().matches(pattern)) {
                msg += "寄回快递单号格式不正确；";
            }

            ImportRejectExpressDetail detail = importRejectExpressDetailService.getOneBySendBackExpressNumber(rejectExpressImportDTO.getSendbackExpressNumber());
            if (ObjectUtils.isNotEmpty(detail)) {
                msg += "寄回快递单号已存在导入记录，不支持重复导入；";
            }

            LogisticsSendBack sendBack = sendBackService.getByExpressNumber(rejectExpressImportDTO.getSendbackExpressNumber());
            if (ObjectUtils.isNotEmpty(sendBack) && StringUtils.isNotEmpty(sendBack.getOriginExpressNumber())) {
                msg += "寄回件快递单号系统已有更新记录，不支持导入；";
            }
        }

        // 入库快递单号校验
        if (StringUtils.isEmpty(rejectExpressImportDTO.getStorageExpressNumber())) {
            msg += "入库快递单号不能为空；";
        } else {
            if (!rejectExpressImportDTO.getStorageExpressNumber().trim().matches(pattern)) {
                msg += "入库快递单号格式不正确；";
            }

            StorageRecord storageRecord = storageRecordService.getByExpressNumber(rejectExpressImportDTO.getStorageExpressNumber());
            if (ObjectUtils.isEmpty(storageRecord)) {
                msg += "系统查询不到入库快递单号；";
            }

            ImportRejectExpressDetail detail = importRejectExpressDetailService.getOneByStorageExpressNumber(rejectExpressImportDTO.getStorageExpressNumber());
            if (ObjectUtils.isNotEmpty(detail)) {
                msg += "入库快递单号已存在导入记录，不支持重复导入；";
            }
        }

        return msg;
    }

    public boolean checkMatchResult(RejectExpressImportDTO rejectExpressImportDTO) {
        LogisticsSendBack sendBack = sendBackService.getByExpressNumber(rejectExpressImportDTO.getSendbackExpressNumber());
        if (ObjectUtils.isEmpty(sendBack)) {
            return false;
        }

        // 非拒收类型不处理
        if (!sendBack.getOrderType().equals(SendBackOrderTypeEnum.AFTER_SALES_REJECT.getValue())) {
            return false;
        }

        StorageRecord storageRecord = storageRecordService.getByExpressNumber(rejectExpressImportDTO.getStorageExpressNumber());
        if (ObjectUtils.isEmpty(storageRecord)) {
            return false;
        }

        try {
            // 更新快递单号
            sendBack = sendBackBusiness.updateOriginExpressNumber(sendBack,
                    rejectExpressImportDTO.getStorageExpressNumber(),
                    rejectExpressImportDTO.getSendbackExpressNumber(),
                    true
            );

            // 检查并更新寄回件状态
            sendBackBusiness.checkAndUpdate(sendBack, storageRecord);
        } catch (Throwable e) {
            log.error("寄回件检查通知异常：{}", e.getLocalizedMessage());
        }
        return true;
    }
}