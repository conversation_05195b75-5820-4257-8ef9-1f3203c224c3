package com.ets.delivery.application.common.dto.supplyGoods;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

@Data
public class SupplyGoodsListDTO {

    /**
     * 所属仓储代号
     */
    private String storageCode;

    /**
     * 货品类型：主要为card,obu,card_obu
     */
    private String goodsType;

    /**
     * 状态,0不可使用，1可使用
     */
    private Integer status;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
