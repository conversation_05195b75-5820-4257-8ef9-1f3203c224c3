package com.ets.delivery.application.infra.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.delivery.feign.request.logistics.SendBackCreateByGoodsDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 寄回件列表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_logistics_sendback")
public class LogisticsSendBack extends BaseEntity<LogisticsSendBack> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 寄回件流水号
     */
    private String sendbackSn;

    /**
     * 订单流水号
     */
    private String orderSn;

    /**
     * 原始订单号（冗余）
     */
    private String originOrderSn;

    /**
     * app_id
     */
    private String appId;

    /**
     * 对接方id
     */
    private Integer issuerId;

    /**
     * 发卡方名称
     */
    private String issuerName;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 所属仓储
     */
    private String storageCode;

    /**
     * 供应商编号
     */
    private String supplyCode;

    /**
     * 货品编号
     */
    private String goodsCode;

    /**
     * 寄回数量
     */
    private Integer nums;

    /**
     * [{"skuSn":"sku编号","count":数量,"skuName":"sku名称"}]
     */
    private String goodsSkuInfo;

    /**
     * 发件人
     */
    private String sendName;

    /**
     * 发件人联系手机
     */
    private String sendPhone;

    /**
     * 发件地区
     */
    private String sendArea;

    /**
     * 发件地址
     */
    private String sendAddress;

    /**
     * 接收人
     */
    private String reviceName;

    /**
     * 接收人联系手机
     */
    private String revicePhone;

    /**
     * 接收地区
     */
    private String reviceArea;

    /**
     * 接收地址
     */
    private String reviceAddress;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 原发货快递单号
     */
    private String originExpressNumber;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 状态：1正常2取消3暂停
     */
    private Integer status;

    /**
     * 接收状态,0待接收，1已入库，2检查正常，3检查异常
     */
    private Integer reviceStatus;

    /**
     * 接收备注
     */
    private String reviceRemark;

    /**
     * 接收时间
     */
    private LocalDateTime reviceTime;

    /**
     * 检查寄回件时间
     */
    private LocalDateTime checkTime;

    /**
     * 回调通知路径
     */
    private String notifyBackUrl;

    /**
     * 通知状态,0待通知，1通知中，2通知成功，3通知失败，4通知取消
     */
    private Integer notifyStatus;

    /**
     * 通知备注
     */
    private String notifyRemark;

    /**
     * 通知时间
     */
    private LocalDateTime notifyTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    public List<SendBackCreateByGoodsDTO.GoodsSku>  getSkuInfoList() {

        if (StringUtils.isEmpty(goodsSkuInfo)) {
            return null;
        }

        return JSON.parseArray(goodsSkuInfo, SendBackCreateByGoodsDTO.GoodsSku.class);
    }


}
