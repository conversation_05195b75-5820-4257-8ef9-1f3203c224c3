package com.ets.delivery.application.common.dto.postReviews;

import com.ets.delivery.application.common.consts.ChannelIdEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;

@Data
public class PostReviewListDTO {

    /**
     * 车牌号码
     */
    private String plateNo;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 渠道枚举值
     */
    private Integer channelId;

    /**
     * 渠道值
     */
    private String channel;

    /**
     * 后审类型
     */
    private Integer emergencyType;

    /**
     * 是否正序
     */
    private String sort = "asc";

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime createStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime createEndTime;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;


    public String getChannel() {
        return ChannelIdEnum.map.getOrDefault(this.channelId, null);
    }
}
