package com.ets.delivery.application.common.vo.riskreview;

import com.ets.delivery.application.common.vo.MultiSelectOptionVO;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AdminRiskReviewsSelectOptionVO {
    // 风控类型
    private List<Map<String, String>> riskTypeList;
    // 发卡方
    private List<Map<String, String>> issuerIdList;
    // 审核状态
    private List<Map<String, String>> riskReviewStatusList;
    // 自动审核
    private List<Map<String, String>> autoAuditList;
    // 驳回原因（二级联动）
    private List<MultiSelectOptionVO> rejectReasonList;
    // 风控原因
    private List<Map<String, String>> riskRuleList;
}
