package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.ExWarehouseSplitDetail;
import com.ets.delivery.application.infra.mapper.ExWarehouseSplitDetailMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 出库单拆单商品详细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Service
@DS("db-issuer-admin")
public class ExWarehouseSplitDetailService extends BaseService<ExWarehouseSplitDetailMapper, ExWarehouseSplitDetail> {

    public ExWarehouseSplitDetail getOneBySplitOrderNoAndGoodsNo(String splitOrderNo, String goodsNo) {
        Wrapper<ExWarehouseSplitDetail> wrapper = Wrappers.<ExWarehouseSplitDetail>lambdaQuery()
                .eq(ExWarehouseSplitDetail::getSplitOrderNo, splitOrderNo)
                .eq(ExWarehouseSplitDetail::getGoodsNo, goodsNo)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
