package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务执行日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TaskRecordLog extends BaseEntity<TaskRecordLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务流水号
     */
    private String taskSn;

    /**
     * 执行结果内容
     */
    private String execContent;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
