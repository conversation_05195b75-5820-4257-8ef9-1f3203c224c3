package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.common.vo.SelectOptionsVO;
import com.ets.delivery.application.infra.entity.Storage;
import com.ets.delivery.application.infra.mapper.StorageMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@DS("db-issuer-admin")
public class StorageService extends BaseService<StorageMapper, Storage> {
    /*
     * 通过仓库编码获取仓库
     */
    public Storage getOneByStorageCode(String storageCode){
        Wrapper<Storage> wrapper = Wrappers.<Storage>lambdaQuery()
                .eq(Storage::getStorageCode, storageCode)
                .eq(Storage::getStatus, 1)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public String getStorageNameByCode(String storageCode) {

        Storage storage = getOneByStorageCode(storageCode);
        if (storage == null) {
            return "";
        }

        return storage.getName();
    }

    /*
     * 获取正常仓库列表下拉选择项
     */
    public List<SelectOptionsVO> getSelectOptions(){
        List<SelectOptionsVO> selectOptionsVOList = new ArrayList<>();
        // 查询条件设置
        LambdaQueryWrapper<Storage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Storage::getStatus, 1);
        List<Storage> list = this.getBaseMapper().selectList(wrapper);
        list.forEach(v -> {
            selectOptionsVOList.add(new SelectOptionsVO(v.getStorageCode(),v.getName()));
        });
        return selectOptionsVOList;
    }

    public List<Storage> getListByCodes(List<String> storageCodeList) {

        return getListBySns(storageCodeList, Storage::getStorageCode);
    }

    public List<Storage> getAllList() {

        LambdaQueryWrapper<Storage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Storage::getStatus, 1);

        return getListByWrapper(wrapper);
    }
}
