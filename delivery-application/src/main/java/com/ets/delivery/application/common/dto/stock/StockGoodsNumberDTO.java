package com.ets.delivery.application.common.dto.stock;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class StockGoodsNumberDTO {
    /**
     * 产品
     */
    @NotNull(message = "请选择产品")
    private Integer product;

    /**
     * 省份
     */
    @NotNull(message = "请选择省份")
    private Integer issuerId;

    /**
     * 1客车，2货车
     */
    @NotNull(message = "请选择客货车")
    private Integer carType;

    /**
     * 厂商
     */
    @NotNull(message = "请选择厂商")
    private Integer manufacturer;

    /**
     * 开始号段
     */
    @NotBlank(message = "请填写开始号段")
    private String beginNumber;

    /**
     * 数量
     */
    @NotNull(message = "请填写数量")
    private Integer count;

    /**
     * 结束号段
     */
    private String endNumber;

}
