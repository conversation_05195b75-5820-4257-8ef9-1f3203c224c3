package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.consts.manualLogistics.ImportManualLogisticsRecordStatusEnum;
import com.ets.delivery.application.common.consts.manualLogistics.ImportManualLogisticsStatusEnum;
import com.ets.delivery.application.common.consts.manualLogistics.ManualLogisticsResultLevelEnum;
import com.ets.delivery.application.common.dto.manualLogistics.ManualLogisticsImportListDTO;
import com.ets.delivery.application.infra.entity.ImportManualLogisticsDetail;
import com.ets.delivery.application.infra.mapper.ImportManualLogisticsDetailMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 手动下单发货导入详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Service
@DS("db-issuer-admin")
public class ImportManualLogisticsDetailService extends BaseService<ImportManualLogisticsDetailMapper, ImportManualLogisticsDetail> {

    public IPage<ImportManualLogisticsDetail> getPage(ManualLogisticsImportListDTO listDTO) {
        Wrapper<ImportManualLogisticsDetail> wrapper = Wrappers.<ImportManualLogisticsDetail>lambdaQuery()
                .eq(ImportManualLogisticsDetail::getBatchNo, listDTO.getBatchNo())
                .eq(ImportManualLogisticsDetail::getStatus, ImportManualLogisticsStatusEnum.NORMAL.getValue())
                .eq(ObjectUtils.isNotEmpty(listDTO.getRecordStatus()), ImportManualLogisticsDetail::getRecordStatus, listDTO.getRecordStatus())
                .orderByAsc(ImportManualLogisticsDetail::getResultLevel)
                .orderByDesc(ImportManualLogisticsDetail::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }

    public List<ImportManualLogisticsDetail> getCanSaveList(String batchNo) {
        Wrapper<ImportManualLogisticsDetail> wrapper = Wrappers.<ImportManualLogisticsDetail>lambdaQuery()
                .eq(ImportManualLogisticsDetail::getBatchNo, batchNo)
                .in(ImportManualLogisticsDetail::getResultLevel, Arrays.asList(
                        ManualLogisticsResultLevelEnum.LEVEL_WARNING.getValue(),
                        ManualLogisticsResultLevelEnum.LEVEL_NORMAL.getValue()
                ))
                .eq(ImportManualLogisticsDetail::getStatus, ImportManualLogisticsStatusEnum.NORMAL.getValue())
                .eq(ImportManualLogisticsDetail::getRecordStatus, ImportManualLogisticsRecordStatusEnum.DEFAULT.getValue());
        return this.baseMapper.selectList(wrapper);
    }

    public List<ImportManualLogisticsDetail> getCanPushList(String batchNo) {
        Wrapper<ImportManualLogisticsDetail> wrapper = Wrappers.<ImportManualLogisticsDetail>lambdaQuery()
                .eq(ImportManualLogisticsDetail::getBatchNo, batchNo)
                .eq(ImportManualLogisticsDetail::getStatus, ImportManualLogisticsStatusEnum.NORMAL.getValue())
                .eq(ImportManualLogisticsDetail::getRecordStatus, ImportManualLogisticsRecordStatusEnum.SAVED.getValue());
        return this.baseMapper.selectList(wrapper);
    }
}
