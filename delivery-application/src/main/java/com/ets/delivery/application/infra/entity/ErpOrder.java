package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * erp业务映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_erp_order")
public class ErpOrder extends BaseEntity<ErpOrder> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * erp流水号
     */
    private String erpSn;

    /**
     * 业务订单号
     */
    private String businessSn;

    /**
     * 物流单号
     */
    private String logisticsSn;

    /**
     * 错误原因
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
