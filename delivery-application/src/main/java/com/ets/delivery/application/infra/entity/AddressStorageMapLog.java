package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 地址仓储映射日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_address_storage_map_log")
public class AddressStorageMapLog extends BaseEntity<AddressStorageMapLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置ID
     */
    private Integer configId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作描述
     */
    private String operateDesc;

    /**
     * 操作前待变更的数据
     */
    private String preOperate;

    /**
     * 操作后的数据
     */
    private String afterOperate;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
