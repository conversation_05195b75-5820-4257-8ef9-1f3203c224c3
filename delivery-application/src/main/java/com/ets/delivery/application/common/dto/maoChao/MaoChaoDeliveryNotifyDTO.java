package com.ets.delivery.application.common.dto.maoChao;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class MaoChaoDeliveryNotifyDTO {

    @NotNull
    private RequestInfo request;

    @Data
    public static class RequestInfo {
        /**
         * 销售物流单号（主单号）
         */
        @NotBlank
        private String consignOrderCode;

        private Integer orderType;

        /**
         * 发货时间
         */
        @NotBlank
        private String operateTime;

        /**
         * 快递公司名称
         */
        //private String tmsServiceName;

        /**
         * 快递公司编号
         */
        private String tmsServiceCode;

        /**
         * 快递单号
         */
        private String mailNo;

        /**
         * 订单金额
         */
        private BigDecimal totalAmount;

        /**
         * 来源平台编号
         */
        private String sourcePlatformCode;

        /**
         * 支付时间
         */
        private String payTime;

        @NotNull
        private List<ItemOrder> tmsOrderList;

        /**
         * 收件方信息(消费者)
         */
        private ReceiverDO receiverDO;
    }

    @Data
    public static class ItemOrder {
        /**
         * 商品信息
         */
        @NotNull
        private List<Item> tmsItems;

    }

    @Data
    public static class Item {

        /**
         * 渠道订单号
         */
        @NotBlank
        private String tradeOrderId;

        private String subTradeOrderId;

        /**
         * 货品编码
         */
        @NotBlank
        private String barCode;

        /**
         * 商品名称
         */
        private String scItemName;

        /**
         * 数量
         */
        @NotNull
        private Integer itemActualQty;

        /**
         * 商品金额，单位分
         */
        private Integer itemAmount;
    }

    @Data
    public static class ReceiverDO {

        /**
         * 收货省
         */
        private String receiverProvince;

        /**
         * 市
         */
        private String receiverCity;

        /**
         * 区
         */
        private String receiverArea;

        /**
         * 详细地址
         */
        private String receiverAddress;

        /**
         * 收货人姓名，脱敏
         */
        private String receiverName;

        /**
         * 收货人手机号，脱敏
         */
        private String receiverMobile;
    }

}
