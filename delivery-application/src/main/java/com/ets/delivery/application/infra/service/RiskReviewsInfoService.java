package com.ets.delivery.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsInfo;
import com.ets.delivery.application.infra.mapper.RiskReviewsInfoMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 风控资料记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
public class RiskReviewsInfoService extends BaseService<RiskReviewsInfoMapper, RiskReviewsInfo> {

    public RiskReviewsInfo getByRiskReviewSn(String riskReviewSn) {
        Wrapper<RiskReviewsInfo> wrapper = Wrappers.<RiskReviewsInfo>lambdaQuery()
                .eq(RiskReviewsInfo::getRiskReviewSn, riskReviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
