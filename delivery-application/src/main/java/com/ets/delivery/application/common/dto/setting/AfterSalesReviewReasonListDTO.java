package com.ets.delivery.application.common.dto.setting;

import com.ets.delivery.application.common.consts.setting.SettingCategoryEnum;
import com.ets.delivery.application.common.consts.setting.SettingStatusEnum;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class AfterSalesReviewReasonListDTO {

    private String category;

    private String reason;

    private Integer status;

    @AssertTrue(message = "业务类型不正确")
    public boolean isValidCategory() {
        return this.category == null || SettingCategoryEnum.afterSalesList.contains(this.category);
    }

    @AssertTrue(message = "状态不正确")
    public boolean isValidStatus() {
        return this.status == null || SettingStatusEnum.list.contains(this.status);
    }

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
