package com.ets.delivery.application.common.vo.stock;

import lombok.Data;

import java.util.List;

@Data
public class StockGoodsInfoVO {

    /**
     * sku编号
     */
    private String skuSn;

    /**
     * 物流公司的商品编号
     */
    private String deliveryGoodsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 申请数量
     */
    private Integer applyCount;

    /**
     * 实际数量
     */
    private String realCount;

    /**
     * 产品
     */
    private Integer product;

    /**
     * 开始号段
     */
    private String beginNumber;

    /**
     * 结束号段
     */
    private String endNumber;

    private List<String> cardNumberList;

    private List<String> obuNumberList;

}
