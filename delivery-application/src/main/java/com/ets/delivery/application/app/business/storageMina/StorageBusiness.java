package com.ets.delivery.application.app.business.storageMina;

import com.ets.common.BeanHelper;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ListUtils;
import com.ets.delivery.application.common.vo.storage.StorageVO;
import com.ets.delivery.application.infra.entity.Storage;
import com.ets.delivery.application.infra.service.StorageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class StorageBusiness {

    @Autowired
    private StorageService storageService;

    public List<StorageVO> getListByCodeStr(String codeStr) {

        if (StringUtils.isEmpty(codeStr)) {
            return null;
        }

        List<String> storageCodeList = Arrays.asList(codeStr.split(","));

        List<Storage> list = storageService.getListByCodes(storageCodeList);

        List<StorageVO> voList = new ArrayList<>();
        list.forEach(storage -> {
            voList.add(BeanHelper.copy(StorageVO.class, storage));
        });

        return voList;
    }

    public Map<String, Storage> getListMap() {

        List<Storage> list = storageService.getAllList();

        return ListUtils.listToMap(list, Storage::getStorageCode);
    }

    public Storage getByStorageCode(String storageCode) {

        Storage storage = storageService.getOneByStorageCode(storageCode);

        if (storage == null) {
            ToolsHelper.throwException("仓库信息不存在");
        }

        return storage;
    }

}
