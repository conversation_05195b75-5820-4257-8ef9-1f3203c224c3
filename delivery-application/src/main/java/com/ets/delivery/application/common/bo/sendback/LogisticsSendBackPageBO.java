package com.ets.delivery.application.common.bo.sendback;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class LogisticsSendBackPageBO {
    private Integer issuerId;
    private String orderSn;
    private String plateNo;
    private String orderSource;
    private String orderType;
    private String expressNumber;
    private Integer receiveStatus;
    private Integer notifyStatus;
    private List<String> storageCodeList;
    private String sendbackSn;

    private LocalDateTime createStartTime;
    private LocalDateTime createEndTime;
    private LocalDateTime receiveStartTime;
    private LocalDateTime receiveEndTime;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
