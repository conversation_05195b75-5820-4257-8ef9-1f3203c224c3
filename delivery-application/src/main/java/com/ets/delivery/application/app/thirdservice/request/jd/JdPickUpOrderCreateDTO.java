package com.ets.delivery.application.app.thirdservice.request.jd;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Accessors(chain = true)
@Data
public class JdPickUpOrderCreateDTO {

    /**
     * 寄件地址，长度不能超过100个字符
     */
    String pickupAddress;
    /**
     * 寄件人，长度不能超过20个字符
     */
    String pickupName;
    /**
     * 寄件人联系电话
     */
    String pickupTel;

    /**
     * 商家编码/青龙业主号/配送编码/月结编码，与京东物流签约后生成，可咨询京东物流的销售
     */
    String customerCode;


    /**
     * 收件人联系电话，可以是座机电话
     */
    String customerTel;
    /**
     * 收货地址，长度不能超过50个字符
     */
    String backAddress;
    /**
     * 收货人，长度不能超过25个字符
     */
    String customerContract;

    /**
     * 取件描述
     */
    String desp;
    /**
     * 商家订单号，请保证商家编码下唯一，字段长度：1-50
     */
    String orderId;
    /**
     * 重量，单位：千克，取值范围：1-1000000，保留两位小数
     */
    Double weight;
    /**
     * 备注，长度不超过1200个字符
     */
    String remark;
    /**
     * 体积，单位：立方厘米，取值范围：1-1000000，保留两位小数
     */
    Double volume;

    /**
     * 商品清单。如果是配置的JDV单号则必填
     */
    List<ProductDetailDTO> goodsDtoList;

    /**
     * 开始取件时间，如忘传则快递员在下单一小时后至三天内取件，具体时间视当地站点业务排期而定，工作时间通常为9点至17点
     */
    Date pickupStartTime;
    /**
     * 结束取件时间，如忘传则快递员在下单一小时后至三天内取件，具体时间视当地站点业务排期而定，工作时间通常为9点至17点
     */
    Date pickupEndTime;
    /**
     * 产品类型 1:特惠送，2:特快送
     */
    Integer promiseTimeType = 1;

    @Data
    public static class ProductDetailDTO {
        /**
         * 商品名称
         */
        String productName;
        /**
         * 商品数量
         */
        Integer productCount;
    }
}
