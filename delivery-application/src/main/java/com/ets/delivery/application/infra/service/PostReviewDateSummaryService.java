package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.PostReviewDateSummary;
import com.ets.delivery.application.infra.mapper.PostReviewDateSummaryMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;


/**
 * <p>
 * 待后审统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-29
 */
@Service
@DS("db-issuer-admin")
public class PostReviewDateSummaryService extends BaseService<PostReviewDateSummaryMapper, PostReviewDateSummary> {

    public List<PostReviewDateSummary> getListNotZeroNum() {
        Wrapper<PostReviewDateSummary> wrapper = Wrappers.<PostReviewDateSummary>lambdaQuery()
                .ne(PostReviewDateSummary::getNum, 0);
        return this.baseMapper.selectList(wrapper);
    }

    public PostReviewDateSummary getOneByDateAndEmergency(LocalDate date, Integer emergencyType) {
        Wrapper<PostReviewDateSummary> wrapper = Wrappers.<PostReviewDateSummary>lambdaQuery()
                .eq(PostReviewDateSummary::getSummaryDate, date)
                .eq(PostReviewDateSummary::getEmergencyType, emergencyType)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public void resetDateSummary() {
        PostReviewDateSummary summary = new PostReviewDateSummary();
        summary.setNum(0);
        Wrapper<PostReviewDateSummary> wrapper = Wrappers.<PostReviewDateSummary>lambdaQuery()
                .ne(PostReviewDateSummary::getNum, 0);
        this.baseMapper.update(summary, wrapper);
    }
}
