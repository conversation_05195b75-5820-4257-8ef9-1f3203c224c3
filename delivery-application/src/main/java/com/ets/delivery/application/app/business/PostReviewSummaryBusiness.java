package com.ets.delivery.application.app.business;

import cn.hutool.core.util.ObjectUtil;
import com.ets.delivery.application.common.dto.postReviews.PostReviewBeReviewDTO;
import com.ets.delivery.application.common.vo.postReviews.PostReviewBeReviewVO;
import com.ets.delivery.application.common.vo.postReviews.PostReviewDateCountVO;
import com.ets.delivery.application.common.vo.postReviews.PostReviewWaitingReviewVO;
import com.ets.delivery.application.infra.entity.PostReviewDateSummary;
import com.ets.delivery.application.infra.entity.PostReviewUserSummary;
import com.ets.delivery.application.infra.entity.PostReviews;
import com.ets.delivery.application.infra.service.PostReviewDateSummaryService;
import com.ets.delivery.application.infra.service.PostReviewUserSummaryService;
import com.ets.delivery.application.infra.service.PostReviewsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PostReviewSummaryBusiness {

    @Autowired
    private PostReviewsService service;

    @Autowired
    private PostReviewDateSummaryService summaryService;

    @Autowired
    private PostReviewUserSummaryService userSummaryService;

    public void doDateSummary() {
        // 清零历史的待审核记录
        summaryService.resetDateSummary();

        List<PostReviewDateCountVO> reviews = service.getAllDefaultGroupByDate();
        if (ObjectUtils.isEmpty(reviews)) {
            log.warn("【按天统计后审记录】没有符合要求的数据。");
            return;
        }

        Map<LocalDate, List<PostReviewDateCountVO>> dateSummaryList = reviews.stream().collect(Collectors.groupingBy(PostReviewDateCountVO::getDate));
        log.info("【待后审统计】{}", dateSummaryList);

        dateSummaryList.forEach((date, dateCountVOList) -> dateCountVOList.forEach(c -> {
            PostReviewDateSummary dateSummary = summaryService.getOneByDateAndEmergency(date, c.getEmergencyType());
            if (ObjectUtil.isNotNull(dateSummary)) {
                dateSummary.setNum(c.getCount());
                dateSummary.setUpdatedAt(LocalDateTime.now());
                summaryService.updateById(dateSummary);
            } else {
                PostReviewDateSummary summary = new PostReviewDateSummary();
                summary.setEmergencyType(c.getEmergencyType());
                summary.setSummaryDate(date);
                summary.setNum(c.getCount());
                summary.setCreatedAt(LocalDateTime.now());
                summary.setUpdatedAt(LocalDateTime.now());
                summaryService.save(summary);
            }
        }));
    }

    public void doSummaryByUserEveryDay(LocalDateTime s, LocalDateTime e) {
        List<PostReviews> reviews = service.getAllReviewedRecords(s,e);
        if (ObjectUtils.isEmpty(reviews)) {
            log.warn("【用户后审记录统计】没有符合要求的数据。");
            return;
        }
        Map<String, List<PostReviews>> userStreamList = reviews.stream().collect(Collectors.groupingBy(PostReviews::getOperator));
        log.info("【用户后审记录统计】{}", userStreamList);

        userStreamList.forEach((username, reviewsList) -> {
            LocalDate reviewDate = LocalDate.now().minusDays(1);
            PostReviewUserSummary userSummary = userSummaryService.getOneByNameAndDate(username, reviewDate);
            if (ObjectUtil.isNull(userSummary)) {
                PostReviewUserSummary user = new PostReviewUserSummary();
                user.setUserName(username);
                user.setNum(reviewsList.size());
                user.setReviewDate(reviewDate);
                user.setCreatedAt(LocalDateTime.now());
                user.setUpdatedAt(LocalDateTime.now());
                userSummaryService.save(user);
            } else {
                userSummary.setNum(reviewsList.size());
                userSummary.setUpdatedAt(LocalDateTime.now());
                userSummaryService.updateById(userSummary);
            }
        });

    }


    public PostReviewWaitingReviewVO waitToReview() {
        PostReviewWaitingReviewVO vo = new PostReviewWaitingReviewVO();
        List<PostReviewDateSummary> records = summaryService.getListNotZeroNum();
        if (ObjectUtils.isEmpty(records)) {
            return vo;
        }

        // 按日期组合
        Map<LocalDate, List<PostReviewDateSummary>> dateListMap = records.stream().collect(Collectors.groupingBy(PostReviewDateSummary::getSummaryDate));

        AtomicReference<Integer> totalNum = new AtomicReference<>(0);
        AtomicReference<Integer> emergencyTotalNum = new AtomicReference<>(0);
        AtomicReference<Integer> firstReviewTotalNum = new AtomicReference<>(0);
        AtomicReference<Integer> normalReviewTotalNum = new AtomicReference<>(0);
        List<PostReviewWaitingReviewVO.DateSummary> summaryList = new ArrayList<>();
        dateListMap.forEach((k, v) -> {
            AtomicReference<Integer> dateTotal = new AtomicReference<>(0);
            PostReviewWaitingReviewVO.DateSummary dateSummary = new PostReviewWaitingReviewVO.DateSummary();
            dateSummary.setDate(k);

            // 组装同一天统计数据
            v.forEach(summary -> {
                switch (summary.getEmergencyType()) {
                    case 3:
                        emergencyTotalNum.updateAndGet(t -> t + summary.getNum());
                        dateSummary.setEmergencyNum(summary.getNum());
                        break;
                    case 2:
                        firstReviewTotalNum.updateAndGet(t -> t + summary.getNum());
                        dateSummary.setFirstReviewNum(summary.getNum());
                        break;
                    case 1:
                        normalReviewTotalNum.updateAndGet(t -> t + summary.getNum());
                        dateSummary.setNormalReviewNum(summary.getNum());
                        break;
                    default:
                        return;
                }
                totalNum.updateAndGet(t -> t + summary.getNum());
                dateTotal.updateAndGet(t -> t + summary.getNum());
            });

            if (dateTotal.get() != 0) {
                dateSummary.setTotal(dateTotal.get());
                summaryList.add(dateSummary);
            }
        });

        // 按日期倒序
        List<PostReviewWaitingReviewVO.DateSummary> dateSummaryList = summaryList.stream()
                .sorted(Comparator.comparing(PostReviewWaitingReviewVO.DateSummary::getDate).reversed())
                .collect(Collectors.toList());

        vo.setSummaryList(dateSummaryList);
        vo.setTotalAmount(totalNum.get());
        vo.setEmergencyTotalNum(emergencyTotalNum.get());
        vo.setFirstReviewTotalNum(firstReviewTotalNum.get());
        vo.setNormalReviewTotalNum(normalReviewTotalNum.get());
        return vo;
    }

    public PostReviewBeReviewVO userReviews(PostReviewBeReviewDTO dto) {
        // 检查搜索时间范围
        dto.checkDate();

        PostReviewBeReviewVO vo = new PostReviewBeReviewVO();
        List<PostReviewUserSummary> summaryList = userSummaryService.getListByDate(dto.getStartDate(), dto.getEndDate());
        if (ObjectUtils.isEmpty(summaryList)) {
            return vo;
        }

        Map<String, List<PostReviewUserSummary>> userMapList = summaryList.stream().collect(Collectors.groupingBy(PostReviewUserSummary::getUserName));

        AtomicReference<Integer> totalNum = new AtomicReference<>(0);
        List<PostReviewBeReviewVO.UserReviewVO> userList = new ArrayList<>();

        userMapList.forEach((k, v) -> {
            PostReviewBeReviewVO.UserReviewVO userReviewVO = new PostReviewBeReviewVO.UserReviewVO();
            AtomicReference<Integer> userTotal = new AtomicReference<>(0);

            userReviewVO.setUserName(k);
            v.forEach(userSummary -> {
                totalNum.updateAndGet(t -> t + userSummary.getNum());
                userTotal.updateAndGet(t -> t + userSummary.getNum());
            });
            userReviewVO.setReviewNum(userTotal.get());
            userList.add(userReviewVO);
        });
        vo.setUserList(userList);
        vo.setTotalNum(totalNum.get());

        return vo;
    }


}
