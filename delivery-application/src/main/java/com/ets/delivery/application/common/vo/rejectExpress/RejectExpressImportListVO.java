package com.ets.delivery.application.common.vo.rejectExpress;

import com.ets.delivery.application.common.consts.rejectExpress.RejectExpressMatchResultEnum;
import lombok.Data;

@Data
public class RejectExpressImportListVO {

    /**
     * 自增id
     */
    private Integer id;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 寄回快递单号
     */
    private String sendbackExpressNumber;

    /**
     * 入库快递单号
     */
    private String storageExpressNumber;

    /**
     * 分析结果等级
     */
    private Integer resultLevel;

    /**
     * 分析结果说明
     */
    private String resultMsg;

    /**
     * 匹配结果[0-默认 1-有结果 2-无结果]
     */
    private Integer matchResult;

    private String matchResultStr;

    public String getMatchResultStr() {
        return RejectExpressMatchResultEnum.map.getOrDefault(matchResult, "");
    }
}
