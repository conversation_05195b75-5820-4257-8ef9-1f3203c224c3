package com.ets.delivery.application.app.thirdservice.request.yundaOpen;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
public class YundaOpenExpressNotifyDTO {
    String appKey;
    String sign;
    String data;

    @Data
    public static class ResultData {
        String partner_id;
        @NotBlank(message = "产生时间不能为空")
        LocalDateTime time;
        String city = "";
        @NotBlank(message = "订单号不能为空")
        String orderid;
        @NotBlank(message = "物流公司运单号不能为空")
        String mailno;
        @NotBlank(message = "节点状态不能为空")
        String action;
        Integer station;
        String station_name;
        String phone;
        Integer station_type;
        Integer next_type;
        String next_name;
        String next_city;
        Integer next;
        @NotBlank(message = "轨迹描述信息不能为空")
        String description;
        String signer;
    }

    public ResultData getData() {
        return JSONObject.parseObject(data, ResultData.class);
    }
}