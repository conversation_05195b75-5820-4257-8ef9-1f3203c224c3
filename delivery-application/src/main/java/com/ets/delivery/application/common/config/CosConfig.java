package com.ets.delivery.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@ConfigurationProperties(
    prefix = "cos.tencent",
    ignoreInvalidFields = true
)
@Data
public class CosConfig {
    private String secretId;
    private String secretKey;
    private String bucket;
    private String region;
    private String baseUrl;
}
