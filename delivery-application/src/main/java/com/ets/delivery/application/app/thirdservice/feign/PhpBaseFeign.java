package com.ets.delivery.application.app.thirdservice.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(url = "${microUrls.gd-micro-base:http://gd-micro-base:80}", name = "PhpBaseFeign")
public interface PhpBaseFeign {

    @PostMapping(value = "/cos/get-image-url")
    String getUploadInfo(@RequestParam String type);

}
