package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.common.dto.logistics.*;
import com.ets.delivery.application.common.vo.logistics.LogisticsDetailVO;
import com.ets.delivery.application.common.vo.logistics.LogisticsListVO;
import com.ets.delivery.application.common.vo.logistics.LogisticsLogVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/admin/logisticsOrder")
public class LogisticsOrderController {

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @PostMapping("/getList")
    public JsonResult<IPage<LogisticsListVO>> getList(@RequestBody @Valid LogisticsListDTO listDTO) {
        return JsonResult.ok(logisticsBusiness.getList(listDTO));
    }

    @PostMapping("/getDetail")
    public JsonResult<LogisticsDetailVO> getDetail(@RequestBody @Valid LogisticsDetailDTO detailDTO) {
        return JsonResult.ok(logisticsBusiness.getDetail(detailDTO));
    }

    @PostMapping("/getLog")
    public JsonResult<IPage<LogisticsLogVO>> getLog(@RequestBody @Valid LogisticsLogDTO logDTO) {
        return JsonResult.ok(logisticsBusiness.getLog(logDTO));
    }

    @PostMapping("/cancel")
    public JsonResult<?> cancel(@RequestBody @Valid LogisticsCancelDTO cancelDTO) {
        logisticsBusiness.cancelLogistics(cancelDTO);
        return JsonResult.ok();
    }

    @PostMapping("/receive")
    public JsonResult<?> receive(@RequestBody @Valid LogisticsReceiveDTO receiveDTO) {
        logisticsBusiness.receiveLogistics(receiveDTO);
        return JsonResult.ok();
    }

    @PostMapping("/cancelReceive")
    public JsonResult<?> cancelReceive(@RequestBody @Valid LogisticsReceiveDTO receiveDTO) {
        logisticsBusiness.cancelReceiveLogistics(receiveDTO);
        return JsonResult.ok();
    }

    @PostMapping("/deliver")
    public JsonResult<?> deliver(@RequestBody @Valid LogisticsDeliverDTO deliverDTO) {
        logisticsBusiness.delivery(deliverDTO);
        return JsonResult.ok();
    }

    @PostMapping("/stopDeliver")
    public JsonResult<?> stopDeliver(@RequestBody @Valid LogisticsStopDeliverDTO stopDeliverDTO) {
        logisticsBusiness.stopDelivery(stopDeliverDTO);
        return JsonResult.ok();
    }

    @PostMapping("/resumeDeliver")
    public JsonResult<?> resumeDeliver(@RequestBody @Valid LogisticsResumeDeliverDTO resumeDeliverDTO) {
        logisticsBusiness.resumeDelivery(resumeDeliverDTO);
        return JsonResult.ok();
    }

    @PostMapping("/notify")
    public JsonResult<?> notifyResult(@RequestBody @Valid LogisticsNotifyDTO notifyDTO) {
        logisticsBusiness.notifyResult(notifyDTO);
        return JsonResult.ok();
    }

    @PostMapping("/menuList")
    public JsonResult<?> menuList() {
        return JsonResult.ok(logisticsBusiness.menuList());
    }
}
