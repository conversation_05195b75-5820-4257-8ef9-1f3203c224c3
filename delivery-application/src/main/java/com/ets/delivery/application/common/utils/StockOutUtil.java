package com.ets.delivery.application.common.utils;

import com.ets.delivery.application.common.consts.stock.StockOutStatusEnum;

import java.util.Arrays;
import java.util.List;

public class StockOutUtil {

    public static List<Integer> allowCancelStatus() {

        return Arrays.asList(
                StockOutStatusEnum.CREATED.getCode(),
                StockOutStatusEnum.NEW.getCode(),
                StockOutStatusEnum.ACCEPT.getCode(),
                StockOutStatusEnum.EXCEPTION.getCode(),
                StockOutStatusEnum.REJECT.getCode()
        );
    }

    public static List<Integer> notAllowEditStatus() {

        return Arrays.asList(
                StockOutStatusEnum.CANCELED.getCode()
        );
    }

}
