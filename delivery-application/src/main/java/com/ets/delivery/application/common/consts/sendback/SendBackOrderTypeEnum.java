package com.ets.delivery.application.common.consts.sendback;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.IntStream;
import java.util.LinkedHashMap;

@Getter
@AllArgsConstructor
public enum SendBackOrderTypeEnum {

    AFTER_SALES_RETURN("after_sales_return", "售后退货"),
    AFTER_SALES_CANCEL("after_sales_cancel", "售后注销"),
    AFTER_SALES_MAINTAIN_EXCHANGE("after_sales_maintain_exchange", "维修换货"),
    AFTER_SALES_MAINTAIN_EXCHANGE_APPLY("after_sales_maintain_exchange_apply", "维修换货-未激活"),
    AFTER_SALES_EXCHANGE("after_sales_exchange", "售后换货"),
    AFTER_SALES_EXCHANGE_APPLY("after_sales_exchange_apply", "售后换货-未激活"),
    AFTER_SALES_RECALL_EXCHANGE("after_sales_recall_exchange", "召回换货"),
    AFTER_SALES_REJECT("after_sales_reject", "售后拒收"),
    AFTER_SALES_RECOVERY("after_sales_recovery", "售后回收"),
    UPGRADE("upgrade", "设备升级"),
    GOODS("goods", "商品寄回");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        SendBackOrderTypeEnum[] enums = SendBackOrderTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
