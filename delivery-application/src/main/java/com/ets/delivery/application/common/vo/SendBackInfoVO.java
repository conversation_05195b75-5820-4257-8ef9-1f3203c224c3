package com.ets.delivery.application.common.vo;

import lombok.Data;

@Data
public class SendBackInfoVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 寄回件流水号
     */
    private String sendbackSn;

    /**
     * 订单流水号
     */
    private String orderSn;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 货品编号
     */
    private String goodsCode;

    /**
     * 寄回数量
     */
    private Integer nums;

    /**
     * 发件人
     */
    private String sendName;

    /**
     * 发件人联系手机
     */
    private String sendPhone;

    /**
     * 发件地区
     */
    private String sendArea;

    /**
     * 发件地址
     */
    private String sendAddress;

    /**
     * 接收人
     */
    private String reviceName;

    /**
     * 接收人联系手机
     */
    private String revicePhone;

    /**
     * 接收地区
     */
    private String reviceArea;

    /**
     * 接收地址
     */
    private String reviceAddress;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;
}
