package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.common.annotation.CosSignAnnotation;
import com.ets.delivery.application.app.business.stock.AdminStockOutBusiness;
import com.ets.delivery.application.common.dto.stock.StockOutCreateDTO;
import com.ets.delivery.application.common.dto.stock.StockOutEditDTO;
import com.ets.delivery.application.common.dto.stock.StockOutListDTO;
import com.ets.delivery.application.common.dto.stock.StockOutSnDTO;
import com.ets.delivery.application.common.vo.stock.StockOutDataVO;
import com.ets.delivery.application.common.vo.stock.StockOutDetailVO;
import com.ets.delivery.application.common.vo.stock.StockOutExportVO;
import com.ets.delivery.application.common.vo.stock.StockOutListVO;
import com.ets.delivery.application.infra.entity.StockOutOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/admin/stockOut")
public class StockOutController {

    @Autowired
    private AdminStockOutBusiness stockBusiness;

    @PostMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<StockOutListVO>> getList(@RequestBody @Valid StockOutListDTO dto) {

        return JsonResult.ok(stockBusiness.getList(dto));
    }

    @PostMapping("/create")
    @ResponseBody
    public JsonResult<StockOutOrder> create(@RequestBody @Valid StockOutCreateDTO dto) {

        return JsonResult.ok(stockBusiness.create(dto));
    }

    @PostMapping("/edit")
    @ResponseBody
    public JsonResult<Object> edit(@RequestBody @Valid StockOutEditDTO dto) {

        stockBusiness.edit(dto);

        return JsonResult.ok();
    }

    @PostMapping("/getData")
    @ResponseBody
    @CosSignAnnotation
    public JsonResult<StockOutDataVO> getData(@RequestBody @Valid StockOutSnDTO dto) {

        return JsonResult.ok(stockBusiness.getData(dto));
    }

    @PostMapping("/detail")
    @ResponseBody
    @CosSignAnnotation
    public JsonResult<StockOutDetailVO> detail(@RequestBody @Valid StockOutSnDTO dto) {

        return JsonResult.ok(stockBusiness.detail(dto));
    }

    @PostMapping("/cancel")
    @ResponseBody
    public JsonResult<Object> cancel(@RequestBody @Valid StockOutSnDTO dto) {

        stockBusiness.cancel(dto);

        return JsonResult.ok();
    }

    @RequestMapping("/export")
    public void export(@RequestBody @Valid StockOutListDTO dto, HttpServletResponse response) {

        stockBusiness.exportFile(dto, response);
    }

}
