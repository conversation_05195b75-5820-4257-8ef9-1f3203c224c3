package com.ets.delivery.application.app.thirdservice.request.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class DeliveryOrderQueryXmlDTO {

    //<startTime>仓库出库开始时间，yyyy-MM-dd HH:mm:ss</startTime>
    //<endTime>仓库出库结束时间，yyyy-MM-dd HH:mm:ss</endTime>
    //<ownerCode>货主编码, string (50) </ownerCode>
    //<warehouseCode>仓库编码, string (50)</warehouseCode>
    //<orderCode>ERP出库单号, string (50) ,详情查询时候必填</orderCode>
    //<orderSourceCode >交易单号</orderSourceCode >
    //<page>当前页，从 1 开始</page>
    //<pageSize>每页条数（最多 100 条）</pageSize>

    private String startTime;
    private String endTime;
    private String ownerCode;
    private String warehouseCode;
    private String orderCode;
    private String orderSourceCode;
    private Integer page = 1;
    private Integer pageSize = 100;
}
