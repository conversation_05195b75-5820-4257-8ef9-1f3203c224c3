package com.ets.delivery.application.app.business.storageMina;

import cn.hutool.crypto.SecureUtil;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.app.business.BaseBusiness;
import com.ets.delivery.application.common.consts.ErrCodeConstant;
import com.ets.delivery.application.common.consts.user.UserStatus;
import com.ets.delivery.application.common.utils.HeaderUtil;
import com.ets.delivery.application.common.vo.auth.AuthUserVO;
import com.ets.delivery.application.infra.entity.User;
import com.ets.delivery.application.infra.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Component
@Slf4j
public class AuthBusiness {

    @Autowired
    private BaseBusiness baseBusiness;

    @Autowired
    private UserService userService;

    public AuthUserVO loginByUserName(String userName, String password) {

        User user = userService.getByUserName(userName);
        if (user == null) {
            ToolsHelper.throwException("用户名或密码错误");
        }

        // 校验密码
        if (! BCrypt.checkpw(password, user.getPasswordHash())) {
            ToolsHelper.throwException("用户名或密码错误");
        }

        if (! user.getStatus().equals(UserStatus.ACTIVE.getCode())) {
            ToolsHelper.throwException("账号已禁用");
        }

        if (StringUtils.isEmpty(user.getToken()) || isExpired(user)) {
            // 生成token
            String token = baseBusiness.generateSn("issuerAdminMinaToken");
            token = SecureUtil.md5(token).toLowerCase(Locale.ROOT);

            userService.updateMinaToken(user.getId(), token);
            user.setToken(token);
        }

        AuthUserVO vo = new AuthUserVO();
        vo.setUid(user.getId());
        vo.setToken(user.getToken());

        return vo;
    }

    public Boolean isExpired(User user) {
        return user.getUpdatedAt() < ToolsHelper.getTime() - 7 * 86400;
    }

    public User getLoginUser() {

        Object user = ThreadLocalUtil.getData("adminLoginUser");
        if (user == null) {
            log.error("获取登录用户失败，需先调用checkAccess");
            ToolsHelper.throwException("请先调用checkAccess, 注解：@MinaCheckAccessAnnotation");
        }

        return (User) user;
    }

    public void checkAccess() {

        String token = HeaderUtil.getMinaToken();
        if (StringUtils.isEmpty(token)) {
            ToolsHelper.throwException("x-app-token不能为空", ErrCodeConstant.CODE_LOGIN_INVALID.getCode());
        }
        User user = userService.getByMinaToken(token);

        if (user == null) {
            ToolsHelper.throwException("无效的token", ErrCodeConstant.CODE_LOGIN_INVALID.getCode());
        }

        if (! user.getStatus().equals(UserStatus.ACTIVE.getCode())) {
            ToolsHelper.throwException("账号已禁用");
        }

        if (isExpired(user)) {
            ToolsHelper.throwException(ErrCodeConstant.CODE_LOGIN_INVALID.getDescription(), ErrCodeConstant.CODE_LOGIN_INVALID.getCode());
        }

        ThreadLocalUtil.setData("adminLoginUser", user);
    }

}
