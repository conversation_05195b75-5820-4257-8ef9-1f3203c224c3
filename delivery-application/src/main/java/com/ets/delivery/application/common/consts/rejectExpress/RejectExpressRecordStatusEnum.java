package com.ets.delivery.application.common.consts.rejectExpress;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.IntStream;
import java.util.LinkedHashMap;

@Getter
@AllArgsConstructor
public enum RejectExpressRecordStatusEnum {

    SUCCESS(1, "成功"),
    FAIL(2, "失败");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        RejectExpressRecordStatusEnum[] enums = RejectExpressRecordStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
