package com.ets.delivery.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "params.mao-chao")
public class MaoChaoConfig {

    private String accessKeyId;

    private String accessKeySecret;

    private Boolean verify = true;

    private HashMap<String, String> packageSnMap;

}
