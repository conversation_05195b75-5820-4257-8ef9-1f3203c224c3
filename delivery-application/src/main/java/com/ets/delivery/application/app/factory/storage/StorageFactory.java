package com.ets.delivery.application.app.factory.storage;

import cn.hutool.extra.spring.SpringUtil;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.storage.impl.JdCloudStorageManage;
import com.ets.delivery.application.app.factory.storage.impl.NormalStorageManage;
import com.ets.delivery.application.app.factory.storage.impl.StorageBase;
import com.ets.delivery.application.app.factory.storage.impl.YundaStorageManage;
import com.ets.delivery.application.common.consts.storage.StorageCodeConstant;

public class StorageFactory {

    public static StorageBase create(String storageCode) {
        StorageBase storageBase = null;

        switch (storageCode) {
            case StorageCodeConstant.JD_CLOUD:
                return SpringUtil.getBean(JdCloudStorageManage.class);
            case StorageCodeConstant.YUNDA:
                return SpringUtil.getBean(YundaStorageManage.class);
            case StorageCodeConstant.NORMAL:
                return SpringUtil.getBean(NormalStorageManage.class);
            default:
                ToolsHelper.throwException("暂不支持此物流公司");
        }
        return storageBase;
    }
}
