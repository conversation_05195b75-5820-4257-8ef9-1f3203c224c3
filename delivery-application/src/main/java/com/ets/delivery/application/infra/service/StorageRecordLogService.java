package com.ets.delivery.application.infra.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.bo.StorageRecordLogBO;
import com.ets.delivery.application.infra.entity.StorageRecordLog;
import com.ets.delivery.application.infra.mapper.StorageRecordLogMapper;
import org.springframework.stereotype.Service;

import jakarta.validation.Valid;
import java.time.LocalDateTime;

/**
 * <p>
 * 入库记录日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Service
@DS("db-issuer-admin")
public class StorageRecordLogService extends BaseService<StorageRecordLogMapper, StorageRecordLog> {

    public void addLog(@Valid StorageRecordLogBO logBO) {
        StorageRecordLog log = BeanUtil.copyProperties(logBO, StorageRecordLog.class);
        log.setCreatedAt(LocalDateTime.now());
        log.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(log);
    }

    public IPage<StorageRecordLog> getLogListById(Integer recordId, Integer pageNum, Integer pageSize) {
        Wrapper<StorageRecordLog> wrapper = Wrappers.<StorageRecordLog>lambdaQuery()
                .eq(StorageRecordLog::getRecordId, recordId)
                .orderByDesc(StorageRecordLog::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);
    }
}
