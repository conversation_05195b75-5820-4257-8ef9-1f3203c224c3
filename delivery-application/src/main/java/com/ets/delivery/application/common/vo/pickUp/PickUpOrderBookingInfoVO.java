package com.ets.delivery.application.common.vo.pickUp;

import lombok.Data;

import java.util.List;

@Data
public class PickUpOrderBookingInfoVO {
    List<PickUpOrderBookingInfoVO.BookingCalendar> bookingCalendar;

    @Data
    public static class BookingCalendar {
        String day;
        String pickUpStartTime;
        String pickUpDeadlineTime;
        String downGradeMark;
        List<PickUpOrderBookingInfoVO.BookingCalendar.TimeDetail> timeList;

        @Data
        public static class TimeDetail {
            String startTime;
            String endTime;
            String timeRange;
        }
    }
}
