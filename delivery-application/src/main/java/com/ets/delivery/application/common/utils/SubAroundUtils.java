package com.ets.delivery.application.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class SubAroundUtils {
    public static String around(String name, int index, int end) {
        if(StringUtils.isBlank(name)) {
            return "";
        }
        if(StringUtils.length(name) <= (index+end)) {
            return name;
        }
        return StringUtils.left(name, index).concat(StringUtils.removeStart(StringUtils.leftPad(StringUtils.right(name, end), StringUtils.length(name)-index, "*"), ""));
    }
}
