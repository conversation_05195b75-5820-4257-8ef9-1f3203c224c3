package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 销售出库单列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_ex_warehouse")
public class ExWarehouse extends BaseEntity<ExWarehouse> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 发货单物流单号
     */
    private String isvUuid;

    /**
     * 出库单号
     */
    private String deliverOrderNo;

    /**
     * 子单订单号
     */
    private String splitOrderNo;

    /**
     * 事业部编号
     */
    private String departmentNo;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 收货人手机
     */
    private String consigneeMobile;

    /**
     * 收货人省
     */
    private String consigneeProvince;

    /**
     * 收货人市
     */
    private String consigneeCity;

    /**
     * 收件人区
     */
    private String consigneeArea;

    /**
     * 收货人地址
     */
    private String consigneeAddress;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 订单状态
     */
    private Integer currentStatus;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 物流单号
     */
    private String wayBill;

    /**
     * 承运商编号
     */
    private String shipperNo;

    /**
     * 承运商名称
     */
    private String shipperName;

    /**
     * 是否拆单
     */
    private Integer splitFlag;

    /**
     * 仓储
     */
    private String storageCode;

    /**
     * 指定发货物流编码
     */
    private String logisticsCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
