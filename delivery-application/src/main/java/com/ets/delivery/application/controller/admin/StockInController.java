package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.common.annotation.CosSignAnnotation;
import com.ets.delivery.application.app.business.stock.AdminStockInBusiness;
import com.ets.delivery.application.common.dto.stock.*;
import com.ets.delivery.application.common.vo.stock.StockInDataVO;
import com.ets.delivery.application.common.vo.stock.StockInDetailVO;
import com.ets.delivery.application.common.vo.stock.StockInListVO;
import com.ets.delivery.application.infra.entity.StockInOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/admin/stockIn")
public class StockInController {

    @Autowired
    private AdminStockInBusiness stockBusiness;


    @PostMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<StockInListVO>> getList(@RequestBody @Valid StockInListDTO dto) {

        return JsonResult.ok(stockBusiness.getList(dto));
    }

    @PostMapping("/create")
    @ResponseBody
    public JsonResult<StockInOrder> create(@RequestBody @Valid StockInCreateDTO dto) {

        return JsonResult.ok(stockBusiness.create(dto));
    }

    @PostMapping("/edit")
    @ResponseBody
    public JsonResult<Object> edit(@RequestBody @Valid StockInEditDTO dto) {

        stockBusiness.edit(dto);

        return JsonResult.ok();
    }

    @PostMapping("/detail")
    @ResponseBody
    @CosSignAnnotation
    public JsonResult<StockInDetailVO> detail(@RequestBody @Valid StockInSnDTO dto) {

        return JsonResult.ok(stockBusiness.detail(dto));
    }

    @PostMapping("/getData")
    @ResponseBody
    @CosSignAnnotation
    public JsonResult<StockInDataVO> getData(@RequestBody @Valid StockInSnDTO dto) {

        return JsonResult.ok(stockBusiness.getData(dto));
    }

    @PostMapping("/cancel")
    @ResponseBody
    public JsonResult<Object> cancel(@RequestBody @Valid StockInSnDTO dto) {

        stockBusiness.cancel(dto);

        return JsonResult.ok();
    }

    @RequestMapping("/export")
    public void export(@RequestBody @Valid StockInListDTO dto, HttpServletResponse response) {

        stockBusiness.exportFile(dto, response);
    }

}
