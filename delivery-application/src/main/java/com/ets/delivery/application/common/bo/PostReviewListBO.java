package com.ets.delivery.application.common.bo;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;

@Data
public class PostReviewListBO {

    /**
     * 审核单号
     */
    private List<String> reviewSnList;

    /**
     * 后审类型
     */
    private Integer emergencyType;

    /**
     * 是否正序
     */
    private String sort = "asc";

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;

    /**
     * 搜索天数范围
     */
    private long daysRange = 90;
}
