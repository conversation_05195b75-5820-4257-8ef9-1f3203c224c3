package com.ets.delivery.application.common.consts.manualLogistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum ImportManualLogisticsUploadStatusEnum {

    DEFAULT(0, "默认"),
    SUCCESS(1, "上传成功"),
    FAIL(2, "上传失败");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        ImportManualLogisticsUploadStatusEnum[] enums = ImportManualLogisticsUploadStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
