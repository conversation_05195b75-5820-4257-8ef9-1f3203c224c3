package com.ets.delivery.application.infra.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;

/**
 * <p>
 * 入库记录表
 * </p>
 *
 * <AUTHOR> @since 2023-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_storage_record")
public class StorageRecord extends BaseEntity<StorageRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 入库件流水号
     */
    private String recordSn;

    /**
     * 仓储
     */
    private String storageCode;

    /**
     * 寄回数量
     */
    private Integer nums;

    /**
     * 接收人
     */
    private String reviceName;

    /**
     * 接收人联系手机
     */
    private String revicePhone;

    /**
     * 接收地区
     */
    private String reviceArea;

    /**
     * 接收地址
     */
    private String reviceAddress;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 操作人员
     */
    private String operator;

    /**
     * 状态：1-待确认 2-匹配成功 3-匹配失败
     */
    private Integer status;

    /**
     * 0-默认, 1-ETC卡+OBU, 2-ETC单卡, 3-ETC单OBU, 4-ETC设备破损, 5-非高灯设备或非设备, 6-单片式设备
     */
    private Integer goodsType;

    /**
     * 货物图片
     */
    private String goodsImages;

    /**
     * sku信息
     */
    private String skuInfo;

    /**
     * 关联对象集,包含类型及order_sn
     */
    private String referObject;

    /**
     * 接收备注
     */
    private String reviceRemark;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime reviceTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    public JSONObject getSkuInfoObj() {

        if (StringUtils.isEmpty(skuInfo)) {
            return new JSONObject();
        }

        return JSON.parseObject(skuInfo);
    }


}
