package com.ets.delivery.application.common.dto.stock;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class StockGoodsInfoDTO {

    /**
     * 物流公司的商品编号
     */
    @NotBlank(message = "请选择商品")
    private String storageSku;

    /**
     * 申请数量
     */
    @NotNull(message = "请填写申请数量")
    private Integer applyCount;

    /**
     * sku编号
     */
    @NotBlank(message = "sku编号不能为空")
    private String skuSn;

    @Valid
    private List<StockGoodsNumberDTO> numberInfoList;


}
