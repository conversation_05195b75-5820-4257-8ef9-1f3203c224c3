package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.SaleafterDeviceExchangeFeign;
import com.ets.delivery.application.app.thirdservice.request.PickUpCallBackRequest;
import org.springframework.cloud.openfeign.FallbackFactory;

import org.springframework.stereotype.Component;

@Component
public class DeviceExchangeFallbackFactory implements FallbackFactory<SaleafterDeviceExchangeFeign> {
    @Override
    public SaleafterDeviceExchangeFeign create(Throwable throwable) {
        return new SaleafterDeviceExchangeFeign() {
            @Override
            public JsonResult<?> pickUpCallBack(PickUpCallBackRequest request) {
                return JsonResult.error("请求售后接口pickUpCallBack失败");
            }
        };
    }
}
