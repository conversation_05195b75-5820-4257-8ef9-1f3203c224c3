package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.JdOpenApiFeign;
import org.springframework.cloud.openfeign.FallbackFactory;

import org.springframework.stereotype.Component;

@Component
public class JdOpenApiFallbackFactory  implements FallbackFactory<JdOpenApiFeign> {
    @Override
    public JdOpenApiFeign create(Throwable throwable) {
        return new JdOpenApiFeign() {
            @Override
            public String StandardCalendar(String jsonBody, String appKey, String accessToken, String timestamp, String v, String lopDn, String sign) {
                return JsonResult.error("请求京东取件单预约日历接口失败：" + throwable.getLocalizedMessage()).toString();
            }

            @Override
            public String CheckBlindArea(String jsonBody, String appKey, String accessToken, String timestamp, String v, String lopDn, String sign) {
                return JsonResult.error("请求京东超区校验接口失败：" + throwable.getLocalizedMessage()).toString();
            }

            @Override
            public String QueryEstimatedFreights(String jsonBody, String appKey, String accessToken, String timestamp, String v, String lopDn, String sign) {
                return JsonResult.error("请求京东查询预估运费和时效接口失败：" + throwable.getLocalizedMessage()).toString();
            }

            @Override
            public String ReceivePickUpOrder(String jsonBody, String appKey, String accessToken, String timestamp, String v, String lopDn, String sign) {
                return JsonResult.error("请求京东取件单下单接口失败：" + throwable.getLocalizedMessage()).toString();
            }

            @Override
            public String PickupOrderCancel(String jsonBody, String appKey, String accessToken, String timestamp, String v, String lopDn, String sign) {
                return JsonResult.error("请求京东取件单取消下单接口失败：" + throwable.getLocalizedMessage()).toString();
            }

            @Override
            public String QueryDynamicTraceInfo(String jsonBody, String appKey, String accessToken, String timestamp, String v, String lopDn, String sign) {
                return JsonResult.error("请求京东全程跟踪查询接口失败：" + throwable.getLocalizedMessage()).toString();
            }
        };
    }
}
