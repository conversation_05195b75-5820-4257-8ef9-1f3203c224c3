package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.PhpIssuerAdminFallbackFactory;
import com.ets.delivery.application.app.thirdservice.request.risk.ReviewRiskResultNotifyDTO;
import com.ets.delivery.application.app.thirdservice.request.risk.ReviewRiskReviewResultNotifyDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        url = "${microUrls.issuer-admin:http://etc-micro-issuer-admin:80}",
        name = "PhpIssuerAdminFeign",
        fallbackFactory = PhpIssuerAdminFallbackFactory.class
)
public interface PhpIssuerAdminFeign {

    @PostMapping("/reviews/risk-result-notify")
    String reviewRiskResultNotify(@RequestBody ReviewRiskResultNotifyDTO notifyDTO);

    @PostMapping("/reviews/risk-review-result-notify")
    String reviewRiskReviewResultNotify(@RequestBody ReviewRiskReviewResultNotifyDTO notifyDTO);
}
