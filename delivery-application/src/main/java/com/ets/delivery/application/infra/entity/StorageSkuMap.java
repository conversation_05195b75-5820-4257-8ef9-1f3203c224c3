package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 仓储映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("etc_storage_sku_map")
public class StorageSkuMap extends BaseEntity<StorageSkuMap> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商品代码
     */
    private String sku;

    /**
     * 仓库代号
     */
    private String storageCode;

    /**
     * 仓库对应sku的编码
     */
    private String storageSku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 指定区域（json格式)
     */
    private String assignArea;

    /**
     * 状态：1正常2关闭
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
