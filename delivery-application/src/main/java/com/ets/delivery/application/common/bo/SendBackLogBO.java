package com.ets.delivery.application.common.bo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class SendBackLogBO {

    @NotNull(message = "寄回件id不能为空")
    private Integer sendbackId;

    @NotBlank(message = "操作内容不能为空")
    private String operateContent;

    @NotBlank(message = "操作人不能为空")
    private String operator;

    @NotBlank(message = "操作类型不能为空")
    private String type;
}
