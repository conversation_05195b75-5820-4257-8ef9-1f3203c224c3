package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.PhpApplyFeign;
import com.ets.delivery.application.app.thirdservice.request.apply.ApplyOrderRiskNotifyDTO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class PhpApplyFallbackFactory implements FallbackFactory<PhpApplyFeign> {
    @Override
    public PhpApplyFeign create(Throwable cause) {
        return new PhpApplyFeign() {
            @Override
            public String orderRiskNotify(ApplyOrderRiskNotifyDTO notifyDTO) {
                return JsonResult.error("请求phpApply服务异常：" + cause.getMessage()).toString();
            }
        };
    }
}
