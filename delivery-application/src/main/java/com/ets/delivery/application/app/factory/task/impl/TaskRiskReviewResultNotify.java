package com.ets.delivery.application.app.factory.task.impl;

import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.thirdservice.feign.PhpApplyFeign;
import com.ets.delivery.application.app.thirdservice.feign.PhpIssuerAdminFeign;
import com.ets.delivery.application.app.thirdservice.request.risk.ReviewRiskReviewResultNotifyDTO;
import com.ets.delivery.application.app.thirdservice.request.apply.ApplyOrderRiskNotifyDTO;
import com.ets.delivery.application.common.consts.reviews.ReviewsRiskStatusEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import com.ets.delivery.application.common.consts.setting.SettingKeyEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.delivery.application.infra.entity.Reviews;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviews;
import com.ets.delivery.application.infra.service.ReviewsService;
import com.ets.delivery.application.infra.service.RiskReviewsService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
public class TaskRiskReviewResultNotify extends TaskBase {

    @Autowired
    private RiskReviewsService riskReviewsService;

    @Autowired
    private ReviewsService reviewsService;

    @Autowired
    private PhpApplyFeign phpApplyFeign;

    @Autowired
    private PhpIssuerAdminFeign phpIssuerAdminFeign;

    @Override
    public void childExec(TaskRecord taskRecord) {

        // 获取风控审核单记录
        RiskReviews riskReviews = riskReviewsService.getByRiskReviewSn(taskRecord.getReferSn());
        if (ObjectUtils.isEmpty(riskReviews)) {
            ToolsHelper.throwException("风控审核单不存在", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        if (riskReviews.getRiskReviewStatus().equals(RiskReviewStatusEnum.CANCELED.getValue())) {
            ToolsHelper.throwException("风控审核单已取消", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        if (riskReviews.getRiskReviewStatus().equals(RiskReviewStatusEnum.PENDING.getValue())) {
            ToolsHelper.throwException("风控审核单状态异常", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        // 补传资料 直接通知申办
        if (riskReviews.getRiskReviewStatus().equals(RiskReviewStatusEnum.SUPPLEMENT_MATERIAL.getValue())) {
            ApplyOrderRiskNotifyDTO notifyDTO = new ApplyOrderRiskNotifyDTO();
            notifyDTO.setOrderSn(riskReviews.getBusinessSn());
            notifyDTO.setRiskType(riskReviews.getRiskType());
            notifyDTO.setRiskReviewStatus(riskReviews.getRiskReviewStatus());
            notifyDTO.setMsg(riskReviews.getRejectReason());
            String result = phpApplyFeign.orderRiskNotify(notifyDTO);
            JsonResult<Object> jsonResult = JsonResult.convertFromJsonStr(result, Object.class);
            jsonResult.checkError();
            return;
        }

        // 审核通过、驳回 通知审核单
        // 获取审核单记录
        Reviews reviews = reviewsService.getOneByOrderSnAndRiskStatus(riskReviews.getBusinessSn(), ReviewsRiskStatusEnum.IN_PROGRESS.getValue());
        if (ObjectUtils.isEmpty(reviews)) {
            ToolsHelper.throwException("审核单不存在", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        if (Arrays.asList(
                RiskReviewStatusEnum.APPROVED.getValue(),
                RiskReviewStatusEnum.REJECTED_CANCEL.getValue(),
                RiskReviewStatusEnum.REJECTED_REUPLOAD.getValue()
        ).contains(riskReviews.getRiskReviewStatus())) {
            ReviewRiskReviewResultNotifyDTO notifyDTO = new ReviewRiskReviewResultNotifyDTO();
            notifyDTO.setReviewSn(reviews.getReviewSn());
            notifyDTO.setRiskReviewStatus(riskReviews.getRiskReviewStatus());
            notifyDTO.setRejectCode(SettingKeyEnum.RISK_REVIEW.getValue() + "_" + riskReviews.getRejectReasonId());
            notifyDTO.setMsg(riskReviews.getRejectReason());
            notifyDTO.setOperator(riskReviews.getOperator());
            String result = phpIssuerAdminFeign.reviewRiskReviewResultNotify(notifyDTO);
            JsonResult<Object> jsonResult = JsonResult.convertFromJsonStr(result, Object.class);
            jsonResult.checkError();
        }
    }
}
