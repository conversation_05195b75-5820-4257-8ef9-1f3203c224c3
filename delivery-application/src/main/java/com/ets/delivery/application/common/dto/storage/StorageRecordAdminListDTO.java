package com.ets.delivery.application.common.dto.storage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;

@Data
public class StorageRecordAdminListDTO {

    /**
     * 仓储
     */
    private String storageCode;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 状态：1-待确认 2-匹配成功 3-匹配失败
     */
    private Integer status;

    /**
     * 签收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate receiveStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate receiveEndTime;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
