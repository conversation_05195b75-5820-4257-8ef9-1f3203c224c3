package com.ets.delivery.application.common.dto.logistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;

@Data
public class LogisticsListDTO {

    private Integer type = 1;

    private Integer issuerId;
    private String orderSn;
    private String logisticsSn;
    private String plateNo;
    private String orderSource;
    private String orderType;
    private String expressNumber;
    private Integer deliveryStatus;
    private Integer notifyStatus;
    private String storageCode;
    private Integer deviceType;

    private String sendPhone;
    private String goodsUnionCode;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createEndTime;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate deliveryStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate deliveryEndTime;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
