package com.ets.delivery.application.common.vo.alarm;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@HeadRowHeight(20)
@ContentRowHeight(15)
@ColumnWidth(25)
public class LogisticsAlarmExcelVO {

    @ExcelProperty(value = "我司商品编码")
    private String goodsUnionCode;

    @ExcelProperty(value = "韵达仓商品编码")
    private String goodsSku;

    @ExcelProperty(value = "商品名称")
    private String goodsName;

    @ExcelProperty(value = "昨天发货量")
    private Integer yesterdayLogisticsNum;

    @ExcelProperty(value = "近3天平均发货量")
    private Integer threeDayAvgLogisticsNum;

    @ExcelProperty(value = "近7天平均发货量")
    private Integer sevenDayAvgLogisticsNum;

    @ExcelProperty(value = "涨跌幅度（%）")
    private BigDecimal floatPercent;
}
