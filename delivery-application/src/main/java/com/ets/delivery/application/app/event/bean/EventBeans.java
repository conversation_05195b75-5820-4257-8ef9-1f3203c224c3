package com.ets.delivery.application.app.event.bean;

import com.ets.delivery.application.app.event.ExpressReceivedEvent;
import com.ets.delivery.application.app.event.ExpressNumberWrongEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class EventBeans {

    public final static String EXPRESS_RECEIVED_EVENT_BEAN = "expressReceivedEventBean";
    public final static String EXPRESS_NUMBER_WRONG_EVENT_BEAN = "expressNumberWrongEventBean";

    @Bean(value = EXPRESS_RECEIVED_EVENT_BEAN)
    public ExpressReceivedEvent expressReceivedEvent() {
        return new ExpressReceivedEvent("");
    }

    @Bean(value = EXPRESS_NUMBER_WRONG_EVENT_BEAN)
    public ExpressNumberWrongEvent expressNumberWrongEvent() {return new ExpressNumberWrongEvent("");}
}
