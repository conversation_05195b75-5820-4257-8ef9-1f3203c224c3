package com.ets.delivery.application.common.consts.pickUp;

import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
public enum PickUpOrderStatusEnum {

    STATUS_CANCEL(-1, "取消"),
    STATUS_NORMAL(1, "正常");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    public static final List<Integer> list;

    PickUpOrderStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        PickUpOrderStatusEnum[] enums = PickUpOrderStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Arrays.stream(enums).map(PickUpOrderStatusEnum::getValue).collect(Collectors.toList());
    }
}
