package com.ets.delivery.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "sms-config")
public class SmsConfig {

    private Integer smsSwitch;

    private Map<String, String> smsTemplate;
}
