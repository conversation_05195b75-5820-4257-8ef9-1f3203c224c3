package com.ets.delivery.application.common.vo.logisticsSendBack;

import com.ets.delivery.application.common.consts.sendback.SendBackOrderSourceEnum;
import com.ets.delivery.application.common.consts.sendback.SendBackOrderTypeEnum;
import com.ets.delivery.application.common.consts.sendback.SendBackReceiveStatusEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Arrays;

@Data
public class SendBackDetailVO {

    private SendInfo sendInfo = new SendInfo();
    private GoodsInfo goodsInfo = new GoodsInfo();
    private ReceiveInfo receiveInfo = new ReceiveInfo();
    private Boolean showCheckButton = false;
    private Boolean showDealButton = false;

    @Data
    public static class SendInfo {
        private String storageCode;
        private String storageCodeStr;
        private String expressNumber;

        public String getStorageCodeStr() {
            return StorageCodeEnum.map.getOrDefault(storageCode, "--");
        }
    }

    @Data
    public static class GoodsInfo {
        private Integer issuerId;
        private String issuerIdStr;
        private String goodsCode;
        private String goodsName;
        private String orderType;
        private String orderTypeStr;
        private String orderSource;
        private String orderSourceStr;
        private String plateNo;

        public String getOrderTypeStr() {
            return SendBackOrderTypeEnum.map.getOrDefault(orderType, "--");
        }

        public String getOrderSourceStr() {
            return SendBackOrderSourceEnum.map.getOrDefault(orderSource, "--");
        }
    }

    @Data
    public static class ReceiveInfo {
        private String orderSn;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime createdAt;
        private Integer reviceStatus;
        private String reviceStatusStr;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime receiveTime;
        private String receiveRemark;
        private String notifyRemark;

        public String getReviceStatusStr() {
            return SendBackReceiveStatusEnum.map.getOrDefault(reviceStatus, "--");
        }
    }

    public Boolean getShowCheckButton() {
        return Arrays.asList(SendBackReceiveStatusEnum.RECEIVE_STATUS_WAIT.getValue(),
                SendBackReceiveStatusEnum.RECEIVE_STATUS_OVERTIME.getValue()).contains(receiveInfo.getReviceStatus());
    }

    public Boolean getShowDealButton() {
        return receiveInfo.getReviceStatus().equals(SendBackReceiveStatusEnum.RECEIVE_STATUS_ENTER.getValue());
    }
}
