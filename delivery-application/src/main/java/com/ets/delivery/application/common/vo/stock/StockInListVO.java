package com.ets.delivery.application.common.vo.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class StockInListVO {

    /**
     * 入库单号
     */
    private String stockInSn;

    /**
     * 实际入库时间
     */
    private String inTime;

    /**
     * 申请入库时间
     */
    private String applyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /*  以下字段需处理********************************/

    /**
     * 库存编号
     */
    private String storageName;

    /**
     * 入库类型
     */
    private String typeStr;

    /**
     * 商品属性
     */
    private String goodsQualityStr;

    /**
     * 入库状态
     */
    private String statusStr;

    private Integer status;

    /**
     * 是否允许取消
     */
    private boolean allowCancel;

    private boolean allowEdit;

    /**
     * 商品信息
     */
    private String goodsNameInfo = "";

    private String goodsApplyCount = "";

    private String goodsRealCount = "";

}
