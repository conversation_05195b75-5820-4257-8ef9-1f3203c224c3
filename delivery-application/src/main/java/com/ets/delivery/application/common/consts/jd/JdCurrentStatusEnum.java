package com.ets.delivery.application.common.consts.jd;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.IntStream;
import java.util.LinkedHashMap;

@Getter
@AllArgsConstructor
public enum JdCurrentStatusEnum {

    STATUS_10010(10010, "订单初始化"),
    STATUS_10014(10014, "已下发库房"),
    STATUS_10015(10015, "任务已分配"),
    STATUS_10016(10016, "拣货下架"),
    STATUS_10017(10017, "复核"),
    STATUS_10018(10018, "货品已打包"),
    STATUS_10019(10019, "交接发货"),
    STATUS_10020(10020, "包裹出库"),
    STATUS_10022(10022, "暂停"),
    STATUS_10027(10027, "取消中"),
    STATUS_10028(10028, "取消成功"),
    STATUS_10032(10032, "分拣验收"),
    STATUS_10033(10033, "站点验收"),
    STATUS_10034(10034, "妥投"),
    STATUS_10035(10035, "拒收"),
    STATUS_10037(10037, "逆向发货"),
    STATUS_10038(10038, "逆向完成"),
    STATUS_10054(10054, "分拣中心发货");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        JdCurrentStatusEnum[] enums = JdCurrentStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
