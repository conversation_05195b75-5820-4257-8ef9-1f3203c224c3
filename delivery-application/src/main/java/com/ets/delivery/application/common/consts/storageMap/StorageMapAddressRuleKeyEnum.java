package com.ets.delivery.application.common.consts.storageMap;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum StorageMapAddressRuleKeyEnum {

    RULE_KEY_PROVINCE("province", "省"),
    RULE_KEY_CITY("city", "市"),
    RULE_KEY_AREA("area", "区");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        map = Arrays.stream(StorageMapAddressRuleKeyEnum.values()).collect(Collectors.toMap(StorageMapAddressRuleKeyEnum::getValue, StorageMapAddressRuleKeyEnum::getDesc));
    }
}
