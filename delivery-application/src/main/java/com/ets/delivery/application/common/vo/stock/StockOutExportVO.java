package com.ets.delivery.application.common.vo.stock;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

@Data
@HeadRowHeight(20)
@ContentRowHeight(15)
@ColumnWidth(25)
public class StockOutExportVO {

    @ExcelProperty("出库单号")
    private String stockOutSn;

    /**
     * 实际出库时间
     */
    @ExcelProperty("实际出库时间")
    private String outTime;

    /**
     * 申请出库时间
     */
    @ExcelProperty("申请出库时间")
    private String applyTime;

    /**
     * 仓库
     */
    @ExcelProperty("仓库")
    private String storageName;

    /**
     * 出库类型
     */
    @ExcelProperty("出库类型")
    private String typeStr;

    /**
     * 商品属性
     */
    @ExcelProperty("商品属性")
    private String goodsQualityStr;

    /**
     * 出库状态
     */
    @ExcelProperty("出库状态")
    private String statusStr;

    @ExcelIgnore
    private Integer status;

    /**
     * 商品信息
     */
    @ExcelProperty("商品信息")
    private String goodsNameInfo = "";

    @ExcelProperty("申请出库数量")
    private String goodsApplyCount = "";

    @ExcelProperty("实际出库数量")
    private String goodsRealCount = "";

    /**
     * 操作人
     */
    @ExcelProperty("操作人")
    private String operator;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    @ExcelIgnore
    private Boolean allowCancel;

    @ExcelIgnore
    private Boolean allowEdit;

}
