package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.storage.StorageFactory;
import com.ets.delivery.application.common.consts.StockAlarmConstant;
import com.ets.delivery.application.common.dto.alarm.LogisticsAlarmDTO;
import com.ets.delivery.application.common.dto.alarm.StockAlarmDTO;
import com.ets.delivery.application.common.vo.InventoryQueryVO;
import com.ets.delivery.application.common.vo.alarm.LogisticsAlarmVO;
import com.ets.delivery.application.common.vo.alarm.StockAlarmVO;
import com.ets.delivery.application.infra.entity.StorageDateStock;
import com.ets.delivery.application.infra.entity.SupplyGoods;
import com.ets.delivery.application.infra.service.StorageDateStockService;
import com.ets.delivery.application.infra.service.SupplyGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class AlarmBusiness {

    @Autowired
    private SupplyGoodsService supplyGoodsService;

    @Autowired
    private StorageDateStockService storageDateStockService;

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @Resource(name = "defaultRedisTemplate")
    StringRedisTemplate redisTemplate;

    public StockAlarmVO stockAlarm(StockAlarmDTO stockAlarmDTO) {
        StockAlarmVO stockAlarmVO = new StockAlarmVO();
        stockAlarmVO.setAlarmCycle(stockAlarmDTO.getAlarmCycle());
        stockAlarmVO.setSafetyCycle(stockAlarmDTO.getSafetyCycle());
        try {
            // 获取商品名称
            SupplyGoods goods = supplyGoodsService.getOneByGoodsCode(stockAlarmDTO.getGoodsCode());
            if (ObjectUtil.isNull(goods)) {
                ToolsHelper.throwException("【库存预警】商品编号不存在：" + stockAlarmDTO.getGoodsCode());
            }
            stockAlarmVO.setGoodsName(goods.getGoodsName());

            // 获取平均发货量
            Double avgLogisticsCount = getLogisticsAverage(stockAlarmDTO.getGoodsCode(), stockAlarmDTO.getBeforeDay(), stockAlarmDTO.getAvgDay());
            // 计算预警值
            stockAlarmVO.setAlarmStockValue((int)Math.ceil(((stockAlarmDTO.getAlarmCycle() + stockAlarmDTO.getSafetyCycle()) * avgLogisticsCount)));

            // 获取商品库存
            Integer goodsStock = getGoodsStock(stockAlarmDTO.getStorageCode(), stockAlarmDTO.getGoodsCode());
            stockAlarmVO.setStock(goodsStock);
        } catch (Throwable e) {
            log.error("【库存预警】{}", e.getLocalizedMessage());
        }

        return stockAlarmVO;
    }

    public LogisticsAlarmVO logisticsAlarm(LogisticsAlarmDTO logisticsAlarmDTO) {
        LogisticsAlarmVO logisticsAlarmVO = new LogisticsAlarmVO();
        logisticsAlarmVO.setAvgDay(logisticsAlarmDTO.getAvgDay());
        try {
            // 获取商品名称
            SupplyGoods goods = supplyGoodsService.getOneByGoodsCode(logisticsAlarmDTO.getGoodsCode());
            if (ObjectUtil.isNull(goods)) {
                ToolsHelper.throwException("【库存预警】商品编号不存在：" + logisticsAlarmDTO.getGoodsCode());
            }
            logisticsAlarmVO.setGoodsName(goods.getGoodsName());

            // 获取昨日发货量
            Double yesterdayCount = getLogisticsAverage(logisticsAlarmDTO.getGoodsCode(), 0, 1);
            logisticsAlarmVO.setYesterdayCount(yesterdayCount);

            // 获取平均发货量
            Double avgLogisticsCount = getLogisticsAverage(
                    logisticsAlarmDTO.getGoodsCode(),
                    logisticsAlarmDTO.getBeforeDay(),
                    logisticsAlarmDTO.getAvgDay()
            );
            logisticsAlarmVO.setAvgCount(avgLogisticsCount);

            // 计算百分比
            if (avgLogisticsCount > 0) {
                double percent = (yesterdayCount - avgLogisticsCount) / avgLogisticsCount;
                String floatType = "持平";
                if (percent > 0) {
                    floatType = "上涨";
                } else if (percent < 0) {
                    floatType = "下跌";
                }

                logisticsAlarmVO.setFloatPercent((new BigDecimal(percent * 100)).setScale(2, RoundingMode.HALF_UP).abs());
                logisticsAlarmVO.setFloatTypeStr(floatType);
            }

        } catch (Throwable e) {
            log.error("【发货量异常预警】{}", e.getLocalizedMessage());
        }

        return logisticsAlarmVO;
    }

    public void setLogisticsAverage(String goodsCode, Integer beforeDay, Integer avgDay, Double quantity) {
        String key = StockAlarmConstant.getLogisticsAverageCacheKey(goodsCode, beforeDay, avgDay);
        redisTemplate.opsForValue().set(key, quantity.toString(), StockAlarmConstant.STOCK_ALARM_CACHE_TIME, TimeUnit.HOURS);
    }

    public Double getLogisticsAverage(String goodsCode, Integer beforeDay, Integer avgDay) {
        String key = StockAlarmConstant.getLogisticsAverageCacheKey(goodsCode, beforeDay, avgDay);
        String value = redisTemplate.opsForValue().get(key);
        Double quantity;
        // 缓存不存在 读取数据库
        if (StringUtils.isEmpty(value)) {
            quantity = logisticsBusiness.getLogisticsAverage(goodsCode, avgDay, beforeDay);
        } else {
            quantity = Double.parseDouble(value);
        }
        return quantity;
    }

    public void setGoodsStock(String storageCode, String goodsCode, Integer stock) {
        String key = StockAlarmConstant.getGoodsStockCacheKey(storageCode, goodsCode);
        redisTemplate.opsForValue().set(key, stock.toString(), StockAlarmConstant.STOCK_ALARM_CACHE_TIME, TimeUnit.HOURS);
    }

    public Integer getGoodsStock(String storageCode, String goodsCode) {
        String key = StockAlarmConstant.getGoodsStockCacheKey(storageCode, goodsCode);
        String value = redisTemplate.opsForValue().get(key);
        AtomicInteger stock = new AtomicInteger();
        // 缓存不存在 请求获取库存
        if (StringUtils.isEmpty(value)) {
            try {
                List<String> goodsCodeList = Collections.singletonList(goodsCode);
                List<InventoryQueryVO> inventoryQueryList = StorageFactory.create(storageCode).inventoryQuery(goodsCodeList);

                if (ObjectUtil.isNull(inventoryQueryList)) {
                    ToolsHelper.throwException("请求结果为null");
                }
                inventoryQueryList.forEach(query -> stock.set(query.getStockNum()));
            } catch (Throwable e) {
                log.error("请求商品库存结果错误：{}", e.getLocalizedMessage());
                stock.set(0);
            }
        } else {
            stock.set(Integer.parseInt(value));
        }

        return stock.get();
    }

    public void setGoodsDailyStock(String storageCode, LocalDate stockDate, List<InventoryQueryVO> inventoryQueryList) {

        inventoryQueryList.forEach(query -> {
            // 查询是否存在
            LambdaQueryWrapper<StorageDateStock> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StorageDateStock::getStorageCode, storageCode)
                    .eq(StringUtils.isNotEmpty(query.getWarehouseNo()), StorageDateStock::getWarehouseNo, query.getWarehouseNo())
                    .eq(StorageDateStock::getStockDate, stockDate)
                    .eq(StorageDateStock::getGoodsCode, query.getGoodsCode());
            StorageDateStock dateStock = storageDateStockService.getOneByWrapper(wrapper);
            if (ObjectUtil.isNotNull(dateStock)) {
                return;
            }

            StorageDateStock storageDateStock = BeanUtil.copyProperties(query, StorageDateStock.class);
            storageDateStock.setStorageCode(storageCode);
            storageDateStock.setStockDate(stockDate);
            storageDateStockService.create(storageDateStock);
        });
    }
}
