package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 自动审核后审记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_post_reviews")
public class PostReviews extends BaseEntity<PostReviews> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审核单号
     */
    private String reviewSn;

    /**
     * 订单流水号
     */
    private String orderSn;

    /**
     * 对接方id
     */
    private Integer issuerId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 后审人
     */
    private String operator;

    /**
     * 后审状态[0-待审核 1-审核中 2-审核通过 3-审核异常]
     */
    private Integer reviewStatus;

    /**
     * 后审类型:默认0-无需后审 1-正常后审 2-优先后审 3-紧急后审
     */
    private Integer emergencyType;

    /**
     * 异常处理状态[0-无需处理 1-未处理 2-已处理]
     */
    private Integer exceptionStatus;

    /**
     * 异常处理备注
     */
    private String remark;

    /**
     * 领取时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime drawTime;

    /**
     * 后审时间
     */
    private LocalDateTime reviewTime;

    /**
     * 异常处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
