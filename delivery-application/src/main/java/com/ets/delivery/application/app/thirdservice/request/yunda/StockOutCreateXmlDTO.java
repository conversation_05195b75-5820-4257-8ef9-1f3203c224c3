package com.ets.delivery.application.app.thirdservice.request.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.*;
import java.util.List;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class StockOutCreateXmlDTO {

    @XmlElement(name = "deliveryOrder")
    private DeliveryOrder deliveryOrder;

    @XmlElementWrapper(name = "orderLines")
    @XmlElement(name = "orderLine")
    private List<OrderLine> orderLines;

    @Data
    public static class DeliveryOrder {
        String deliveryOrderCode;
        String warehouseCode;
        String orderType;
        String createTime;
        String transportMode;
        String logisticsCode;

        ReceiverInfo receiverInfo;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class ReceiverInfo {
        String name;
        String mobile;
        String province;
        String city;
        String area;
        String detailAddress;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class OrderLine {
        String ownerCode;
        String itemCode;
        Integer planQty;
        String inventoryType;
    }

}
