package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 仓库库存记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_storage_date_stock")
public class StorageDateStock extends BaseEntity<StorageDateStock> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 仓储编码
     */
    private String storageCode;

    /**
     * 库房编码
     */
    private String warehouseNo;

    /**
     * 库房名称
     */
    private String warehouseName;

    /**
     * 仓库商品编码
     */
    private String goodsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 库存日期
     */
    private LocalDate stockDate;

    /**
     * 库存
     */
    private Integer stockNum;

    /**
     * 可用库存
     */
    private Integer stockUsableNum;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
