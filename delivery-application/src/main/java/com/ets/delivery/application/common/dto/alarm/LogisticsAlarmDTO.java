package com.ets.delivery.application.common.dto.alarm;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
public class LogisticsAlarmDTO {
    /**
     * 仓储
     */
    @NotEmpty(message = "仓储编码不能为空")
    private String storageCode;

    /**
     * 商品编码
     */
    @NotEmpty(message = "商品编码不能为空")
    private String goodsCode;

    /**
     * 平均天数
     */
    @NotNull(message = "平均天数不能为空")
    @Min(value = 1, message = "平均天数不能小于1")
    private Integer avgDay;

    /**
     * 提前天数
     */
    @NotNull(message = "提前天数不能为空")
    @Min(value = 0, message = "提前天数不能小于0")
    private Integer beforeDay = 0;
}
