package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.common.bo.notify.LogisticsShipNotifyBO;
import com.ets.delivery.application.common.bo.notify.SendBackNotifyBO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.net.URI;
import java.util.HashMap;

/**
 * 通知类
 */
@FeignClient(name = "NotifyFeign", url = "uri")
public interface NotifyFeign {

    @PostMapping
    String sendBackNotify(URI uri, @RequestBody SendBackNotifyBO bo, @RequestHeader HashMap<String, String> headers);

    @PostMapping
    String logisticsShipNotify(URI uri, @RequestBody LogisticsShipNotifyBO bo, @RequestHeader HashMap<String, String> headers);
}
