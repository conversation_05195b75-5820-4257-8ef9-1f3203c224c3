package com.ets.delivery.application.infra.entity.aftersalesreview;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.delivery.application.infra.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 审核单日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@TableName("etc_aftersales_reviews_log")
public class AftersalesReviewsLog extends BaseEntity<AftersalesReviewsLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审核单号
     */
    private String reviewSn;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
