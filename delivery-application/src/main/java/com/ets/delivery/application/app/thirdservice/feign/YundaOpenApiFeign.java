package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.YundaOpenApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(url = "${yunda-open-api.apiUrl}", name = "YundaOpenApiFeign", fallbackFactory = YundaOpenApiFallbackFactory.class)
public interface YundaOpenApiFeign {

    @PostMapping(
            value = "/openapi/outer/logictis/query",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            headers = {"app-key=${yunda-open-api.appKey}"}
    )
    String logisticsQuery(
            @RequestHeader("sign") String sign,
            @RequestHeader("req-time") String reqTime,
            @RequestBody String json
    );

    @PostMapping(
            value = "/openapi/outer/logictis/subscribe",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            headers = {"app-key=${yunda-open-api.appKey}"}
    )
    String subscribe(
            @RequestHeader("sign") String sign,
            @RequestHeader("req-time") String reqTime,
            @RequestBody String json
    );
}
