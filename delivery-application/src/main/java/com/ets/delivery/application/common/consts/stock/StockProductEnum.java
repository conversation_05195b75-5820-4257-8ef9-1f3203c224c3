package com.ets.delivery.application.common.consts.stock;

import com.ets.delivery.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum StockProductEnum {

    OBU(1, "OBU"),

    DEBT_CARD(2, "记账卡"),

    RECHARGE_CARD(3, "储值卡");

    private final Integer code;
    private final String description;

    public static String getDescByCode(int code) {

        for (StockProductEnum node : StockProductEnum.values()) {

            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static StockProductEnum getByCode(int code) {

        for (StockProductEnum node : StockProductEnum.values()) {

            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        List<SelectOptionsVO> selectOptionsVOList = new ArrayList<>();
        for (StockProductEnum node : StockProductEnum.values()) {
            selectOptionsVOList.add(new SelectOptionsVO(node.getCode().toString(),node.getDescription()));
        }

        return selectOptionsVOList;
    }

}
