package com.ets.delivery.application.common.config.queue.task;

import com.ets.common.BizException;
import com.ets.common.queue.BaseDisposer;
import com.ets.common.queue.JobDto;
import com.ets.common.queue.MicroBaseQueue;
import com.ets.starter.interceptor.ApplicationContextHelper;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueueTask extends MicroBaseQueue {
    @Autowired
    QueueTaskConfig queueTaskConfig;

    @Override
    public void push(BaseDisposer job) throws BizException {

        DefaultMQProducer defaultMQProducer = (DefaultMQProducer) ApplicationContextHelper.getBean(QueueTaskConfig.PRODUCER_BEAN_NAME);
        if (defaultMQProducer == null) {
            return;
        }

        JobDto jobDto = getJobDto(job);
        super.pushRocketMqJob(defaultMQProducer, queueTaskConfig.getTopic(), queueTaskConfig.getQueueName(), jobDto);
    }

    public void push(BaseDisposer job, int delayLevel) {
        DefaultMQProducer defaultMQProducer = (DefaultMQProducer)ApplicationContextHelper.getBean(QueueTaskConfig.PRODUCER_BEAN_NAME);
        if (defaultMQProducer != null) {
            JobDto jobDto = this.getJobDto(job);
            super.pushRocketMqJob(defaultMQProducer, queueTaskConfig.getTopic(), queueTaskConfig.getQueueName(), jobDto, delayLevel);
        }
    }
}
