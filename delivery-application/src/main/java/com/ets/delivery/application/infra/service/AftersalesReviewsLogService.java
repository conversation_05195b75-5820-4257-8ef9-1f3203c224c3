package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviewsLog;
import com.ets.delivery.application.infra.mapper.AftersalesReviewsLogMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 审核单日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@DS("db-issuer-admin-proxy")
public class AftersalesReviewsLogService extends BaseService<AftersalesReviewsLogMapper, AftersalesReviewsLog> {

}
