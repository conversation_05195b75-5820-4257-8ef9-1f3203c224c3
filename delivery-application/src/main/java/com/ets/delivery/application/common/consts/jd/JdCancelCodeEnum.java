package com.ets.delivery.application.common.consts.jd;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.IntStream;
import java.util.LinkedHashMap;

@Getter
@AllArgsConstructor
public enum JdCancelCodeEnum {

    CANCEL_SUCCESS(1, "取消成功"),
    CANCEL_FAIL(2, "取消失败"),
    CANCEL_PROCESSING(3, "取消中,请稍后查询订单结果");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        JdCancelCodeEnum[] enums = JdCancelCodeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
