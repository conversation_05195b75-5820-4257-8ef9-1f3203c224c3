package com.ets.delivery.application.common.dto.express;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class ExpressSubscribeDTO {

    @NotNull(message = "物流编码不能为空")
    String expressCode;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    String orderSn;

    /**
     * 快递公司
     */
    String expressCompany;

    /**
     * 快递单号
     */
    @NotBlank(message = "快递单号不能为空")
    String expressNumber;

    /**
     * 收件人姓名
     */
    @NotBlank(message = "收件人姓名不能为空")
    String name;

    /**
     * 收件省
     */
    @NotBlank(message = "收件省不能为空")
    String province;

    /**
     * 收件城市
     */
    @NotBlank(message = "收件城市不能为空")
    String city;

    /**
     * 收件区
     */
    String area = "未知区";

    /**
     * 收件详细地址
     */
    @NotBlank(message = "收件详细地址不能为空")
    String address;

    /**
     * 收件人手机
     */
    @NotBlank(message = "收件人手机号码不能为空")
    String mobile;
}
