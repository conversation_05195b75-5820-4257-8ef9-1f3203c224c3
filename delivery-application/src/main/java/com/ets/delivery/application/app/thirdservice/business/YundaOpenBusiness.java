package com.ets.delivery.application.app.thirdservice.business;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.ets.delivery.application.app.thirdservice.feign.YundaOpenApiFeign;
import com.ets.delivery.application.app.thirdservice.request.yundaOpen.YundaOpenExpressSubscribeDTO;
import com.ets.delivery.application.app.thirdservice.response.yundaOpen.YundaOpenExpressQueryVO;
import com.ets.delivery.application.app.thirdservice.response.yundaOpen.YundaOpenExpressSubscribeVO;
import com.ets.delivery.application.common.bo.yunda.YundaLogisticsSubscribeBO;
import com.ets.delivery.application.common.config.yunda.YundaOpenApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Locale;

/**
 * @deprecated
 * 韵达开放平台物流轨迹订阅推送
 * 暂时无用
 */
@Slf4j
@Component
public class YundaOpenBusiness {

    @Autowired
    private YundaOpenApiConfig yundaOpenApiConfig;

    @Autowired
    private YundaOpenApiFeign yundaOpenApiFeign;

    public YundaOpenExpressQueryVO logisticsQuery(String expressNumber) {
        String mailNo = "{\"mailno\":\"" + expressNumber + "\"}";
        String sign = SecureUtil.md5(mailNo + "_" + yundaOpenApiConfig.getAppSecret()).toLowerCase(Locale.ROOT);
        String reqTime = String.valueOf(System.currentTimeMillis());
        String query = yundaOpenApiFeign.logisticsQuery(sign, reqTime, mailNo);
        log.info("query result:{}", query);
        return JSONObject.parseObject(query, YundaOpenExpressQueryVO.class);
    }

    public YundaOpenExpressSubscribeVO subscribe(YundaLogisticsSubscribeBO subscribeBO) {
        YundaOpenExpressSubscribeDTO subscribeDTO = new YundaOpenExpressSubscribeDTO();
        // 订单
        YundaOpenExpressSubscribeDTO.Order order = new YundaOpenExpressSubscribeDTO.Order();
        order.setOrderid(subscribeBO.getOrderid());
        order.setMailno(subscribeBO.getMailno());

        // 发件人信息
        YundaOpenExpressSubscribeDTO.Order.Sender sender = new YundaOpenExpressSubscribeDTO.Order.Sender();
        sender.setName(yundaOpenApiConfig.getSenderName());
        sender.setProvince(yundaOpenApiConfig.getSenderProvince());
        sender.setCity(yundaOpenApiConfig.getSenderCity());
        sender.setCounty(yundaOpenApiConfig.getSenderCounty());
        sender.setAddress(yundaOpenApiConfig.getSenderAddress());
        sender.setMobile(yundaOpenApiConfig.getSenderMobile());
        order.setSender(sender);

        // 收件人信息
        YundaOpenExpressSubscribeDTO.Order.Receiver receiver = new YundaOpenExpressSubscribeDTO.Order.Receiver();
        receiver.setName(subscribeBO.getName());
        receiver.setProvince(subscribeBO.getProvince());
        receiver.setCity(subscribeBO.getCity());
        receiver.setCounty(subscribeBO.getCounty());
        receiver.setAddress(subscribeBO.getAddress());
        receiver.setMobile(subscribeBO.getMobile());
        order.setReceiver(receiver);

        subscribeDTO.setOrders(Collections.singletonList(order));

        String dto = JSONObject.toJSONString(subscribeDTO);
        String sign = SecureUtil.md5(dto + "_" + yundaOpenApiConfig.getAppSecret()).toLowerCase(Locale.ROOT);
        String reqTime = String.valueOf(System.currentTimeMillis());
        String subscribe = yundaOpenApiFeign.subscribe(sign, reqTime, dto);
        log.info("【韵达】订阅结果:{}", subscribe);
        return JSONObject.parseObject(subscribe, YundaOpenExpressSubscribeVO.class);
    }
}
