package com.ets.delivery.application.common.dto.manage;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class ManageStorageSkuMapEditDTO {

    private Integer id = 0;

    /**
     * 商品代码
     */
    @NotBlank(message = "Sku不能为空")
    private String sku;

    /**
     * 仓库代号
     */
    @NotBlank(message = "仓库代号storageCode不能为空")
    private String storageCode;

    /**
     * 仓库对应sku的编码
     */
    @NotBlank(message = "仓库对应sku的编码storageSku不能为空")
    private String storageSku;

    private String goodsName = "";

    /**
     * 指定区域（json格式)
     */
    private String assignArea;

    /**
     * 状态：1正常2关闭
     */
    private Integer status = 1;
}
