package com.ets.delivery.application.common.consts.express;

import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Getter
public enum ExpressCodeEnum {

    JD_CLOUD(ExpressCodeConstant.JD_CLOUD, "京东"),
    YUNDA(ExpressCodeConstant.YUNDA, "韵达"),
    YUNDA_OPEN(ExpressCodeConstant.YUNDA_OPEN, "韵达开放平台"),
    KD100(ExpressCodeConstant.KD100, "快递100");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;

    ExpressCodeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        ExpressCodeEnum[] enums = ExpressCodeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Stream.of(enums)
                .map(ExpressCodeEnum::getValue)
                .collect(Collectors.toList());
    }
}
