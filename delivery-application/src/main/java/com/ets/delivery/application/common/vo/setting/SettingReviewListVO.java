package com.ets.delivery.application.common.vo.setting;

import com.ets.delivery.application.common.consts.setting.SettingKeyEnum;
import com.ets.delivery.application.common.consts.setting.SettingStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SettingReviewListVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 键值key
     */
    private String key;

    private String keyStr;

    /**
     * 渠道
     */
    private String value;

    /**
     * 操作参数
     */
    private String params;

    /**
     * 引导
     */
    private String guide;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 状态：1正常2取消
     */
    private Integer status;

    private String statusStr;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    private Integer sort;

    public String getStatusStr() {
        return SettingStatusEnum.map.getOrDefault(this.status, "未知");
    }

    public String getKeyStr() {
        return SettingKeyEnum.map.getOrDefault(key, "未知");
    }
}
