package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 手动下单发货导入详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_import_manual_logistics_detail")
public class ImportManualLogisticsDetail extends BaseEntity<ImportManualLogisticsDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 发货流水号
     */
    private String logisticsSn;

    /**
     * 原发货流水号
     */
    private String originLogisticsSn;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 下单原因
     */
    private String reason;

    /**
     * 发货快递编码
     */
    private String logisticsCode;

    /**
     * 发货仓储
     */
    private String storageCode;

    /**
     * 商品编码
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品数量
     */
    private Integer nums;

    /**
     * 收货人姓名
     */
    private String sendName;

    /**
     * 收货人电话
     */
    private String sendPhone;

    /**
     * 收货人地区
     */
    private String sendArea;

    /**
     * 收货人详细地址
     */
    private String sendAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分析结果等级
     */
    private Integer resultLevel;

    /**
     * 分析结果说明
     */
    private String resultMsg;

    /**
     * 状态[-1-删除 0-正常]
     */
    private Integer status;

    /**
     * 记录状态[0-初始化 1-已保存 2-已推送]
     */
    private Integer recordStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
