package com.ets.delivery.application.app.thirdservice.response.jd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class JdQueryWarehouseVO extends JdErrorResponseVO {

    @JSONField(name = "jingdong_eclp_master_queryWarehouse_responce")
    private JdQueryWarehouseResponseVO jdQueryWarehouseResponse;

    @Data
    public static class JdQueryWarehouseResponseVO {

            private String code;

            @JSONField(name = "querywarehouse_result")
            private List<JdQueryWarehouseResult> result;

            @Data
            public static class JdQueryWarehouseResult {

                private String warehouseNo;
                private String warehouseName;
                private String status;
                private String contacts;
                private String phone;
                private String province;
                private String city;
                private String county;
                private String town;
                private String address;

            }
    }
}
