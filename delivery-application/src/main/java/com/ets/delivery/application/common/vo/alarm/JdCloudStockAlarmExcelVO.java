package com.ets.delivery.application.common.vo.alarm;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@HeadRowHeight(20)
@ContentRowHeight(15)
@ColumnWidth(25)
public class JdCloudStockAlarmExcelVO {

    @ExcelProperty(value = "我司商品编码")
    private String goodsUnionCode;

    @ExcelProperty(value = "京东仓商品编码")
    private String goodsSku;

    @ExcelProperty(value = "商品名称")
    private String goodsName;

    @ColumnWidth(20)
    @ExcelProperty(value = "昨日京东发货总数")
    private Integer jdYesterdayLogisticsSum;

    @ColumnWidth(20)
    @ExcelProperty(value = "昨日我司推单总数")
    private Integer yesterdayLogisticsNum;

    @ColumnWidth(20)
    @ExcelProperty(value = "出库相差")
    private Integer logisticsDiff;

    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    @ColumnWidth(20)
    @ExcelProperty(value = "总库存数")
    private Integer stockNum;

    @ColumnWidth(20)
    @ExcelProperty(value = "可用库存")
    private Integer stockUsableNum;

    @ColumnWidth(20)
    @ExcelProperty(value = "京东昨天发货量")
    private Integer jdYesterdayOutStockNum;

    @ColumnWidth(20)
    @ExcelProperty(value = "京东过去3天发货量")
    private Integer jdThreeDaysOutStockNum;

    @ColumnWidth(20)
    @ExcelProperty(value = "京东过去7天发货量")
    private Integer jdSevenDaysOutStockNum;

    @ColumnWidth(20)
    @ExcelProperty(value = "预计可发货天数")
    private Integer predictDayNum;
}
