package com.ets.delivery.application.common.consts.sendback;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum SendBackLogTypeEnum {

    TYPE_ADD("add", "新增"),
    TYPE_MODIFY("modify", "修改"),
    TYPE_CHECK("check", "检查"),
    TYPE_CANCEL("cancel", "取消");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        SendBackLogTypeEnum[] enums = SendBackLogTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
