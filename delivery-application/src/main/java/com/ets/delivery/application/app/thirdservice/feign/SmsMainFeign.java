package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.SmsMainFallbackFactory;
import com.ets.delivery.application.app.thirdservice.request.SmsSendDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(
        url = "${microUrls.sms:http://etc-sms-main:8910}",
        name = "etc-sms-main",
        contextId = "sms-main",
        path = "/sms",
        fallbackFactory = SmsMainFallbackFactory.class
)
public interface SmsMainFeign {

    @PostMapping(value = "/send")
    String sendSms(SmsSendDTO sendDTO);
}
