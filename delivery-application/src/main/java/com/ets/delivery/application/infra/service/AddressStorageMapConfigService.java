package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.delivery.application.infra.entity.AddressStorageMapConfig;
import com.ets.delivery.application.infra.mapper.AddressStorageMapConfigMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 地址仓储映射配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
@DS("db-issuer-admin")
public class AddressStorageMapConfigService extends BaseService<AddressStorageMapConfigMapper, AddressStorageMapConfig> {

    public AddressStorageMapConfig getDefaultConfig() {
        return this.lambdaQuery().eq(AddressStorageMapConfig::getIsDefault, true).one();
    }
}
