package com.ets.delivery.application.common.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum DeviceTypeEnum {
    DEVICE_TYPE_NORMAL(0, "普通设备"),
    DEVICE_TYPE_CHARGEABLE(1, "可充电设备"),
    DEVICE_TYPE_MONOLITHIC(2, "单片式设备");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        DeviceTypeEnum[] enums = DeviceTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
