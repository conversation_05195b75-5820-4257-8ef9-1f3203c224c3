package com.ets.delivery.application.common.consts.express;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum ExpressStateEnum {

    DEFAULT(-1, "无"),
    ON_THE_WAY(0, "在途"),
    ACCEPT(1, "揽收"),
    ON_TROUBLE(2, "疑难"),
    RECEIVED(3, "签收"),
    REJECTED(4, "退签"),
    SENDING(5, "派件"),
    SEND_BACK(6, "退回"),
    SEND_ANOTHER(7, "转单"),
    WAIT_CUSTOMS_CLEAR(10, "待清关"),
    CUSTOMS_CLEARING(11, "清关中"),
    CUSTOMS_CLEAR(12, "已清关"),
    CUSTOMS_CLEAR_ABNORMAL(13, "清关异常"),
    RECEIVER_REJECT(14, "收件人拒签"),
    OTHER(99, "其他");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    ExpressStateEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        ExpressStateEnum[] enums = ExpressStateEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
