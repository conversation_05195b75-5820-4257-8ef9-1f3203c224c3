package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 物流单日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExpressLog extends BaseEntity<ExpressLog> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String expressSn;

    private Integer subscribeStatus;

    /**
     * 类型 1：订阅日志 2、实时查询日志
     */
    private Integer type;

    private String expressNumber;

    /**
     * 操作信息
     */
    private String content;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;


}
