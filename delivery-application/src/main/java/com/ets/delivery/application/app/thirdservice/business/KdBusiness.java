package com.ets.delivery.application.app.thirdservice.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.delivery.application.app.thirdservice.feign.KdApiFeign;
import com.ets.delivery.application.app.thirdservice.feign.KdFeign;
import com.ets.delivery.application.app.thirdservice.request.kd.KdExpressQueryDTO;
import com.ets.delivery.application.app.thirdservice.response.kd.KdExpressQueryVO;
import com.ets.delivery.application.common.bo.express.ExpressBO;
import com.ets.delivery.application.common.config.kd.KdConfig;
import com.ets.delivery.application.app.thirdservice.request.kd.KdExpressSubscribeDTO;
import com.ets.delivery.application.app.thirdservice.response.kd.KdAutoNumberVO;
import com.ets.delivery.application.app.thirdservice.response.kd.KdSubscribeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.List;

@Slf4j
@Component
public class KdBusiness {

    @Autowired
    private KdConfig kdConfig;

    @Autowired
    private KdApiFeign kdApiFeign;

    @Autowired
    private KdFeign kdFeign;

    public KdSubscribeVO subscribe(ExpressBO expressBO) {
        KdExpressSubscribeDTO subscribeDTO = new KdExpressSubscribeDTO();
        subscribeDTO.setSchema(kdConfig.getSchema());

        KdExpressSubscribeDTO.Param param = new KdExpressSubscribeDTO.Param();
        param.setKey(kdConfig.getAppKey());
        param.setNumber(expressBO.getExpressNumber());
        param.setCompany(expressBO.getExpressCompany());

        KdExpressSubscribeDTO.Param.Parameters parameters = new KdExpressSubscribeDTO.Param.Parameters();
        parameters.setResultv2(kdConfig.getResultv2());
        parameters.setCallbackurl(kdConfig.getCallbackUrl());
        parameters.setPhone(expressBO.getPhone());
        param.setParameters(parameters);

        subscribeDTO.setParam(JSONObject.toJSONString(param));
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.setAll(BeanUtil.beanToMap(subscribeDTO));
        String subscribe = kdApiFeign.subscribe(body);
        log.info("【快递100】订阅结果:{}", subscribe);
        return JSON.parseObject(subscribe, KdSubscribeVO.class);
    }

    public KdExpressQueryVO expressQuery(ExpressBO expressBO) {
        KdExpressQueryDTO queryDTO = new KdExpressQueryDTO();

        KdExpressQueryDTO.Param param = new KdExpressQueryDTO.Param();
        param.setCom(expressBO.getExpressCompany());
        param.setPhone(expressBO.getPhone());
        param.setNum(expressBO.getExpressNumber());
        param.setResultv2(kdConfig.getResultv2());
        String paramStr = JSON.toJSONString(param);

        queryDTO.setCustomer(kdConfig.getCustomer());
        queryDTO.setParam(paramStr);
        queryDTO.setSign(buildSign(paramStr));

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.setAll(BeanUtil.beanToMap(queryDTO));
        String expressQuery = kdApiFeign.expressQuery(body);
        log.info("【快递100】查询物流轨迹结果：{}", expressQuery);
        return JSON.parseObject(expressQuery, KdExpressQueryVO.class);
    }

    public List<KdAutoNumberVO> autoNumber(String expressNumber) {
        String autoNumber = kdFeign.autoNumber(expressNumber, kdConfig.getAppKey());
        log.info("【快递100】智能单号识别结果:{}", autoNumber);
        List<KdAutoNumberVO> autoNumberVO = null;
        try {
            autoNumberVO = JSON.parseArray(autoNumber, KdAutoNumberVO.class);
        } catch (Exception e) {
            log.error("【快递100】智能单号识别结果解析失败:{}", e.getMessage());
        }
        return autoNumberVO;
    }

    private String buildSign(String param) {
        return SecureUtil.md5(param + kdConfig.getAppKey() + kdConfig.getCustomer()).toUpperCase();
    }
}
