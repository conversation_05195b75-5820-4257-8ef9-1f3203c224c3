package com.ets.delivery.application.common.dto.setting;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

import java.util.List;

@Data
public class SettingListDTO {

    private List<String> categoryList;

    private String category;

    private String key;

    private String value;

    private Integer status;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
