package com.ets.delivery.application.app.disposer;

import com.alibaba.fastjson.JSON;
import com.ets.common.JsonResult;
import com.ets.common.queue.BaseDisposer;
import com.ets.delivery.application.app.thirdservice.feign.SmsMainFeign;
import com.ets.delivery.application.app.thirdservice.request.SmsSendDTO;
import com.ets.delivery.application.common.bo.PickUpLogBO;
import com.ets.delivery.application.common.bo.PickUpSmsBO;
import com.ets.delivery.application.common.config.SmsConfig;
import com.ets.delivery.application.common.consts.pickUp.PickUpLogTypeEnum;
import com.ets.delivery.application.common.consts.pickUp.PickUpSmsStatusEnum;
import com.ets.delivery.application.infra.entity.Pickup;
import com.ets.delivery.application.infra.service.PickupLogService;
import com.ets.delivery.application.infra.service.PickupService;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@NoArgsConstructor
@Component(value = "PickUpSmsNotifyJobBean")
public class PickUpSmsNotifyDisposer extends BaseDisposer {

    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private SmsMainFeign smsMainFeign;

    @Autowired
    private PickupService pickupService;

    @Autowired
    private PickupLogService pickupLogService;

    public PickUpSmsNotifyDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "PickUpSmsNotifyJobBean";
    }

    @Override
    public void execute(Object content) throws Exception {
        PickUpSmsBO smsBO = super.getParamsObject(content, PickUpSmsBO.class);
        Integer pickupId = smsBO.getPickupId();

        Pickup pickup = pickupService.getById(pickupId);

        if (ObjectUtils.isEmpty(pickup)) {
            log.error("【上门取件短信通知】上门取件记录不存在，pickupId={}", pickupId);
            return;
        }

        // 后台创建记录
        // 状态待发送
        if (pickup.getOrderSource().equals("admin") && pickup.getSmsStatus().equals(PickUpSmsStatusEnum.WAIT_SEND.getValue())) {
            try {
                // 短信开关
                if (smsConfig.getSmsSwitch() == 0) {
                    return;
                }

                String templateId = smsConfig.getSmsTemplate().getOrDefault("wait-to-pickup", "");
                if (StringUtils.isEmpty(templateId)) {
                    log.error("【上门取件短信通知】短信模板配置不存在");
                    return;
                }

                // 参数
                SmsSendDTO smsSendDTO = new SmsSendDTO();
                smsSendDTO.setTemId(templateId);
                smsSendDTO.setMobiles(Collections.singletonList(pickup.getSendMobile()));
                smsSendDTO.setSmsChannel("MW");
                smsSendDTO.setNoticeType(1);
                smsSendDTO.setBizCode("others");

                Map<String, String> paramsMap = new HashMap<>();
                paramsMap.put("pickup_code", pickup.getPickupCode());
                paramsMap.put("courier", pickup.getPickupName());
                paramsMap.put("mobile", pickup.getPickupMobile());
                paramsMap.put("pickup_time_range", pickup.getPickupStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                        + "~" + pickup.getPickupEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                smsSendDTO.setParamsMap(paramsMap);

                String result = smsMainFeign.sendSms(smsSendDTO);
                JsonResult<?> jsonResult = JSON.parseObject(result, JsonResult.class);
                jsonResult.checkError();
            } catch (Exception e) {
                pickup.setSmsStatus(PickUpSmsStatusEnum.SEND_FAIL.getValue());
                pickupService.updateById(pickup);

                // 记录日志
                PickUpLogBO logBO = new PickUpLogBO();
                logBO.setPickupId(pickupId);
                logBO.setType(PickUpLogTypeEnum.UPDATE.getValue());
                logBO.setOperator("system");
                logBO.setOperateContent("短信发送失败，错误：" + e.getMessage());
                pickupLogService.addLog(logBO);

                return;
            }

            pickup.setSmsStatus(PickUpSmsStatusEnum.SEND_SUCCESS.getValue());
            pickupService.updateById(pickup);

            // 记录日志
            PickUpLogBO logBO = new PickUpLogBO();
            logBO.setPickupId(pickupId);
            logBO.setType(PickUpLogTypeEnum.UPDATE.getValue());
            logBO.setOperator("system");
            logBO.setOperateContent("短信发送成功");
            pickupLogService.addLog(logBO);
        }

    }
}
