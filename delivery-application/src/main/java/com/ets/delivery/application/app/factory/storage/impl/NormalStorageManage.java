package com.ets.delivery.application.app.factory.storage.impl;

import com.ets.delivery.application.common.dto.logistics.LogisticsAddOrderDTO;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.ExWarehouseDetail;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class NormalStorageManage extends StorageBase {

    @Override
    public ExWarehouse initExWarehouse(LogisticsAddOrderDTO addOrderDTO) {
        return null;
    }

    @Override
    public List<ExWarehouseDetail> initExWarehouseDetail(LogisticsAddOrderDTO addOrderDTO) {
        return null;
    }
}
