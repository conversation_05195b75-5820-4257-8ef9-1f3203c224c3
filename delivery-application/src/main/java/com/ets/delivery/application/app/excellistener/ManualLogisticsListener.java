package com.ets.delivery.application.app.excellistener;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.importFile.impl.ImportFileLogistics;
import com.ets.delivery.application.common.consts.manualLogistics.ImportManualLogisticsRecordStatusEnum;
import com.ets.delivery.application.common.consts.manualLogistics.ImportManualLogisticsStatusEnum;
import com.ets.delivery.application.common.consts.manualLogistics.ManualLogisticsResultLevelEnum;
import com.ets.delivery.application.common.dto.manualLogistics.ManualLogisticsImportDTO;
import com.ets.delivery.application.infra.entity.ImportManualLogisticsDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ManualLogisticsListener extends AnalysisEventListener<ManualLogisticsImportDTO> {

    private List<ImportManualLogisticsDetail> dataList;

    private final String batchNo;

    private ImportFileLogistics importFileLogistics;

    private Integer headLine = 1;

    public ManualLogisticsListener(String batchNo, ImportFileLogistics importFileLogistics) {
        this.batchNo = batchNo;
        this.importFileLogistics = importFileLogistics;
        this.dataList = new ArrayList<>();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        if (headLine++ == 1) {
            return;
        }

        log.info("headMap:{}", headMap);

        // 校验表头
        Collection<String> excelHead = headMap.values();
        List<String> head = Arrays.asList("原发货流水号", "下单原因", "快递", "商品编码", "发货数量", "收件人手机", "收件人姓名", "收件人省", "收件人市", "收件人区", "收件人详细地址", "备注");
        head.forEach(key -> {
            if (!excelHead.contains(key)) {
                ToolsHelper.throwException("上传文件列项与模板不一致");
            }
        });

        // 行数
        Integer total = context.readSheetHolder().getApproximateTotalRowNumber();
        if (total == 2 || total > 102) {
            ToolsHelper.throwException("上传文件超过行数限制，请上传最小为1条最大为100条数据");
        }
    }

    @Override
    public void invoke(ManualLogisticsImportDTO manualLogisticsImportDTO, AnalysisContext analysisContext) {
        // 清洗数据
        if (ObjectUtils.isEmpty(manualLogisticsImportDTO)) {
            return;
        }

        try {
            Integer.valueOf(manualLogisticsImportDTO.getNums());
        } catch (NumberFormatException e) {
            manualLogisticsImportDTO.setNums("-1");
        }

        // 组装数据
        ImportManualLogisticsDetail detail = BeanUtil.copyProperties(manualLogisticsImportDTO, ImportManualLogisticsDetail.class);
        detail.setBatchNo(batchNo);
        detail.setSendArea(manualLogisticsImportDTO.getSendProvince() + " " +
                manualLogisticsImportDTO.getSendCity() + " " +
                manualLogisticsImportDTO.getSendDistrict());
        detail.setPlateNo("");
        detail.setGoodsName("");
        detail.setResultLevel(ManualLogisticsResultLevelEnum.LEVEL_NORMAL.getValue());
        detail.setResultMsg("");
        detail.setStatus(ImportManualLogisticsStatusEnum.NORMAL.getValue());
        detail.setRecordStatus(ImportManualLogisticsRecordStatusEnum.DEFAULT.getValue());
        detail.setCreatedAt(LocalDateTime.now());
        detail.setUpdatedAt(LocalDateTime.now());

        // 检查失败项目
        String errorMsg = importFileLogistics.checkImportError(manualLogisticsImportDTO);
        if (StringUtils.isNotEmpty(errorMsg)) {
            detail.setResultLevel(ManualLogisticsResultLevelEnum.LEVEL_ERROR.getValue());
            detail.setResultMsg(errorMsg);
        }

        // 没有失败
        if (StringUtils.isEmpty(errorMsg)) {
            // 检查警告项目
            String warningMsg = importFileLogistics.checkImportWarning(manualLogisticsImportDTO);
            if (StringUtils.isNotEmpty(warningMsg)) {
                detail.setResultLevel(ManualLogisticsResultLevelEnum.LEVEL_WARNING.getValue());
                detail.setResultMsg(warningMsg);
            }
        }

        // 填充信息
        detail = importFileLogistics.fillData(detail);

        // 缓存
        dataList.add(detail);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 查重标记
        Map<String, Long> counts = dataList.stream()
                .filter(detail -> StringUtils.isNotEmpty(detail.getOriginLogisticsSn()))
                .collect(Collectors.groupingBy(ImportManualLogisticsDetail::getOriginLogisticsSn, Collectors.counting()));

        dataList = dataList.stream()
                .peek(detail -> {
                    if (counts.get(detail.getOriginLogisticsSn()) > 1) {
                        detail.setResultMsg(detail.getResultMsg() + "文件中存在相同的原发货流水号;");
                        // 正常需要改成警告
                        if (detail.getResultLevel().equals(ManualLogisticsResultLevelEnum.LEVEL_NORMAL.getValue())) {
                            detail.setResultLevel(ManualLogisticsResultLevelEnum.LEVEL_WARNING.getValue());
                        }
                    }
                })
                .collect(Collectors.toList());

        log.info("【手动发货】导入成功 批次号：{} 条数：{}", batchNo, dataList.size());
        importFileLogistics.saveImportFileData(batchNo, dataList);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("【手动发货】导入失败：", exception);
        throw exception;
    }
}
