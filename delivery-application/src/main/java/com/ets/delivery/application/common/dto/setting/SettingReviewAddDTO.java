package com.ets.delivery.application.common.dto.setting;

import com.ets.delivery.application.common.consts.setting.SettingCategoryEnum;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class SettingReviewAddDTO {

    private Integer type = 1;

    private String category = SettingCategoryEnum.REVIEW.getValue();

    /**
     * 键值key
     */
    @NotBlank(message = "key不能为空")
    private String key;

    /**
     * 渠道
     */
    @NotBlank(message = "value不能为空")
    private String value;

    /**
     * 操作参数
     */
    private String params = "";

    /**
     * 排序
     */
    private Integer sort = 0;
}
