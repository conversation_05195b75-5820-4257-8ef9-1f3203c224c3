package com.ets.delivery.application.infra.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.delivery.application.common.bo.ReviewLogBO;
import com.ets.delivery.application.infra.entity.ReviewsLog;
import com.ets.delivery.application.infra.mapper.ReviewsLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;

/**
 * <p>
 * 审核单日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
@DS("db-issuer-admin")
public class ReviewsLogService extends BaseService<ReviewsLogMapper, ReviewsLog> {

    public void addLog(@Validated ReviewLogBO logBO) {
        ReviewsLog log = BeanUtil.copyProperties(logBO, ReviewsLog.class);
        log.setCreatedAt(LocalDateTime.now());
        log.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(log);
    }
}
