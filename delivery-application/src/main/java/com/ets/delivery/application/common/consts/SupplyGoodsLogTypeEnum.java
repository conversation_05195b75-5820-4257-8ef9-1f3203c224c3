package com.ets.delivery.application.common.consts;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum SupplyGoodsLogTypeEnum {

    TYPE_ADD("add", "新增"),
    TYPE_MODIFY("modify", "修改");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    SupplyGoodsLogTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        SupplyGoodsLogTypeEnum[] enums = SupplyGoodsLogTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
