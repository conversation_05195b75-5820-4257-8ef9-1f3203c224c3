package com.ets.delivery.application.common.dto.setting;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AfterSalesReviewReasonEditDTO {

    @NotNull(message = "id不能为空")
    private Integer id;

    @NotBlank(message = "分类不能为空")
    private String category;

    @NotBlank(message = "原因不能为空")
    private String reason;

}
