package com.ets.delivery.application.common.vo.manualLogistics;

import com.ets.delivery.application.common.consts.manualLogistics.ManualLogisticsReasonEnum;
import lombok.Data;

import java.util.List;

@Data
public class ManualLogisticsDetailVO {

    private Integer id;

    /**
     * 原发货流水号
     */
    private String originOrderSn;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 所属仓储
     */
    private String storageCode;

    /**
     * 指定发货物流编码
     */
    private String logisticsCode;

    private List<ManualLogisticsGoodsVO> goodsList;

    /**
     * 收件人
     */
    private String sendName;

    /**
     * 联系手机
     */
    private String sendPhone;

    /**
     * 发货地区
     */
    private String sendArea;

    /**
     * 收货地址
     */
    private String sendAddress;

    /**
     * 下单原因
     */
    private String reason;

    /**
     * 下单备注
     */
    private String remark;
}
