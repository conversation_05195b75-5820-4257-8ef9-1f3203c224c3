package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.YundaOpenApiFeign;
import org.springframework.cloud.openfeign.FallbackFactory;

import org.springframework.stereotype.Component;

@Component
public class YundaOpenApiFallbackFactory implements FallbackFactory<YundaOpenApiFeign> {
    @Override
    public YundaOpenApiFeign create(Throwable throwable) {
        return new YundaOpenApiFeign() {
            @Override
            public String logisticsQuery(String sign, String reqTime, String json) {
                return JsonResult.error("请求【YundaOpenApi】【logisticsQuery】接口失败: " + throwable.getMessage()).toString();
            }

            @Override
            public String subscribe(String sign, String reqTime, String json) {
                return JsonResult.error("请求【YundaOpenApi】【subscribe】接口失败: " + throwable.getMessage()).toString();
            }
        };
    }
}
