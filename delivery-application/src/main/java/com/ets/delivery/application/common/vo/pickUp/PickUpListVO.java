package com.ets.delivery.application.common.vo.pickUp;

import com.ets.delivery.application.common.consts.pickUp.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Data
public class PickUpListVO {

    /**
     * 自增id
     */
    private Integer id;

    /**
     * 上门取件单订单号
     */
    private String pickupSn;

    /**
     * 取件单号
     */
    private String pickupCode;

    /**
     * 业务订单号
     */
    private String orderSn;

    /**
     * 订单类型[upgrade-设备升级]
     */
    private String orderType;

    private String orderTypeStr;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 预约时间段开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pickupStartTime;

    /**
     * 预约时间段结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pickupEndTime;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 寄件人姓名
     */
    private String sendName;

    /**
     * 寄件人电话
     */
    private String sendMobile;

    /**
     * 寄件地区
     */
    private String sendArea;

    /**
     * 寄件地址
     */
    private String sendAddress;

    /**
     * 取件状态[-1-取消 0-默认 1-下单成功 2-下单失败 3-待上门取件 4-取件完成 5-取件妥投 6-取件异常]
     */
    private Integer pickupStatus;

    private String pickupStatusStr;

    /**
     * 订单状态[-1-取消 1-正常]
     */
    private Integer orderStatus;

    private String orderStatusStr;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 短信状态[0-无需发送 1-待发送 2-发送成功 3-发送失败]
     */
    private Integer smsStatus;

    private String smsStatusStr;

    /**
     * 创建方式
     */
    private Integer createType = PickUpCreateTypeEnum.TYPE_BUSINESS.getValue();

    private String createTypeStr;

    /**
     * 取件成功时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pickupTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    private List<PickUpGoodsListVO> goodsList;

    /**
     * 是否显示取消按钮
     */
    private boolean showCancelBtn;

    public String getOrderTypeStr() {
        return PickUpOrderTypeEnum.map.getOrDefault(orderType, "未知");
    }

    public String getPickupStatusStr() {
        return PickUpStatusEnum.map.getOrDefault(pickupStatus, "未知");
    }

    public String getOrderStatusStr() {
        return PickUpOrderStatusEnum.map.getOrDefault(orderStatus, "未知");
    }

    public String getCreateTypeStr() {
        return PickUpCreateTypeEnum.map.getOrDefault(createType, "未知");
    }

    public String getSmsStatusStr() {
        return PickUpSmsStatusEnum.map.getOrDefault(smsStatus, "未知");
    }

    public boolean isShowCancelBtn() {
        return orderSource.equals("admin")
                && Arrays.asList(
                        PickUpStatusEnum.STATUS_CREATE_SUCCESS.getValue(),
                        PickUpStatusEnum.STATUS_CREATE_FAIL.getValue(),
                        PickUpStatusEnum.STATUS_WAIT_PICKUP.getValue(),
                        PickUpStatusEnum.STATUS_PICKUP_ERROR.getValue()
                ).contains(pickupStatus);
    }
}
