package com.ets.delivery.application.app.factory.express;

import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeDTO;
import com.ets.delivery.application.infra.entity.Express;

public interface IExpress {

    void subscribe(ExpressSubscribeDTO subscribeDTO);

    Express expressNotify(ExpressNotifyDTO notifyDTO);

    Express expressQuery(Express express);
}
