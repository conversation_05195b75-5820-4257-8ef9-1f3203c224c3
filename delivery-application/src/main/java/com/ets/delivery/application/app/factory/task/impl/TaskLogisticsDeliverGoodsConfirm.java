package com.ets.delivery.application.app.factory.task.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOperateTypeEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsStatusEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordErrorEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordStatusEnum;
import com.ets.delivery.application.common.dto.task.LogisticsConfirmDTO;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
/*
 *  发货确认流程，获取到物流单号，触发发货通知
 *   更新出库单数据
 *   更新发货单的物流信息
 *   触发回调业务方发货通知
 */
@Slf4j
@Component
public class TaskLogisticsDeliverGoodsConfirm extends TaskBase {
    @Autowired
    private LogisticsService logisticsService;
    @Autowired
    private LogisticsLogService logisticsLogService;

    @Qualifier("redisPermanentTemplate")
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Value("${spring.profiles.active}")
    private String ACTIVE;
    @Autowired
    private ExWarehouseService exWarehouseService;


    @Override
    public void childExec(TaskRecord currentTaskRecord){
        try{
            //解析任务内容参数
            LogisticsConfirmDTO logisticsConfirmDTO = JSONObject.toJavaObject(JSONObject.parseObject(currentTaskRecord.getNotifyContent()),LogisticsConfirmDTO.class);

            //判断发货单是否正常状态
            Logistics logistics = logisticsService.getByLogisticsSn(currentTaskRecord.getReferSn());

            if(logistics == null){
                String msg = "发货单"+  currentTaskRecord.getReferSn()+"订单状态不正常不进行出库下单";
                ToolsHelper.throwException(msg, TaskRecordErrorEnum.TASK_ERROR_CODE_NO_NEED_DEAL.getCode());
            }
            if(
                ! logistics.getStatus().equals(LogisticsStatusEnum.STATUS_NORMAL.getValue()) ||
                    !Arrays.asList(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue()).contains(logistics.getStatus())
            ){
                String msg = "发货单"+  currentTaskRecord.getReferSn()+"订单状态不正常不进行出库下单";
                //添加日志
                logisticsLogService.addLog(logistics.getId(), LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),msg,"system");
                ToolsHelper.throwException(msg, TaskRecordErrorEnum.TASK_ERROR_CODE_NO_NEED_DEAL.getCode());
            }
            //加锁
            if (! ToolsHelper.addLock(redisTemplate, "TaskRecord:"+logistics.getLogisticsSn(), 20)) {
                ToolsHelper.throwException("发货单处理中，请稍后！");
            }

            //出库单是否存在
            ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(logisticsConfirmDTO.getLogisticsSn());
            if( exWarehouse == null){
                ToolsHelper.throwException("出库单"+logisticsConfirmDTO.getLogisticsSn()+"不存在");
            }
            //更新出库单 10020已出库
            if(exWarehouse.getWayBill().isEmpty()){
                exWarehouse.setWayBill(logisticsConfirmDTO.getExpressNumber());
                exWarehouse.setShipperNo(logisticsConfirmDTO.getExpressCorpNo().toLowerCase(Locale.ROOT));
                exWarehouse.setShipperName(logisticsConfirmDTO.getExpressCorp());
                exWarehouse.setWarehouseNo(logisticsConfirmDTO.getWarehouseNo());
                exWarehouse.setCurrentStatus(logisticsConfirmDTO.getDeliveryStatus());
                exWarehouseService.save(exWarehouse);
            }
            //更新发货单
            if(logistics.getExpressNumber().isEmpty()){
                logistics.setExpressCorp(logisticsConfirmDTO.getExpressCorp());
                logistics.setExpressNumber(logisticsConfirmDTO.getExpressNumber());
                logistics.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue());
                logistics.setDeliveryTime(LocalDateTime.parse(logisticsConfirmDTO.getDeliveryTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                //添加日志
                logisticsLogService.addLog(
                    logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                    logisticsConfirmDTO.getExpressCorp()+"物流单号:"+logisticsConfirmDTO.getExpressNumber(),
                    "system"
                );
                Map<String,Integer> map = new HashMap<>();
                map.put("logistics_id",logistics.getId());
                //通知发货
                TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_DELIVER_GOODS_CONFIRM.getType()).addAndPush(new TaskRecordDTO(
                    ToolsHelper.genNum(redisTemplate,"task_record",ACTIVE,8),
                    logistics.getLogisticsSn(),
                    TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType(),
                    JSON.toJSONString(map),
                    TaskRecordStatusEnum.TASK_STATUS_WAIT.getCode(),
                    LocalDateTime.now(),
                        0
                ));
            }

        }catch (BizException e) {
            //原路输出
            ToolsHelper.throwException(e.getMessage(), e.getErrorCode());
        }catch(Exception e1){
            ToolsHelper.throwException("系统错误"+e1.getMessage());
        }
    }
}
