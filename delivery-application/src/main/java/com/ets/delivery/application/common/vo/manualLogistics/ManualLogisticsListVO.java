package com.ets.delivery.application.common.vo.manualLogistics;

import com.ets.delivery.application.common.consts.logistics.LogisticsExpressStatusEnum;
import com.ets.delivery.application.common.consts.manualLogistics.ManualLogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.manualLogistics.ManualLogisticsReasonEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ManualLogisticsListVO {

    private Integer id;

    /**
     * 手动下单订单号
     */
    private String orderSn;

    /**
     * 原发货流水号
     */
    private String originOrderSn;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    private List<ManualLogisticsGoodsVO> goodsList;

    /**
     * 收件人
     */
    private String sendName;

    /**
     * 联系手机
     */
    private String sendPhone;

    /**
     * 发货地区
     */
    private String sendArea;

    /**
     * 收货地址
     */
    private String sendAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 下单原因
     */
    private String reason;

    private String reasonStr;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 快递状态[1-已发货 2-已揽收 3-运输中 4-已签收]
     */
    private Integer expressStatus;

    private String expressStatusStr;

    /**
     * 发货状态[0-待发货, 1-发货中, 2-已发货, 3-发货取消, 4-发货暂停]
     */
    private Integer deliveryStatus;

    private String deliveryStatusStr;

    /**
     * 创建人
     */
    private String operator;

    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pushTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    public String getReasonStr() {
        return ManualLogisticsReasonEnum.map.getOrDefault(reason, "未知");
    }

    public String getExpressStatusStr() {
        return LogisticsExpressStatusEnum.map.getOrDefault(expressStatus, "未知");
    }

    public String getDeliveryStatusStr() {
        return ManualLogisticsDeliveryStatusEnum.map.getOrDefault(deliveryStatus, "未知");
    }


}
