package com.ets.delivery.application.controller.admin;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.SettingBusiness;
import com.ets.delivery.application.common.consts.setting.SettingCategoryEnum;
import com.ets.delivery.application.common.consts.setting.SettingKeyEnum;
import com.ets.delivery.application.common.dto.setting.SettingAddDTO;
import com.ets.delivery.application.common.dto.setting.SettingListDTO;
import com.ets.delivery.application.common.dto.setting.SettingReviewAddDTO;
import com.ets.delivery.application.common.vo.setting.SettingReviewListVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Arrays;

@RestController
@RequestMapping("/admin/setting/review")
public class SettingReviewController {

    @Autowired
    private SettingBusiness settingBusiness;

    @RequestMapping("/getList")
    public JsonResult<IPage<SettingReviewListVO>> getList(@Valid @RequestBody SettingListDTO listDTO) {
        listDTO.setCategory(SettingCategoryEnum.REVIEW.getValue());
        if (ObjectUtils.isEmpty(listDTO.getKey()) ||
                !Arrays.asList(SettingKeyEnum.VEHICLE_TYPE.getValue(),
                        SettingKeyEnum.USE_CHARACTER.getValue()).contains(listDTO.getKey())) {
            ToolsHelper.throwException("key错误");
        }

        return JsonResult.ok(settingBusiness.getList(listDTO));
    }

    @RequestMapping("/add")
    public JsonResult<?> add(@Valid @RequestBody SettingReviewAddDTO reviewAddDTO) {
        if (!Arrays.asList(SettingKeyEnum.VEHICLE_TYPE.getValue(),
                        SettingKeyEnum.USE_CHARACTER.getValue()).contains(reviewAddDTO.getKey())) {
            ToolsHelper.throwException("key分类错误");
        }
        SettingAddDTO addDTO = BeanUtil.copyProperties(reviewAddDTO, SettingAddDTO.class);
        settingBusiness.addSetting(addDTO);
        return JsonResult.ok();
    }
}
