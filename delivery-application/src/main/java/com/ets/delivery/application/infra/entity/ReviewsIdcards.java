package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 审核身份证数据
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_reviews_idcards")
public class ReviewsIdcards extends BaseEntity<ReviewsIdcards> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审核单sn
     */
    private String reviewSn;

    /**
     * 证件名称
     */
    private String name;

    /**
     * 证件类型：1身份证 2 港澳回乡证 3 台胞证
     */
    private Integer type;

    /**
     * 证件号码
     */
    private String number;

    /**
     * 证件正面
     */
    private String frontImgUrl;

    /**
     * 证件反面
     */
    private String backImgUrl;

    /**
     * 手持证件照
     */
    private String holdImgUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
