package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.RefundFeign;
import org.springframework.cloud.openfeign.FallbackFactory;

import org.springframework.stereotype.Component;

@Component
public class RefundFallbackFactory implements FallbackFactory<RefundFeign> {
    @Override
    public RefundFeign create(Throwable throwable) {
        return new RefundFeign() {
            @Override
            public JsonResult<?> subscribeFailed(String orderSn) {
                return JsonResult.error("请求售后接口subscribeFailed失败");
            }
        };
    }
}
