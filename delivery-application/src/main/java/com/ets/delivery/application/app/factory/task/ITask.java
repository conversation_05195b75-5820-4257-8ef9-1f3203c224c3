package com.ets.delivery.application.app.factory.task;

import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.infra.entity.TaskRecord;

import java.time.LocalDateTime;

public interface ITask {
    void addAndPush(TaskRecordDTO taskRecordDTO);
    void beforeExec(TaskRecord taskRecord);
    void execute(String taskSn);
    //子类实际执行
    void childExec(TaskRecord taskRecord);
    void afterExec(TaskRecord taskRecord, Integer status, String msg);
}
