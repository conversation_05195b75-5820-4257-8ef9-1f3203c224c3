package com.ets.delivery.application.common.consts.riskreview;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum RiskReviewRiskTypeEnum {

    FIRST_REVIEW(1, "初审"),
    RECHECK_REVIEW(2, "复审");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    static {
        map = Arrays.stream(RiskReviewRiskTypeEnum.values()).collect(Collectors.toMap(RiskReviewRiskTypeEnum::getValue, RiskReviewRiskTypeEnum::getDesc));
    }

    public static boolean isValidValue(Integer value) {
        if (value == null) {
            return false;
        }
        return Arrays.stream(RiskReviewRiskTypeEnum.values())
                .anyMatch(enumValue -> enumValue.getValue().equals(value));
    }

    public static List<Map<String, String>> getSelectOptions() {
        return Arrays.stream(RiskReviewRiskTypeEnum.values())
                .map(enumValue -> Map.of("label", enumValue.getDesc(), "value", enumValue.getValue().toString()))
                .collect(Collectors.toList());
    }
}
