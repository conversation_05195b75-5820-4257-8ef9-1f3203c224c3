package com.ets.delivery.application.app.thirdservice.request.yunda;


import lombok.Data;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class YundaOrderCancelXmlDTO implements Serializable {
    String ownerCode;
    String orderCode;
    String orderType;
}
