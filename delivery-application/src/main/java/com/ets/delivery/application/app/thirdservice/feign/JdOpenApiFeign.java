package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.JdOpenApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(url = "${jd.apiUrl}", name = "JdOpenApiFeign", fallbackFactory = JdOpenApiFallbackFactory.class)
public interface JdOpenApiFeign {

    @PostMapping(path = "/api/MOGU/standardCalendar", consumes = MediaType.APPLICATION_JSON_VALUE)
    String StandardCalendar(
            @RequestBody String jsonBody,
            @RequestParam(value = "app_key") String appKey,
            @RequestParam(value = "access_token") String accessToken,
            @RequestParam(value = "timestamp") String timestamp,
            @RequestParam(value = "v") String v,
            @RequestParam(value = "LOP-DN") String lopDn,
            @RequestParam(value = "sign") String sign
    );

    @PostMapping(path = "/api/MOGU/checkBlindArea", consumes = MediaType.APPLICATION_JSON_VALUE)
    String CheckBlindArea(
            @RequestBody String jsonBody,
            @RequestParam(value = "app_key") String appKey,
            @RequestParam(value = "access_token") String accessToken,
            @RequestParam(value = "timestamp") String timestamp,
            @RequestParam(value = "v") String v,
            @RequestParam(value = "LOP-DN") String lopDn,
            @RequestParam(value = "sign") String sign
    );

    @PostMapping(path = "/query/estimatedfreights", consumes = MediaType.APPLICATION_JSON_VALUE)
    String QueryEstimatedFreights (
            @RequestBody String jsonBody,
            @RequestParam(value = "app_key") String appKey,
            @RequestParam(value = "access_token") String accessToken,
            @RequestParam(value = "timestamp") String timestamp,
            @RequestParam(value = "v") String v,
            @RequestParam(value = "LOP-DN") String lopDn,
            @RequestParam(value = "sign") String sign
    );

    @PostMapping(path = "/PickupReceiveApi/receivePickUpOrder", consumes = MediaType.APPLICATION_JSON_VALUE)
    String ReceivePickUpOrder(
            @RequestBody String jsonBody,
            @RequestParam(value = "app_key") String appKey,
            @RequestParam(value = "access_token") String accessToken,
            @RequestParam(value = "timestamp") String timestamp,
            @RequestParam(value = "v") String v,
            @RequestParam(value = "LOP-DN") String lopDn,
            @RequestParam(value = "sign") String sign
    );

    @PostMapping(path = "/pickupordercancel", consumes = MediaType.APPLICATION_JSON_VALUE)
    String PickupOrderCancel(
            @RequestBody String jsonBody,
            @RequestParam(value = "app_key") String appKey,
            @RequestParam(value = "access_token") String accessToken,
            @RequestParam(value = "timestamp") String timestamp,
            @RequestParam(value = "v") String v,
            @RequestParam(value = "LOP-DN") String lopDn,
            @RequestParam(value = "sign") String sign
    );

    @PostMapping(path = "/query/dynamictraceinfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    String QueryDynamicTraceInfo(
            @RequestBody String jsonBody,
            @RequestParam(value = "app_key") String appKey,
            @RequestParam(value = "access_token") String accessToken,
            @RequestParam(value = "timestamp") String timestamp,
            @RequestParam(value = "v") String v,
            @RequestParam(value = "LOP-DN") String lopDn,
            @RequestParam(value = "sign") String sign
    );
}
