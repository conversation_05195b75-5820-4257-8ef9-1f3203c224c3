package com.ets.delivery.application.common.consts.storageRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum StorageRecordLogTypeEnum {

    ADD("add", "新增"),
    EDIT("edit", "编辑");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        map = Arrays.stream(StorageRecordLogTypeEnum.values()).collect(Collectors.toMap(StorageRecordLogTypeEnum::getValue, StorageRecordLogTypeEnum::getDesc));
    }
}
