package com.ets.delivery.application.common.consts.logistics;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum LogisticsStatusEnum {

    STATUS_NORMAL(1, "正常"),
    STATUS_CANCEL(2, "取消"),
    STATUS_STOP(3, "暂停");
    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    LogisticsStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        LogisticsStatusEnum[] enums = LogisticsStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
