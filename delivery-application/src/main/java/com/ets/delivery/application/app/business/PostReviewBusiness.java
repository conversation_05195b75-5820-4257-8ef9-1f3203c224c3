package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.common.bo.PostReviewListBO;
import com.ets.delivery.application.common.bo.ReviewDataBO;
import com.ets.delivery.application.common.bo.ReviewLogBO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.consts.ReviewLogTypeEnum;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.VehicleBelongEnum;
import com.ets.delivery.application.common.consts.postReviews.PostReviewExceptionStatusEnum;
import com.ets.delivery.application.common.consts.postReviews.PostReviewStatusEnum;
import com.ets.delivery.application.common.dto.postReviews.*;
import com.ets.delivery.application.common.vo.ReviewListVO;
import com.ets.delivery.application.common.vo.postReviews.PostReviewAbnormalListVO;
import com.ets.delivery.application.common.vo.postReviews.PostReviewVO;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PostReviewBusiness {

    @Autowired
    DeliveryConfig deliveryConfig;

    @Autowired
    PostReviewsService postReviewsService;

    @Autowired
    ReviewsService reviewsService;

    @Autowired
    ReviewsLogService reviewsLogService;

    @Autowired
    ReviewsIdcardsService idCardsService;

    @Autowired
    ReviewsVehiclesService vehiclesService;

    @Autowired
    ReviewsLicencesService licencesService;

    @Autowired
    PostReviewQueueBusiness postReviewQueueBusiness;

    @Autowired
    CosBusiness cosBusiness;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    public IPage<ReviewListVO> getList(PostReviewListDTO listDTO) {
        // 搜索符合条件的后审单
        IPage<PostReviews> defaultPage = postReviewsService.getDefaultPage(listDTO);
        List<PostReviews> postReviews = defaultPage.getRecords();
        if (CollectionUtils.isEmpty(postReviews)) {
            return new Page<>();
        }

        // 审核单号列表
        List<String> reviewSnList = postReviews.stream().map(PostReviews::getReviewSn).collect(Collectors.toList());

        PostReviewListBO listBO = new PostReviewListBO();
        listBO.setReviewSnList(reviewSnList);
        listBO.setPageSize(listDTO.getPageSize());
        listBO.setSort(listDTO.getSort());
        IPage<ReviewListVO> reviewList = reviewsService.getPostReviewByReviewSnList(listBO).convert(reviews -> {
            ReviewListVO listVO = BeanUtil.copyProperties(reviews, ReviewListVO.class);
            listVO.setIssuerName(deliveryConfig.getCnNameByIssuerId(reviews.getIssuerId()));
            return listVO;
        });
        reviewList.setTotal(defaultPage.getTotal());
        reviewList.setPages(defaultPage.getPages());
        reviewList.setCurrent(defaultPage.getCurrent());
        reviewList.setSize(defaultPage.getSize());
        return reviewList;
    }

    public List<PostReviewVO> batchReceive(PostReviewBatchReceiveDTO receiveDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        int totalCount = 3;
        List<PostReviewVO> list = new ArrayList<>();
        // 用户已领取待审队列
        List<String> reviewSnList = postReviewQueueBusiness.getUserPostReviewList(user.getUsername());
        if (!CollectionUtils.isEmpty(reviewSnList)) {
            reviewSnList.forEach(reviewSn -> {
                PostReviewVO postReviewVO = this.receivePostReviewData(reviewSn);
                if (ObjectUtil.isNotNull(postReviewVO)) {
                    list.add(postReviewVO);
                }
            });
        }

        // 待后审队列
        int remain = totalCount - list.size();
        for (int i = 0; i < remain; i++) {
            PostReviewVO postReviewVO = this.getOnePostReview(receiveDTO);
            if (ObjectUtil.isNull(postReviewVO)) {
                break;
            }
            list.add(postReviewVO);
        }
        return list;
    }

    public void batchAuditPass(PostReviewBatchAuditPassDTO batchAuditPassDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        List<Integer> postReviewIds = batchAuditPassDTO.getPostReviewIds();
        if (!CollectionUtils.isEmpty(postReviewIds)) {
            for (Integer id : postReviewIds) {
                PostReviews postReview = postReviewsService.getById(id);
                if (ObjectUtil.isNotNull(postReview)) {
                    // 已审核通过
                    if (postReview.getReviewStatus().equals(PostReviewStatusEnum.REVIEW_STATUS_PASS.getValue())) {
                        continue;
                    }
                    // 审核异常
                    if (postReview.getReviewStatus().equals(PostReviewStatusEnum.REVIEW_STATUS_EXCEPTION.getValue())) {
                        ToolsHelper.throwException("已有后审结果：审核异常");
                    }
                    // 非本人审核
                    if (!postReview.getOperator().equals(user.getUsername())) {
                        ToolsHelper.throwException("非本人领取，不能审核");
                    }
                    PostReviews updatePostReview = new PostReviews();
                    updatePostReview.setId(id);
                    updatePostReview.setReviewStatus(PostReviewStatusEnum.REVIEW_STATUS_PASS.getValue());
                    updatePostReview.setDrawTime(postReview.getDrawTime());
                    updatePostReview.setReviewTime(LocalDateTime.now());
                    updatePostReview.setUpdatedAt(LocalDateTime.now());
                    updatePostReview.updateById();

                    // 移除用户领取记录
                    postReviewQueueBusiness.removeUserPostReview(user.getUsername(), postReview.getReviewSn());

                    // 移除审核缓存数据
                    postReviewQueueBusiness.delReviewData(postReview.getReviewSn());

                    // 记录日志
                    Reviews reviews = reviewsService.getOneByReviewSn(postReview.getReviewSn());
                    if (ObjectUtil.isNotNull(reviews)) {
                        ReviewLogBO logBO = new ReviewLogBO();
                        logBO.setReviewId(reviews.getId());
                        logBO.setOperator(user.getUsername());
                        logBO.setType(ReviewLogTypeEnum.TYPE_REVIEW.getValue());
                        logBO.setOperateContent("【后审】审核通过");
                        reviewsLogService.addLog(logBO);
                    }
                }
            }
        }
    }

    public void abnormal(Integer id) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        PostReviews postReview = postReviewsService.getById(id);
        if (ObjectUtil.isNull(postReview)) {
            ToolsHelper.throwException("后审单不存在");
        }
        if (postReview.getReviewStatus().equals(PostReviewStatusEnum.REVIEW_STATUS_EXCEPTION.getValue())) {
            return;
        }
        if (postReview.getReviewStatus().equals(PostReviewStatusEnum.REVIEW_STATUS_PASS.getValue())) {
            ToolsHelper.throwException("已有后审结果：审核通过");
        }
        // 非本人审核
        if (!postReview.getOperator().equals(user.getUsername())) {
            ToolsHelper.throwException("非本人领取，不能审核");
        }

        // 更新状态
        PostReviews updatePostReview = new PostReviews();
        updatePostReview.setId(id);
        updatePostReview.setReviewStatus(PostReviewStatusEnum.REVIEW_STATUS_EXCEPTION.getValue());
        updatePostReview.setExceptionStatus(PostReviewExceptionStatusEnum.EXCEPTION_STATUS_WAIT.getValue());
        updatePostReview.setDrawTime(postReview.getDrawTime());
        updatePostReview.setReviewTime(LocalDateTime.now());
        updatePostReview.setUpdatedAt(LocalDateTime.now());
        updatePostReview.updateById();

        // 移除用户领取记录
        postReviewQueueBusiness.removeUserPostReview(user.getUsername(), postReview.getReviewSn());

        // 移除审核缓存数据
        postReviewQueueBusiness.delReviewData(postReview.getReviewSn());

        // 记录日志
        Reviews reviews = reviewsService.getOneByReviewSn(postReview.getReviewSn());
        if (ObjectUtil.isNotNull(reviews)) {
            ReviewLogBO logBO = new ReviewLogBO();
            logBO.setReviewId(reviews.getId());
            logBO.setOperator(user.getUsername());
            logBO.setType(ReviewLogTypeEnum.TYPE_REVIEW.getValue());
            logBO.setOperateContent("【后审】置为异常");
            reviewsLogService.addLog(logBO);
        }
    }

    public IPage<PostReviewAbnormalListVO> getAbnormalList(PostReviewAbnormalListDTO abnormalListDTO) {
        // 搜索符合条件的异常后审单
        IPage<PostReviews> abnormalPage = postReviewsService.getAbnormalPage(abnormalListDTO);
        List<PostReviews> postReviews = abnormalPage.getRecords();
        if (CollectionUtils.isEmpty(postReviews)) {
            return new Page<>();
        }

        // 审核单号列表
        List<String> reviewSnList = postReviews.stream().map(PostReviews::getReviewSn).collect(Collectors.toList());
        // 审核单号-后审单map
        Map<String, PostReviews> postReviewsMap = postReviews.stream().collect(Collectors.toMap(PostReviews::getReviewSn, Function.identity(), (v1, v2) -> v2));

        PostReviewListBO listBO = new PostReviewListBO();
        listBO.setReviewSnList(reviewSnList);
        listBO.setPageSize(abnormalListDTO.getPageSize());
        IPage<PostReviewAbnormalListVO> abnormalList = reviewsService.getPostReviewByReviewSnList(listBO).convert(reviews -> {
            PostReviewAbnormalListVO listVO = BeanUtil.copyProperties(reviews, PostReviewAbnormalListVO.class);
            PostReviews postReview = postReviewsMap.get(reviews.getReviewSn());
            listVO.setIssuerName(deliveryConfig.getCnNameByIssuerId(reviews.getIssuerId()));
            listVO.setPostReviewId(postReview.getId());
            listVO.setExceptionStatus(postReview.getExceptionStatus());
            listVO.setPostReviewRemark(postReview.getRemark());
            return listVO;
        });
        abnormalList.setTotal(abnormalPage.getTotal());
        abnormalList.setPages(abnormalPage.getPages());
        abnormalList.setSize(abnormalPage.getSize());
        abnormalList.setCurrent(abnormalPage.getCurrent());

        return abnormalList;
    }

    public void handleException(PostReviewHandleDTO handleDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        PostReviews postReview = postReviewsService.getById(handleDTO.getId());
        if (ObjectUtil.isNull(postReview)) {
            ToolsHelper.throwException("后审单不存在");
        }
        if (!postReview.getReviewStatus().equals(PostReviewStatusEnum.REVIEW_STATUS_EXCEPTION.getValue())) {
            ToolsHelper.throwException("后审单不是异常状态，无需处理");
        }
        if (postReview.getExceptionStatus().equals(PostReviewExceptionStatusEnum.EXCEPTION_STATUS_DONE.getValue())) {
            ToolsHelper.throwException("已处理，无需重复处理");
        }
        PostReviews updatePostReview = new PostReviews();
        updatePostReview.setId(handleDTO.getId());
        updatePostReview.setExceptionStatus(PostReviewExceptionStatusEnum.EXCEPTION_STATUS_DONE.getValue());
        updatePostReview.setDrawTime(postReview.getDrawTime());
        updatePostReview.setHandleTime(LocalDateTime.now());
        updatePostReview.setRemark(handleDTO.getRemark());
        updatePostReview.updateById();

        // 记录日志
        Reviews reviews = reviewsService.getOneByReviewSn(postReview.getReviewSn());
        if (ObjectUtil.isNotNull(reviews)) {
            ReviewLogBO logBO = new ReviewLogBO();
            logBO.setReviewId(reviews.getId());
            logBO.setOperator(user.getUsername());
            logBO.setType(ReviewLogTypeEnum.TYPE_HANDLE.getValue());
            logBO.setOperateContent("【后审】异常处理：" + handleDTO.getRemark());
            reviewsLogService.addLog(logBO);
        }
    }


    /**
     * 领取后审单
     * @param reviewSn 审核单号
     * @return 后审内容
     */
    private PostReviewVO receivePostReviewData(String reviewSn) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        // 加锁
        String lockKey = "review:postReview:" + reviewSn;
        if (!ToolsHelper.addLock(redisPermanentTemplate, lockKey, 5)) {
            ToolsHelper.throwException("后审单处理中");
        }
        Reviews reviews = reviewsService.getOneByReviewSn(reviewSn);
        if (ObjectUtil.isNull(reviews)) {
            ToolsHelper.throwException("审核记录不存在，审核单号：" + reviewSn);
        }
        PostReviews postReviews = postReviewsService.getOneByReviewSn(reviewSn);
        if (ObjectUtil.isNull(postReviews)) {
            ToolsHelper.throwException("后审记录不存在，审核单号：" + reviewSn);
        }
        try {
            // 判断后审单状态
            // 非本人审核中
            if (postReviews.getReviewStatus().equals(PostReviewStatusEnum.REVIEW_STATUS_PROCESSING.getValue())
                    && !postReviews.getOperator().equals(user.getUsername())
            ) {
                ToolsHelper.throwException("后审单非本人审核中，审核人：" + user.getUsername());
            }

            // 已审核
            if (Arrays.asList(
                    PostReviewStatusEnum.REVIEW_STATUS_PASS.getValue(),
                    PostReviewStatusEnum.REVIEW_STATUS_EXCEPTION.getValue()
            ).contains(postReviews.getReviewStatus())) {
                ToolsHelper.throwException("后审单已有审核结果:" + PostReviewStatusEnum.map.get(postReviews.getReviewStatus()));
            }

            // 更新领取人 审核状态 领取时间
            PostReviews updatePostReviews = new PostReviews();
            updatePostReviews.setId(postReviews.getId());
            updatePostReviews.setOperator(user.getUsername());
            updatePostReviews.setReviewStatus(PostReviewStatusEnum.REVIEW_STATUS_PROCESSING.getValue());
            updatePostReviews.setDrawTime(LocalDateTime.now());
            updatePostReviews.updateById();

            // 添加到用户领取记录
            List<String> userPostReviewList = postReviewQueueBusiness.getUserPostReviewList(user.getUsername());
            if (!userPostReviewList.contains(reviewSn)) {
                postReviewQueueBusiness.addUserPostReview(user.getUsername(), reviewSn);

                // 添加日志
                ReviewLogBO logBO = new ReviewLogBO();
                logBO.setReviewId(reviews.getId());
                logBO.setOperator(user.getUsername());
                logBO.setType(ReviewLogTypeEnum.TYPE_RECEIVE.getValue());
                logBO.setOperateContent("【后审】领取成功");
                reviewsLogService.addLog(logBO);
            }
        } catch (Throwable e) {
            log.error("【后审】领取失败：{}", e.getLocalizedMessage());
            // 移除用户领取记录
            postReviewQueueBusiness.removeUserPostReview(user.getUsername(), reviewSn);

            // 添加日志
            ReviewLogBO logBO = new ReviewLogBO();
            logBO.setReviewId(reviews.getId());
            logBO.setOperator(user.getUsername());
            logBO.setType(ReviewLogTypeEnum.TYPE_RECEIVE.getValue());
            String errMsg = e.getLocalizedMessage();
            if (errMsg.length() > 200) {
                errMsg = errMsg.substring(0, 200);
            }
            logBO.setOperateContent("【后审】领取失败：" + errMsg);
            reviewsLogService.addLog(logBO);
            return null;
        } finally {
            ToolsHelper.unLock(redisPermanentTemplate, lockKey);
        }

        // 获取后审数据
        PostReviewVO postReviewVO = this.getPostReviewData(reviews);
        this.getPostReviewCosImg(postReviewVO);
        postReviewVO.setPostReviewId(postReviews.getId());
        return postReviewVO;
    }

    /**
     * 从队列获取后审单
     * @return 后审内容
     */
    private PostReviewVO getOnePostReview(PostReviewBatchReceiveDTO receiveDTO) {
        PostReviewVO postReviewVO = null;
        String reviewSn = postReviewQueueBusiness.popFromPostReviewQueueList(receiveDTO.getSort().equals("asc"), receiveDTO.getEmergencyType());
        if (StringUtils.isEmpty(reviewSn)) {
            // 休眠100毫秒以防更新事务未完成
            try {
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (InterruptedException e) {
                log.error("sleep error {}", e.getLocalizedMessage());
            }
            // 搜索数据库
            PostReviewListBO listBO = new PostReviewListBO();
            listBO.setEmergencyType(receiveDTO.getEmergencyType());
            List<PostReviews> postReviews = postReviewsService.getDefaultListLimitDate(listBO);

            // 重新入队列
            if (!CollectionUtils.isEmpty(postReviews)) {
                postReviews.forEach(postReview ->
                        postReviewQueueBusiness.pushToPostReviewQueueList(postReview.getReviewSn(), postReview.getEmergencyType()));
                reviewSn = postReviewQueueBusiness.popFromPostReviewQueueList(receiveDTO.getSort().equals("asc"), receiveDTO.getEmergencyType());
            }
        }

        if (StringUtils.isNotEmpty(reviewSn)) {
            postReviewVO = this.receivePostReviewData(reviewSn);
        }
        return postReviewVO;
    }

    /**
     * 获取后审数据
     * @param reviews 审核单
     * @return 后审内容
     */
    public PostReviewVO getPostReviewData(Reviews reviews) {
        PostReviewVO postReviewVO = new PostReviewVO();
        postReviewVO.setId(reviews.getId());
        postReviewVO.setIssuerName(deliveryConfig.getCnNameByIssuerId(reviews.getIssuerId()));
        postReviewVO.setOrderSn(reviews.getOrderSn());

        ReviewsIdcards idCards = null;
        ReviewsVehicles vehicles = null;
        ReviewsLicences licences = null;
        ReviewDataBO bo = postReviewQueueBusiness.getReviewData(reviews.getReviewSn());
        if (ObjectUtil.isNotNull(bo)) {
            // 从缓存中获取
            idCards = bo.getReviewsIdcards();
            if (ObjectUtil.isNull(idCards)) {
                idCards = idCardsService.getOneByReviewSn(reviews.getReviewSn());
            }
            vehicles = bo.getReviewsVehicles();
            if (ObjectUtil.isNull(vehicles)) {
                vehicles = vehiclesService.getOneByReviewSn(reviews.getReviewSn());
            }
            licences = bo.getReviewsLicences();
            if (Objects.equals(reviews.getVehicleBelong(), VehicleBelongEnum.COMPANY.getValue())
                    && ObjectUtil.isNull(licences)
            ) {
                licences = licencesService.getOneByReviewSn(reviews.getReviewSn());
            }
        }

        ReviewDataBO reviewDataBO = new ReviewDataBO();
        reviewDataBO.setReviews(reviews);
        // 获取身份证
        if (ObjectUtil.isNotNull(idCards)) {
            postReviewVO.setIdCardFrontUrl(idCards.getFrontImgUrl());
            reviewDataBO.setReviewsIdcards(idCards);
        }

        // 获取行驶证
        if (ObjectUtil.isNotNull(vehicles)) {
            postReviewVO.setVehicleFrontUrl(vehicles.getFrontImgUrl());
            postReviewVO.setOwner(vehicles.getOwner());
            postReviewVO.setPlateNo(vehicles.getPlateNo());
            reviewDataBO.setReviewsVehicles(vehicles);
        }

        // 公司车需要获取营业执照
        if (Objects.equals(reviews.getVehicleBelong(), VehicleBelongEnum.COMPANY.getValue())
                && ObjectUtil.isNotNull(licences)
        ) {
            postReviewVO.setBizLicenseUrl(licences.getWmImgUrl());
            reviewDataBO.setReviewsLicences(licences);
        }

        // 缓存数据
        postReviewQueueBusiness.setReviewData(reviews.getReviewSn(), reviewDataBO);
        return postReviewVO;
    }

    /**
     * 获取后审cos图片
     * @param postReviewVO 后审数据
     */
    private void getPostReviewCosImg(PostReviewVO postReviewVO) {
        if (postReviewVO.getIdCardFrontUrl() != null) {
            String url = cosBusiness.getAccessUrl(postReviewVO.getIdCardFrontUrl());
            if (url != null) {
                postReviewVO.setIdCardFrontUrl(cosBusiness.getThumbnail(url, 3, 600, 300));
            }
        }
        if (postReviewVO.getVehicleFrontUrl() != null) {
            String url = cosBusiness.getAccessUrl(postReviewVO.getVehicleFrontUrl());
            if (url != null) {
                postReviewVO.setVehicleFrontUrl(cosBusiness.getThumbnail(url, 3, 600, 300));
            }
        }
        if (postReviewVO.getBizLicenseUrl() != null) {
            String url = cosBusiness.getAccessUrl(postReviewVO.getBizLicenseUrl());
            if (url != null) {
                postReviewVO.setBizLicenseUrl(cosBusiness.getThumbnail(url, 3, 600, 300));
            }
        }
    }
}
