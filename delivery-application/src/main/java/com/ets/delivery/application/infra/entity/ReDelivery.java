package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 补发货表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_re_delivery")
public class ReDelivery extends BaseEntity<ReDelivery> {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 补发货单
     */
    private String deliverySn;

    /**
     * 发货单编号
     */
    private String logisticsSn;

    /**
     * 申办订单号
     */
    private String applyOrderSn;

    /**
     * 类型[1:补办,2:维修换货,3:延保权益-只换不修,4:补发（丢件）,5:补发（历史发货失败）,6:地推]
     */
    private Integer type;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌号(加密)
     */
    private String plateNoCipher;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 发卡方id
     */
    private Integer issuerId;

    /**
     * 发货内容
     */
    private String contentType;

    /**
     * 收件人姓名
     */
    private String receiverNamePlain;

    /**
     * 收件人姓名(加密)
     */
    private String receiverName;

    /**
     * 收件地区
     */
    private String address;

    /**
     * 收货详细地址
     */
    private String addressDetailPlain;

    /**
     * 收货详细地址(加密)
     */
    private String addressDetail;

    /**
     * 收件手机号
     */
    private String phonePlain;

    /**
     * 收件手机号(加密)
     */
    private String phone;

    /**
     * 关联工单号
     */
    private String workOrderNo;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 设备号段
     */
    private String obuNoRegion;

    /**
     * 卡号号段
     */
    private String cardNoRegion;

    /**
     * 设备商
     */
    private Integer manufacturer;

    /**
     * 设备类型(废弃)
     */
    private Integer deviceType;

    /**
     * 发货套数
     */
    private Integer goodsCount;

    /**
     * 创建人
     */
    private String operator;

    /**
     * 状态[0:已保存,1:已推单,2:已发货,3:已完成,9:已取消]
     */
    private Integer status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;


}
