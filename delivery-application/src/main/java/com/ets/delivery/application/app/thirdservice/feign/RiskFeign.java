package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.fallback.RiskFallbackFactory;
import com.ets.delivery.application.app.thirdservice.request.risk.RiskCreateDTO;
import com.ets.delivery.application.app.thirdservice.request.risk.RiskRuleQueryDTO;
import com.ets.delivery.application.app.thirdservice.response.RiskRuleQueryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
        url = "http://delivery-application:20130",
        name = "RiskFeign",
        fallbackFactory = RiskFallbackFactory.class
)
public interface RiskFeign {

    @PostMapping("/risk/accept")
    JsonResult<Boolean> accept(@RequestBody RiskCreateDTO dto);

    @PostMapping("/risk/getRuleByParams")
    JsonResult<List<RiskRuleQueryVO>> getRuleByParams(@RequestBody RiskRuleQueryDTO queryDTO);
}
