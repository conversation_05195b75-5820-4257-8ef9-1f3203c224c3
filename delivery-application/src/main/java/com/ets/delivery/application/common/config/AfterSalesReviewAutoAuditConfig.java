package com.ets.delivery.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 售后审核自动审核开关配置
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "after-sales-review-auto-audit")
public class AfterSalesReviewAutoAuditConfig {

    /**
     * 售后审核自动审核总开关 0-关闭 1-开启
     * 总开关关闭时，所有审核单直接自动审核通过
     */
    private Integer totalSwitch = 1;

    /**
     * 售后审核自动审核卡方维度开关配置
     * key: 发卡方ID, value: 0-关闭 1-开启
     * 卡方开关关闭时，该发卡方的审核单直接自动审核通过
     * 未配置的发卡方默认执行数据比对逻辑
     */
    private Map<Integer, Integer> issuerSwitch;

    /**
     * 检查是否应该直接自动审核通过
     * @param issuerId 发卡方ID
     * @return true-直接通过, false-执行数据比对
     */
    public boolean shouldAutoPass(Integer issuerId) {
        // 检查总开关
        if (totalSwitch == null || totalSwitch == 0) {
            return true; // 总开关关闭，直接通过
        }
        
        // 检查卡方维度开关
        if (issuerSwitch != null && issuerId != null) {
            Integer issuerSwitchValue = issuerSwitch.get(issuerId);
            if (issuerSwitchValue == null) {
                return true;
            }
            return issuerSwitchValue == 0; // 对应卡方开关关闭，直接通过
        }
        
        return false; // 开关都开启，需要执行数据比对
    }

    /**
     * 获取自动审核通过的原因描述
     * @param issuerId 发卡方ID
     * @return 原因描述
     */
    public String getAutoPassReason(Integer issuerId) {
        // 检查总开关
        if (totalSwitch == null || totalSwitch == 0) {
            return "总开关关闭，自动审核通过";
        }
        
        // 检查卡方维度开关
        if (issuerSwitch != null && issuerId != null) {
            Integer issuerSwitchValue = issuerSwitch.get(issuerId);
            if (issuerSwitchValue == null) {
                return "发卡方[" + issuerId + "]未配置开关，自动审核通过";
            }
            if (issuerSwitchValue == 0) {
                return "发卡方[" + issuerId + "]开关关闭，自动审核通过";
            }
        }
        
        return "开关配置自动审核通过";
    }
}
