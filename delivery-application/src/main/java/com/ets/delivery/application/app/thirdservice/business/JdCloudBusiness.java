package com.ets.delivery.application.app.thirdservice.business;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.thirdservice.feign.JdApiFeign;
import com.ets.delivery.application.app.thirdservice.request.jd.*;
import com.ets.delivery.application.app.thirdservice.response.jd.*;
import com.ets.delivery.application.common.config.jd.JdExpressConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Component
public class JdCloudBusiness {

    @Autowired
    private JdExpressConfig jdConfig;

    @Autowired
    private JdApiFeign jdApiFeign;

    public JdAddOrderVO addOrder(JdAddOrderDTO addOrderDTO) {
        String method = "jingdong.eclp.order.addOrder";
        String result = sendRequest(addOrderDTO, method);
        JdAddOrderVO addOrderVO = JSON.parseObject(result, JdAddOrderVO.class);
        checkErrorResponse(addOrderVO);

        return addOrderVO;
    }

    public JdQueryOrderVO queryOrder(JdQueryOrderDTO queryOrderDTO) {
        String method = "jingdong.eclp.order.queryOrder";
        String result = sendRequest(queryOrderDTO, method);
        JdQueryOrderVO queryOrderVO = JSON.parseObject(result, JdQueryOrderVO.class);
        checkErrorResponse(queryOrderVO);

        return queryOrderVO;
    }

    public JdCancelOrderVO cancelOrder(JdCancelOrderDTO cancelOrderDTO) {
        String method = "jingdong.eclp.order.cancelOrder";
        String result = sendRequest(cancelOrderDTO, method);
        JdCancelOrderVO cancelOrderVO = JSON.parseObject(result, JdCancelOrderVO.class);
        checkErrorResponse(cancelOrderVO);

        return cancelOrderVO;
    }

    public JdExpressTraceGetVO traceGet(JdExpressTraceGetDTO traceGetDTO) {
        String method = "jingdong.ldop.receive.trace.get";
        String result = sendRequest(traceGetDTO, method);
        JdExpressTraceGetVO traceGetVO = JSON.parseObject(result, JdExpressTraceGetVO.class);
        checkErrorResponse(traceGetVO);

        return traceGetVO;
    }

    public JdQueryGoodsInfoVO queryGoodsInfo(JdQueryGoodsInfoDTO queryGoodsInfoDTO) {
        String method = "jingdong.eclp.goods.queryGoodsInfo";
        String result = sendRequest(queryGoodsInfoDTO, method);
        JdQueryGoodsInfoVO queryGoodsInfoVO = JSON.parseObject(result, JdQueryGoodsInfoVO.class);
        checkErrorResponse(queryGoodsInfoVO);

        return queryGoodsInfoVO;
    }

    public JdQueryWarehouseVO queryWarehouse(JdQueryWarehouseDTO queryWarehouseDTO) {
        String method = "jingdong.eclp.master.queryWarehouse";
        String result = sendRequest(queryWarehouseDTO, method);
        JdQueryWarehouseVO queryWarehouseVO = JSON.parseObject(result, JdQueryWarehouseVO.class);
        checkErrorResponse(queryWarehouseVO);

        return queryWarehouseVO;
    }

    public JdQueryStockVO queryStock(JdQueryStockDTO queryStockDTO) {
        String method = "jingdong.eclp.stock.queryStock";
        String result = sendRequest(queryStockDTO, method);
        JdQueryStockVO queryStockVO = JSON.parseObject(result, JdQueryStockVO.class);
        checkErrorResponse(queryStockVO);

        return queryStockVO;
    }

    private String sendRequest(Object requestDTO, String method) {
        try {
            String params = JSON.toJSONString(requestDTO);
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("360buy_param_json", params);
            log.info("【京东发货】【{}】请求参数：{}", method, body);

            String timestamp = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
            String sign = getSign(method, timestamp, params);
            log.info("【京东发货】【{}】签名：{}", method, sign);

            // 发起请求
            String result = jdApiFeign.RouterJson(body,
                    method,
                    jdConfig.getAppKey(),
                    jdConfig.getAccessToken(),
                    timestamp,
                    jdConfig.getV(),
                    sign);
            log.info("【京东发货】【{}】响应结果：{}", method, result);

            return result;
        } catch (Exception e) {
            log.error("【京东发货】请求异常：{}", e.getMessage(), e);
            ToolsHelper.throwException("京东发货请求异常");
        }
        return null;
    }

    private String getSign(String method, String timestamp, String paramJson) {
        String content = String.join("", new String[]{
                jdConfig.getAppSecret(),
                "360buy_param_json", paramJson,
                "access_token", jdConfig.getAccessToken(),
                "app_key", jdConfig.getAppKey(),
                "method", method,
                "timestamp", timestamp,
                "v", jdConfig.getV(),
                jdConfig.getAppSecret()});
        return SecureUtil.md5(content).toUpperCase();
    }

    private void checkErrorResponse(JdErrorResponseVO errorResponseVO) {
        if (ObjectUtils.isEmpty(errorResponseVO)) {
            log.error("【京东发货】下单失败：返回结果为空");
            ToolsHelper.throwException("京东下单失败：返回结果为空");
        }

        if (ObjectUtils.isNotEmpty(errorResponseVO.getErrorResponse())) {
            log.error("【京东发货】下单失败，错误码：【{}】 错误信息：{}", errorResponseVO.getErrorResponse().getCode(), errorResponseVO.getErrorResponse().getZhDesc());
            ToolsHelper.throwException("京东下单失败，错误码：【" + errorResponseVO.getErrorResponse().getCode() + "】 错误信息："
                    + errorResponseVO.getErrorResponse().getZhDesc());
        }
    }
}
