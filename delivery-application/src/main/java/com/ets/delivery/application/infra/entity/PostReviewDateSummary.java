package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 待后审统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_post_review_date_summary")
public class PostReviewDateSummary extends BaseEntity<PostReviewDateSummary> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 统计日期
     */
    private LocalDate summaryDate;

    /**
     * 3-紧急后审 2-优先后审 1-正常后审
     */
    private Integer emergencyType;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
