package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.ReviewsIdcards;
import com.ets.delivery.application.infra.mapper.ReviewsIdcardsMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 审核身份证数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
@DS("db-issuer-admin-proxy")
public class ReviewsIdcardsService extends BaseService<ReviewsIdcardsMapper, ReviewsIdcards> {

    public ReviewsIdcards getOneByReviewSn(String reviewSn) {
        Wrapper<ReviewsIdcards> wrapper = Wrappers.<ReviewsIdcards>lambdaQuery()
                .eq(ReviewsIdcards::getReviewSn, reviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
