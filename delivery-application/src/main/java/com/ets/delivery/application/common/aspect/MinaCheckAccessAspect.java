package com.ets.delivery.application.common.aspect;

import com.ets.delivery.application.app.business.storageMina.AuthBusiness;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@SuppressWarnings({"unused"})
@Slf4j
@Aspect
public class MinaCheckAccessAspect {

    @Autowired
    private AuthBusiness authBusiness;

    @Pointcut("@within(com.ets.delivery.application.common.annotation.MinaCheckAccessAnnotation) " +
            "|| @annotation(com.ets.delivery.application.common.annotation.MinaCheckAccessAnnotation)")
    public void annotationPointcut() {
    }

    // 此处进入到方法前  可以实现一些业务逻辑
    @Before(value = "annotationPointcut()")
    public void beforePointcut(JoinPoint joinPoint) {
        authBusiness.checkAccess();
    }

}
