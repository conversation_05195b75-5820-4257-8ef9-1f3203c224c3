package com.ets.delivery.application.common.dto.storage;

import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.storageRecord.GoodsTypeEnum;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;

@Data
public class StorageRecordAdminEditDTO {

    @NotNull(message = "id不能为空")
    private Integer id;

    /**
     * 仓储
     */
    @NotBlank(message = "仓库编号不能为空")
    private String storageCode;

    /**
     * 快递单号
     */
    @NotBlank(message = "快递单号不能为空")
    private String expressNumber;

    /**
     * 货物图片
     */
    @NotBlank(message = "图片地址不能为空")
    private String goodsImages;

    /**
     * 寄回数量
     */
    @NotNull(message = "寄回数量不能为空")
    private Integer nums;

    /**
     * 0-默认, 1-ETC卡+OBU, 2-ETC单卡, 3-ETC单OBU, 4-ETC设备破损, 5-非高灯设备或非设备, 6-单片式设备
     */
    @NotNull(message = "请选择货物类型")
    private Integer goodsType;

    @Override
    public String toString() {
        return "[仓库编号：" + StorageCodeEnum.map.getOrDefault(storageCode, storageCode) + "，" +
                "快递单号：" + expressNumber + "," +
                "货物类型：" + GoodsTypeEnum.map.getOrDefault(goodsType, "-") + "，" +
                "寄回数量：" + nums + "，" +
                "图片地址：" + goodsImages +
                "]";
    }
}
