package com.ets.delivery.application.app.job;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.delivery.application.app.business.PickUpBusiness;
import com.ets.delivery.application.common.consts.pickUp.PickUpOrderStatusEnum;
import com.ets.delivery.application.common.consts.pickUp.PickUpStatusEnum;
import com.ets.delivery.application.common.dto.pickUp.PickUpListDTO;
import com.ets.delivery.application.common.vo.pickUp.PickUpListVO;
import com.ets.delivery.application.infra.entity.Pickup;
import com.ets.delivery.application.infra.service.PickupService;
import com.ets.delivery.feign.request.pickup.PickUpCancelDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Slf4j
@Component
public class PickUpJob {

    @Autowired
    private PickUpBusiness pickUpBusiness;

    @Autowired
    private PickupService pickupService;

    @XxlJob("pickUpAutoCancelHandler")
    public ReturnT<String> pickUpAutoCancelHandler(String params) {

        PickUpListDTO listDTO = new PickUpListDTO();
        listDTO.setOrderStatus(PickUpOrderStatusEnum.STATUS_NORMAL.getValue());
        IPage<PickUpListVO> list = pickUpBusiness.getList(listDTO);

        log.info("取件单总条数：{}", list.getRecords().size());
        list.getRecords().forEach(pickup -> {
            try {
                PickUpCancelDTO cancelDTO = new PickUpCancelDTO();
                cancelDTO.setPickupSn(pickup.getPickupSn());
                pickUpBusiness.cancelPickUpOrder(cancelDTO);
                log.info("取件单：{}，取消成功", pickup.getPickupSn());
            } catch (Throwable e) {
                log.error("取件单：{}，取消失败：{}", pickup.getPickupSn(), e.getLocalizedMessage());
            }
        });

        return ReturnT.SUCCESS;
    }

    @XxlJob("pickUpQueryTraceInfoHandler")
    public ReturnT<String> pickUpQueryTraceInfoHandler(String params) {
        Integer pickupStatus = Integer.valueOf(params);
        if (!Arrays.asList(
                PickUpStatusEnum.STATUS_CREATE_SUCCESS.getValue(),
                PickUpStatusEnum.STATUS_WAIT_PICKUP.getValue()
        ).contains(pickupStatus)) {
            log.error("取件状态：{}，不正确", pickupStatus);
            return ReturnT.FAIL;
        }

        PickUpListDTO listDTO = new PickUpListDTO();
        listDTO.setOrderStatus(PickUpOrderStatusEnum.STATUS_NORMAL.getValue());
        listDTO.setPickupStatus(pickupStatus);
        listDTO.setPageSize(100);
        IPage<PickUpListVO> list = pickUpBusiness.getList(listDTO);

        list.getRecords().forEach(pickUpVO -> {
            Pickup pickUp = pickupService.getOneByPickUpSn(pickUpVO.getPickupSn());
            pickUpBusiness.queryTraceInfo(pickUp);
        });

        return ReturnT.SUCCESS;
    }
}
