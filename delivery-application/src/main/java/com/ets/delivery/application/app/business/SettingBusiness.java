package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.common.bo.SettingLogBO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.consts.SettingCacheKey;
import com.ets.delivery.application.common.consts.SettingLogTypeEnum;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.setting.SettingStatusEnum;
import com.ets.delivery.application.common.dto.CategoryMapDTO;
import com.ets.delivery.application.common.dto.setting.SettingAddDTO;
import com.ets.delivery.application.common.dto.setting.SettingChangeStatusDTO;
import com.ets.delivery.application.common.dto.setting.SettingEditDTO;
import com.ets.delivery.application.common.dto.setting.SettingListDTO;
import com.ets.delivery.application.common.vo.setting.SettingReviewListVO;
import com.ets.delivery.application.infra.entity.Setting;
import com.ets.delivery.application.infra.entity.User;
import com.ets.delivery.application.infra.service.SettingLogService;
import com.ets.delivery.application.infra.service.SettingService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Locale;
import java.util.Map;

@Component
public class SettingBusiness {

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Autowired
    private DeliveryConfig deliveryConfig;

    @Autowired
    protected SettingService settingService;

    @Autowired
    private SettingLogService settingLogService;

    @Resource(name = "defaultRedisTemplate")
    StringRedisTemplate redisTemplate;

    public Map<String, CategoryMapDTO> getCategory() {
        return deliveryConfig.getCategoryMap();
    }

    public IPage<SettingReviewListVO> getList(SettingListDTO listDTO) {
        return settingService.getPageByCategoryKey(listDTO).convert(
                setting -> BeanUtil.copyProperties(setting, SettingReviewListVO.class)
        );
    }

    public void addSetting(SettingAddDTO addDTO) {
        checkSetting(addDTO);

        // 新增记录
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);

        Setting setting = new Setting();
        BeanUtil.copyProperties(addDTO, setting);
        setting.setOperator(user.getUsername());
        setting.setIssuerId(0);
        setting.setStatus(SettingStatusEnum.STATUS_NORMAL.getValue());
        setting.setCreatedAt(LocalDateTime.now());
        setting.setUpdatedAt(LocalDateTime.now());
        settingService.save(setting);

        // 记录日志
        SettingLogBO logBO = new SettingLogBO();
        logBO.setSettingId(setting.getId());
        logBO.setType(SettingLogTypeEnum.CREATE.getValue());
        logBO.setOperator(user.getUsername());
        logBO.setOperateContent(user.getUsername() + SettingLogTypeEnum.CREATE.getDesc() + JSON.toJSONString(addDTO));
        settingLogService.addLog(logBO);

        // 清除缓存
        clearCache(setting.getCategory(), setting.getKey());
    }

    protected void checkSetting(SettingAddDTO addDTO) {
        if (!addDTO.checkCategory()) {
            ToolsHelper.throwException("category分类不正确");
        }
        if (!addDTO.checkKey()) {
            ToolsHelper.throwException("key分类不正确");
        }

        if (!addDTO.checkGuide()) {
            ToolsHelper.throwException("guide分类不正确");
        }

        // 检查是否已存在
        Setting settingExist = settingService.getOneByCategoryKeyValue(addDTO.getCategory(), addDTO.getKey(), addDTO.getValue());
        if (ObjectUtil.isNotNull(settingExist)) {
            ToolsHelper.throwException("该参数已存在");
        }
    }

    public void editSetting(SettingEditDTO editDTO) {
        Setting setting = settingService.getById(editDTO.getId());
        if (ObjectUtil.isNull(setting)) {
            ToolsHelper.throwException("配置不存在");
        }

        // 修改记录
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);

        if (ObjectUtils.isEmpty(editDTO.getCategory())) {
            editDTO.setCategory(setting.getCategory());
        }
        if (ObjectUtils.isEmpty(editDTO.getParams())) {
            editDTO.setParams(setting.getParams());
        }
        BeanUtil.copyProperties(editDTO, setting);
        setting.setUpdatedAt(LocalDateTime.now());
        settingService.updateById(setting);

        // 记录日志
        SettingLogBO logBO = new SettingLogBO();
        logBO.setSettingId(setting.getId());
        logBO.setType(SettingLogTypeEnum.UPDATE.getValue());
        logBO.setOperator(user.getUsername());
        logBO.setOperateContent(user.getUsername() + SettingLogTypeEnum.UPDATE.getDesc() + JSON.toJSONString(editDTO));
        settingLogService.addLog(logBO);

        // 清除缓存
        clearCache(setting.getCategory(), setting.getKey());
    }

    public void changeStatus(SettingChangeStatusDTO changeStatusDTO) {
        if (!changeStatusDTO.checkStatus()) {
            ToolsHelper.throwException("状态不正确");
        }

        Setting setting = settingService.getById(changeStatusDTO.getId());
        if (ObjectUtil.isNull(setting)) {
            ToolsHelper.throwException("配置不存在");
        }

        if (setting.getStatus().equals(changeStatusDTO.getStatus())) {
            return;
        }

        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        // 修改状态
        setting.setStatus(changeStatusDTO.getStatus());
        setting.setUpdatedAt(LocalDateTime.now());
        settingService.updateById(setting);

        // 记录日志
        SettingLogBO logBO = new SettingLogBO();
        logBO.setSettingId(setting.getId());
        logBO.setType(SettingLogTypeEnum.UPDATE.getValue());
        logBO.setOperator(user.getUsername());
        logBO.setOperateContent(user.getUsername() + "修改状态为：" + SettingStatusEnum.map.get(changeStatusDTO.getStatus()));
        settingLogService.addLog(logBO);

        // 清除缓存
        clearCache(setting.getCategory(), setting.getKey());
    }

    // 环境变量 + MD5小写 兼容yii缓存的key
    private void clearCache(String category, String key) {
        String cacheKey = ACTIVE + SecureUtil.md5(SettingCacheKey.getCategoryKeyCacheKey(category, key)).toLowerCase(Locale.ROOT);
        redisTemplate.delete(cacheKey);

        String guideCacheKey = ACTIVE + SecureUtil.md5(SettingCacheKey.getCategoryKeyWithGuideCacheKey(category, key)).toLowerCase(Locale.ROOT);
        redisTemplate.delete(guideCacheKey);

        String truckCacheKey = ACTIVE + SecureUtil.md5(SettingCacheKey.getTruckRejectReasonCacheKey(category)).toLowerCase(Locale.ROOT);
        redisTemplate.delete(truckCacheKey);
    }
}
