package com.ets.delivery.application.app.thirdservice.request.jd;

import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class JdQueryEstimatedFreightsDTO {
    /**
     * 发货地详，请填写详细地址或完整地址(长度不超过200个字符)
     */
    String senderAddress;

    /**
     * 寄托物（货物）重量，单位：千克
     */
    String weight;

    /**
     * 收货地址，请填写详细地址或完整地址(长度不超过200个字符)
     */
    String receiverAddress;

    /**
     * 业务类型（0:下单时间，1:上门揽收时间 ；默认下单时间）
     */
    Integer businessType = 0;
}
