package com.ets.delivery.application.app.thirdservice.request.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class YundaInventoryQueryXmlDTO implements Serializable {

    @XmlElementWrapper(name = "criteriaList")
    @XmlElement(name = "criteria")
    private List<criteria> criteria;

    @Data
    public static class criteria {

        /**
         * 仓库编码, string (50)
         */
        private String warehouseCode;

        /**
         * 货主编码，string（50）
         */
        private String ownerCode;

        /**
         * 商品编码，string（50）
         */
        private String itemCode;

        /**
         * 仓储系统商品ID, string (50)
         */
        private String itemId;

        /**
         * 库存类型，string (50) , ZP=正品, CC=残次,JS=机损, XS= 箱损, ZT=在途库存，默认为查所有类型的库存
         */
        private String inventoryType;
    }
}
