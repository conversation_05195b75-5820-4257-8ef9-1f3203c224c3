package com.ets.delivery.application.app.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.delivery.application.app.business.StorageMapAddressBusiness;
import com.ets.delivery.application.common.vo.addressMap.AddressMapInfoVO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class StorageMapJob {

    @Autowired
    private StorageMapAddressBusiness storageMapAddressBusiness;

    @XxlJob("storageMapAddressCheckHandler")
    public ReturnT<String> storageMa1pAddressCheck(String params) {
        log.info("storageMapAddressCheckHandler params:{}", params);
        JSONObject jsonObject = JSON.parseObject(params);

        String area = jsonObject.getString("area");
        Integer configId = jsonObject.getInteger("configId");

        AddressMapInfoVO addressMapInfoVO  = storageMapAddressBusiness.addressMapStorageCode(area, configId);
        log.info("地址：{} 规则id：{} 仓储编码：{}", area, configId, addressMapInfoVO);

        return ReturnT.SUCCESS;
    }
}
