package com.ets.delivery.application.common.consts.yunda;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum YundaLogiticsCodeEnum {

    SF("SF", "顺丰"),
    EMS("EMS", "标准快递"),
    EYB("EYB", "经济快件"),
    ZJS("ZJS", "宅急送"),
    YTO("YTO", "圆通"),
    ZTO("ZTO", "中通(ZTO)"),
    HTKY("HTKY", "百世汇通"),
    BSKY("BSKY", "百世快运"),
    UC("UC", "优速"),
    STO("STO", "申通"),
    TTKDEX("TTKDEX", "天天快递"),
    QFKD("QFKD", "全峰"),
    FAST("FAST", "快捷"),
    POSTB("POSTB",  "邮政小包"),
    GTO("GTO", "国通"),
    YUNDA("YUNDA", "韵达"),
    JD("JD", "京东配送"),
    DD("DD", "当当宅配"),
    AMAZON("AMAZON", "亚马逊物流"),
    DBWL("DBWL", "德邦物流"),
    DBKD("DBKD", "德邦快递"),
    DBKY("DBKY", "德邦快运"),
    RRS("RRS", "日日顺"),
    OTHER("OTHER", "其他");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;

    static {
        map = Arrays.stream(YundaLogiticsCodeEnum.values()).collect(Collectors.toMap(YundaLogiticsCodeEnum::getValue, YundaLogiticsCodeEnum::getDesc));
        list = Stream.of(YundaLogiticsCodeEnum.values())
                .map(YundaLogiticsCodeEnum::getValue)
                .collect(Collectors.toList());
    }
}
