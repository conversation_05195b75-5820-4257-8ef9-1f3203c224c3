package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 配置映射发布缓存表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cache_map")
public class CacheMap extends BaseEntity<CacheMap> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型[preview-预发布 prod-生产]
     */
    private String cacheType;

    /**
     * 缓存主key
     */
    private String cacheMainKey;

    /**
     * 缓存key
     */
    private String cacheKey;

    /**
     * 缓存值
     */
    private String cacheValue;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
