package com.ets.delivery.application.app.factory.importFile.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcel;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.excellistener.ManualLogisticsListener;
import com.ets.delivery.application.common.bo.logistics.LogisticsOneBO;
import com.ets.delivery.application.common.consts.importFile.ImportFileUploadStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.manualLogistics.ManualLogisticsReasonEnum;
import com.ets.delivery.application.common.consts.yunda.YundaLogiticsCodeEnum;
import com.ets.delivery.application.common.dto.manualLogistics.ManualLogisticsImportDTO;
import com.ets.delivery.application.infra.entity.ImportFileRecord;
import com.ets.delivery.application.infra.entity.ImportManualLogisticsDetail;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.entity.StorageSkuMap;
import com.ets.delivery.application.infra.service.ImportFileRecordService;
import com.ets.delivery.application.infra.service.ImportManualLogisticsDetailService;
import com.ets.delivery.application.infra.service.LogisticsService;
import com.ets.delivery.application.infra.service.StorageSkuMapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class ImportFileLogistics extends ImportFileBase<ImportManualLogisticsDetail> {

    @Autowired
    private ImportFileRecordService importFileRecordService;

    @Autowired
    private ImportManualLogisticsDetailService importManualLogisticsDetailService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private StorageSkuMapService storageSkuMapService;

    @Override
    public void importFile(MultipartFile file, ImportFileRecord fileRecord) {
        try {
            EasyExcel.read(file.getInputStream(),
                            ManualLogisticsImportDTO.class,
                            new ManualLogisticsListener(fileRecord.getBatchNo(), this))
                    .sheet()
                    .headRowNumber(2)
                    .doRead();
        } catch (IOException e) {
            log.error("导入文件失败：{}", e.getLocalizedMessage());
            // 更新导入失败
            fileRecord.setUploadStatus(ImportFileUploadStatusEnum.FAIL.getValue());
            fileRecord.setErrorMsg("导入文件失败");
            fileRecord.setUpdatedAt(LocalDateTime.now());
            importFileRecordService.updateById(fileRecord);
            ToolsHelper.throwException("导入文件失败");
        } catch (Throwable e) {
            fileRecord.setUploadStatus(ImportFileUploadStatusEnum.FAIL.getValue());
            fileRecord.setErrorMsg(e.getLocalizedMessage());
            fileRecord.setUpdatedAt(LocalDateTime.now());
            importFileRecordService.updateById(fileRecord);
            ToolsHelper.throwException(e.getLocalizedMessage());
        }
    }

    @Override
    public void saveImportFileData(String batchNo, List<ImportManualLogisticsDetail> dataList) {
        // 插入数据
        importManualLogisticsDetailService.saveBatch(dataList);

        // 更新导入结果
        ImportFileRecord importFileRecord = importFileRecordService.getByBatchNo(batchNo);
        importFileRecord.setTotal(dataList.size());
        importFileRecord.setUploadStatus(ImportFileUploadStatusEnum.SUCCESS.getValue());
        importFileRecord.setUpdatedAt(LocalDateTime.now());
        importFileRecordService.updateById(importFileRecord);
    }


    public ImportManualLogisticsDetail fillData(ImportManualLogisticsDetail detail) {
        // 车牌号
        if (StringUtils.isNotEmpty(detail.getOriginLogisticsSn())) {
            Logistics logistics = logisticsService.getByLogisticsSn(detail.getOriginLogisticsSn());
            if (ObjectUtils.isNotEmpty(logistics)) {
                detail.setPlateNo(logistics.getPlateNo());
            }
        }

        // 商品名称
        if (StringUtils.isNotEmpty(detail.getSku())) {
            StorageSkuMap skuMap = storageSkuMapService.getOneByStorageCodeSku(detail.getStorageCode(), detail.getSku());
            if (ObjectUtils.isNotEmpty(skuMap)) {
                detail.setGoodsName(skuMap.getGoodsName());
            }
        }

        // 地址去空格
        if (StringUtils.isNotEmpty(detail.getSendAddress())) {
            detail.setSendAddress(detail.getSendAddress().replaceAll("[\\s*\\u00A0]", ""));
        }

        // 商品编码去空格
        if (StringUtils.isNotEmpty(detail.getSku())) {
            detail.setSku(detail.getSku().replaceAll("[\\s*\\u00A0]", ""));
        }

        return detail;
    }

    public String checkImportWarning(ManualLogisticsImportDTO manualLogisticsImportDTO) {
        String msg = "";

        // 系统已存在该笔原发货流水号的手动下单数据
        if (StringUtils.isNotEmpty(manualLogisticsImportDTO.getOriginLogisticsSn())) {
            LogisticsOneBO oneBO = new LogisticsOneBO();
            oneBO.setOriginOrderSn(manualLogisticsImportDTO.getOriginLogisticsSn());
            oneBO.setDeliveryStatusList(Arrays.asList(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue(),
                    LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue()));
            oneBO.setPushStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusMonths(2)));
            Logistics exist = logisticsService.getOneByCondition(oneBO);
            if (ObjectUtils.isNotEmpty(exist)) {
                msg += "系统已存在该笔原发货流水号的手动下单数据；";
            }
        }

        return msg;
    }

    public String checkImportError(ManualLogisticsImportDTO manualLogisticsImportDTO) {
        String msg = "";
        // 必填项
        if (StringUtils.isEmpty(manualLogisticsImportDTO.getOriginLogisticsSn())) {
            msg += "原发货流水号不能为空；";
        }

        if (StringUtils.isEmpty(manualLogisticsImportDTO.getReason())) {
            msg += "下单原因不能为空；";
        }

        if (StringUtils.isEmpty(manualLogisticsImportDTO.getLogisticsCode())) {
            msg += "快递不能为空；";
        }

        if (StringUtils.isEmpty(manualLogisticsImportDTO.getSku())) {
            msg += "商品编码不能为空；";
        }

        if (StringUtils.isEmpty(manualLogisticsImportDTO.getNums())) {
            msg += "发货数量不能为空；";
        }

        if (StringUtils.isEmpty(manualLogisticsImportDTO.getSendPhone())) {
            msg += "收件人手机不能为空；";
        }

        if (StringUtils.isEmpty(manualLogisticsImportDTO.getSendName())) {
            msg += "收件人姓名不能为空；";
        }

        if (StringUtils.isEmpty(manualLogisticsImportDTO.getSendProvince())) {
            msg += "收件人省不能为空；";
        }

        if (StringUtils.isEmpty(manualLogisticsImportDTO.getSendCity())) {
            msg += "收件人市不能为空；";
        }

        if (StringUtils.isEmpty(manualLogisticsImportDTO.getSendDistrict())) {
            msg += "收件人区不能为空；";
        }

        if (StringUtils.isEmpty(manualLogisticsImportDTO.getSendAddress())) {
            msg += "收件人详细地址不能为空；";
        }

        // 格式校验
        int nums = Integer.parseInt(manualLogisticsImportDTO.getNums());
        if (manualLogisticsImportDTO.getNums().equals("-1") || (nums > 1000 || nums < 0)) {
            msg += "发货数量错误；";
        }

        if (!ManualLogisticsReasonEnum.list.contains(manualLogisticsImportDTO.getReason())) {
            msg += "下单原因错误；";
        }

        if (!YundaLogiticsCodeEnum.list.contains(manualLogisticsImportDTO.getLogisticsCode())) {
            msg += "快递错误；";
        }

        if (manualLogisticsImportDTO.getSendName().length() > 20) {
            msg += "收件人姓名错误；";
        }

        if (StringUtils.isNotEmpty(manualLogisticsImportDTO.getSendPhone())) {
            String pattern = "\\d+";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(manualLogisticsImportDTO.getSendPhone());
            if (!m.find()) {
                msg += "收件人电话错误；";
            }
        }

        if (StringUtils.isNotEmpty(manualLogisticsImportDTO.getOriginLogisticsSn())) {
            Logistics logistics = logisticsService.getByLogisticsSn(manualLogisticsImportDTO.getOriginLogisticsSn());
            if (ObjectUtils.isEmpty(logistics)) {
                msg += "系统不存在该笔原发货流水号；";
            }
        }

        StorageSkuMap skuMap = storageSkuMapService.getOneByStorageCodeSku(manualLogisticsImportDTO.getStorageCode(), manualLogisticsImportDTO.getSku());
        if (ObjectUtils.isEmpty(skuMap)) {
            msg += "系统不存在该编码：" + manualLogisticsImportDTO.getSku() + "；";
        }

        return msg;
    }
}
