package com.ets.delivery.application.common.consts.sendback;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.IntStream;
import java.util.LinkedHashMap;

@Getter
@AllArgsConstructor
public enum SendBackReceiveStatusEnum {

    RECEIVE_STATUS_WAIT(0, "未签收"),
    RECEIVE_STATUS_ENTER(1, "已签收"),
    RECEIVE_STATUS_NORMAL(2, "检查正常"),
    RECEIVE_STATUS_ABNORMAL(3, "检查异常"),
    RECEIVE_STATUS_OVERTIME(4, "超时未签收");
    
    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    
    static {
        SendBackReceiveStatusEnum[] enums = SendBackReceiveStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
