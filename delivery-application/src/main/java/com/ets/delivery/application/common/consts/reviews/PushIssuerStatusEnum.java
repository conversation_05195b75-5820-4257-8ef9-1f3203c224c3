package com.ets.delivery.application.common.consts.reviews;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum PushIssuerStatusEnum {

    PUSH_ISSUER_STATUS_WAIT(0, "未推送"),
    PUSH_ISSUER_STATUS_PROCESSING(1, "推送中"),
    PUSH_ISSUER_STATUS_SUCCESS(2, "推送成功"),
    PUSH_ISSUER_STATUS_FAIL(3, "推送失败"),
    PUSH_ISSUER_STATUS_CANCEL(4, "推送取消"),
    PUSH_ISSUER_STATUS_ABNORMAL(5, "推送结果异常");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    PushIssuerStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        PushIssuerStatusEnum[] enums = PushIssuerStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
