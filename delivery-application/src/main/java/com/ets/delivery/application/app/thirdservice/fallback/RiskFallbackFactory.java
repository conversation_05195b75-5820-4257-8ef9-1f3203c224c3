package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.RiskFeign;
import com.ets.delivery.application.app.thirdservice.request.risk.RiskCreateDTO;
import com.ets.delivery.application.app.thirdservice.request.risk.RiskRuleQueryDTO;
import com.ets.delivery.application.app.thirdservice.response.RiskRuleQueryVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RiskFallbackFactory implements FallbackFactory<RiskFeign> {
    @Override
    public RiskFeign create(Throwable cause) {
        return new RiskFeign() {
            @Override
            public JsonResult<Boolean> accept(RiskCreateDTO dto) {
                return JsonResult.error("调用risk服务失败，风控接收失败：" + cause.getMessage());
            }

            @Override
            public JsonResult<List<RiskRuleQueryVO>> getRuleByParams(RiskRuleQueryDTO queryDTO) {
                return JsonResult.error("调用risk服务失败，获取规则失败：" + cause.getMessage());
            }
        };
    }
}
