package com.ets.delivery.application.common.vo.postReviews;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class PostReviewWaitingReviewVO {

    /**
     * 待后审总数
     */
    private Integer totalAmount = 0;

    /**
     * 紧急后审总数
     */
    private Integer emergencyTotalNum = 0;

    /**
     * 优先后审总数
     */
    private Integer firstReviewTotalNum = 0;

    /**
     * 正常后审总数
     */
    private Integer normalReviewTotalNum = 0;

    private List<DateSummary> summaryList;

    @Data
    public static class DateSummary {

        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private LocalDate date;

        private Integer total = 0;

        private Integer emergencyNum = 0;

        private Integer firstReviewNum = 0;

        private Integer normalReviewNum = 0;

    }
}
