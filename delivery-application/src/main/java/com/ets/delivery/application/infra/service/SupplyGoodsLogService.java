package com.ets.delivery.application.infra.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.delivery.application.common.bo.GoodsLogBO;
import com.ets.delivery.application.infra.entity.SupplyGoodsLog;
import com.ets.delivery.application.infra.mapper.SupplyGoodsLogMapper;
import org.springframework.stereotype.Service;

import jakarta.validation.Valid;
import java.time.LocalDateTime;

/**
 * <p>
 * 货品操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Service
@DS("db-issuer-admin")
public class SupplyGoodsLogService extends BaseService<SupplyGoodsLogMapper, SupplyGoodsLog> {

    public void addLog(@Valid GoodsLogBO logBO) {
        SupplyGoodsLog log = BeanUtil.copyProperties(logBO, SupplyGoodsLog.class);
        log.setCreatedAt(LocalDateTime.now());
        log.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(log);
    }
}
