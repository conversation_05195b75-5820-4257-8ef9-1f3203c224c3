package com.ets.delivery.application.common.dto.riskreview;

import lombok.Data;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

/**
 * 风控审核日志列表查询DTO
 */
@Data
public class RiskReviewLogListDTO {

    /**
     * 风控单号
     */
    @NotBlank(message = "风控单号不能为空")
    private String riskReviewSn;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}