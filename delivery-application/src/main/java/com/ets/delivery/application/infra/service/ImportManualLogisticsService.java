package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.ImportManualLogistics;
import com.ets.delivery.application.infra.mapper.ImportManualLogisticsMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 手动下单发货导入记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Service
@DS("db-issuer-admin")
public class ImportManualLogisticsService extends BaseService<ImportManualLogisticsMapper, ImportManualLogistics> {

    public ImportManualLogistics getByBatchNo(String batchNo) {
        Wrapper<ImportManualLogistics> wrapper = Wrappers.<ImportManualLogistics>lambdaQuery()
                .eq(ImportManualLogistics::getBatchNo, batchNo)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
