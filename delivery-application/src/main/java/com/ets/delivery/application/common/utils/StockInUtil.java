package com.ets.delivery.application.common.utils;

import com.ets.delivery.application.common.consts.stock.StockInStatusEnum;

import java.util.Arrays;
import java.util.List;

public class StockInUtil {

    public static List<Integer> allowCancelStatus() {

        return Arrays.asList(
                StockInStatusEnum.CREATED.getCode(),
                StockInStatusEnum.NEW.getCode(),
                StockInStatusEnum.ACCEPT.getCode(),
                StockInStatusEnum.EXCEPTION.getCode(),
                StockInStatusEnum.REJECT.getCode()
        );
    }

    public static List<Integer> notAllowEditStatus() {

        return Arrays.asList(
                StockInStatusEnum.CANCELED.getCode()
        );
    }
}
