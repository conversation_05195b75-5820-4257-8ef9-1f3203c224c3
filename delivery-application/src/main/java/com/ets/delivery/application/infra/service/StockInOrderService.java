package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.thirdservice.response.yunda.StockResponseVO;
import com.ets.delivery.application.common.consts.stock.StockInStatusEnum;
import com.ets.delivery.application.common.dto.stock.StockInEditDTO;
import com.ets.delivery.application.infra.entity.StockInOrder;
import com.ets.delivery.application.infra.mapper.StockInOrderMapper;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 入库单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Service
@DS("db-issuer-admin-minor")
public class StockInOrderService extends BaseService<StockInOrderMapper, StockInOrder> {

    public StockInOrder getBySn(String stockInSn) {

        return getOneByColumn(stockInSn, StockInOrder::getStockInSn);
    }

    @Override
    public int updateByWrapper(LambdaUpdateWrapper<StockInOrder> lambdaUpdateWrapper) {
        lambdaUpdateWrapper.set(StockInOrder::getUpdatedAt, ToolsHelper.getDateTime());

        return super.updateByWrapper(lambdaUpdateWrapper);
    }

    public void cancel(String stockInSn) {

        LambdaUpdateWrapper<StockInOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StockInOrder::getStockInSn, stockInSn)
                .set(StockInOrder::getStatus, StockInStatusEnum.CANCELED.getCode());

        updateByWrapper(wrapper);
    }

    public void edit(StockInEditDTO dto) {

        LambdaUpdateWrapper<StockInOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StockInOrder::getStockInSn, dto.getStockInSn())
                .set(dto.getRemark() != null, StockInOrder::getRemark, dto.getRemark())
                .set(dto.getImages() != null, StockInOrder::getImages, dto.getImages());

        updateByWrapper(wrapper);
    }

    public void updateStatus(String stockSn, Integer status, String inTime, String extra) {

        LambdaUpdateWrapper<StockInOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StockInOrder::getStockInSn, stockSn)
                .set(StockInOrder::getStatus, status)
                .set(StringUtils.isNotEmpty(inTime), StockInOrder::getInTime, inTime)
                .set(StockInOrder::getExtra, extra);

        updateByWrapper(wrapper);
    }

    public void applySuccess(String stockInSn, StockResponseVO response) {

        LambdaUpdateWrapper<StockInOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StockInOrder::getStockInSn, stockInSn)
                .set(StockInOrder::getApplyTime, ToolsHelper.getDateTime())
                .set(StockInOrder::getStatus, StockInStatusEnum.NEW.getCode());

        updateByWrapper(wrapper);
    }
}
