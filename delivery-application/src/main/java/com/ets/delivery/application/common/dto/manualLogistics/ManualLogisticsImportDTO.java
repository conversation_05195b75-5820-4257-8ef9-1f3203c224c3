package com.ets.delivery.application.common.dto.manualLogistics;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ManualLogisticsImportDTO {

    @ExcelProperty("原发货流水号")
    private String originLogisticsSn;

    @ExcelProperty("下单原因")
    private String reason;

    @ExcelProperty("快递")
    private String logisticsCode;

    @ExcelProperty("商品编码")
    private String sku;

    @ExcelProperty("发货数量")
    private String nums;

    @ExcelProperty("收件人手机")
    private String sendPhone;

    @ExcelProperty("收件人姓名")
    private String sendName;

    @ExcelProperty("收件人省")
    private String sendProvince;

    @ExcelProperty("收件人市")
    private String sendCity;

    @ExcelProperty("收件人区")
    private String sendDistrict;

    @ExcelProperty("收件人详细地址")
    private String sendAddress;

    @ExcelProperty("备注")
    private String remark;

    /**
     * 发货仓储 默认韵达
     */
    private String storageCode = "Yunda";
}
