package com.ets.delivery.application.app.factory.task.impl;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.ets.common.*;
import com.ets.delivery.application.common.bo.task.TaskLogisticsShipFailBO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsStatusEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.delivery.application.common.dto.AppKeyParams;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.LogisticsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class TaskLogisticsShipFail extends TaskBase {

    @Autowired
    DeliveryConfig deliveryConfig;

    @Autowired
    LogisticsService logisticsService;

    @Override
    public void childExec(TaskRecord taskRecord) {
        TaskLogisticsShipFailBO shipFailBO = JSON.parseObject(taskRecord.getNotifyContent(), TaskLogisticsShipFailBO.class);
        // 检查发货单状态
        Logistics logistics = logisticsService.getByLogisticsSn(shipFailBO.getLogisticsSn());
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单不存在");
        }

        // 发货单已取消
        if (logistics.getStatus().equals(LogisticsStatusEnum.STATUS_CANCEL.getValue())) {
            ToolsHelper.throwException("发货单已取消", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        // 发货单已发货
        if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue())) {
            ToolsHelper.throwException("发货单已发货", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        Map<String, String> data = new HashMap<>();
        data.put("order_sn", shipFailBO.getOrderSn());
        data.put("status", "2");
        data.put("remark", shipFailBO.getRemark());

        try {
            // 生成签名
            String key = "key";
            Map<String, AppKeyParams> appKeyParams = deliveryConfig.getAppKeyParams();
            String secret = appKeyParams.get(logistics.getAppId()).getAppSecret();
            log.info("【发货异常通知】sign str:{}", SignHelper.getSignStringKeepEmpty(data, key, secret));
            String sign = SignHelper.generateSignKeepEmpty(data, key, secret);
            String body = JSON.toJSONString(data);
            log.info("【发货异常通知】request url:{}, body:{}, sign:{}", shipFailBO.getNotifyUrl(), body, sign);

            // 发起请求
            String result = HttpRequest.post(shipFailBO.getNotifyUrl())
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .header("x-app-key", secret)
                    .header("x-app-sign", sign)
                    .body(body)
                    .execute().body();

            log.info("【发货异常通知】response:{}", result);
            JsonResult<?> jsonResult = JSON.parseObject(result, JsonResult.class);
            jsonResult.checkError();
        } catch (HttpException e) {
            log.error("【发货异常通知】接口异常：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("通知接口请求失败");
        } catch (BizException e) {
            log.error("【发货异常通知】通知业务失败：{}", e.getErrorMsg());
            ToolsHelper.throwException("通知业务方失败：" + e.getErrorMsg());
        } catch (Throwable e) {
            log.error("【发货异常通知】{}", e.getLocalizedMessage());
            ToolsHelper.throwException("系统错误");
        }

    }
}
