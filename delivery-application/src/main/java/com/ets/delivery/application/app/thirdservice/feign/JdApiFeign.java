package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.JdApiFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(url = "${jd.express.apiUrl}", name = "JdApiFeign", fallbackFactory = JdApiFallbackFactory.class)
public interface JdApiFeign {

    @PostMapping(path = "/routerjson")
    String RouterJson(
            @RequestBody MultiValueMap<String, String> body,
            @RequestParam(value = "method") String method,
            @RequestParam(value = "app_key") String appKey,
            @RequestParam(value = "access_token") String accessToken,
            @RequestParam(value = "timestamp") String timestamp,
            @RequestParam(value = "v") String v,
            @RequestParam(value = "sign") String sign
    );
}
