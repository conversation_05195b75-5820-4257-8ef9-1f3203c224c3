package com.ets.delivery.application.common.consts;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum ReviewLogTypeEnum {

    TYPE_ADD("add", "新增"),
    TYPE_MODIFY("modify", "修改"),
    TYPE_RECEIVE("receive", "领取"),
    TYPE_REVIEW("review", "审核"),
    TYPE_PUSH_SYNC("sync", "同步数据"),
    TYPE_PUSH_ISSUER("issuer", "通知发卡方"),
    TYPE_NOTIFY_RESULT("notify", "通知结果"),
    TYPE_CANCEL("cancel", "取消"),
    TYPE_HANDLE("handle", "异常处理");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    ReviewLogTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        ReviewLogTypeEnum[] enums = ReviewLogTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
