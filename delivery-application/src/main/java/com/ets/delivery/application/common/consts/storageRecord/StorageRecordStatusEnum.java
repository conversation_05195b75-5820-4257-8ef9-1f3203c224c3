package com.ets.delivery.application.common.consts.storageRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum StorageRecordStatusEnum {


    DEFAULT(1, "待确认"),

    SUCCESS(2, "匹配成功"),

    FAILED(3, "匹配失败");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(StorageRecordStatusEnum.values())
                .collect(Collectors.toMap(StorageRecordStatusEnum::getValue, StorageRecordStatusEnum::getDesc));
    }
}
