package com.ets.delivery.application.app.thirdservice.request.workWeChat;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class WorkWeChatSendDTO {
    @J<PERSON>NField(name = "msgtype")
    private String msgType;

    @JSONField(name = "text")
    private Text text;

    @JSONField(name = "markdown")
    private Markdown markdown;

    @JSONField(name = "file")
    private File file;


    @Data
    public static class Text {
        @JSONField(name = "content")
        private String content;

        @JSONField(name = "mentioned_list")
        private List<String> mentionedList;

        @JSONField(name = "mentioned_mobile_list")
        private List<String> mentionedMobileList;
    }

    @Data
    public static class Markdown {
        @J<PERSON><PERSON>ield(name = "content")
        private String content;
    }

    @Data
    public static class File {
        @JSONField(name = "media_id")
        private String mediaId;
    }
}
