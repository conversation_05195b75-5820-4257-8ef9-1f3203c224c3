package com.ets.delivery.application.common.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ErrCodeConstant {

    CODE_LOGIN_INVALID(10001, "登录已过期，请重新登录"),

    CODE_NORMAL_ORDER_EXIST(206001,"已存在处理中的订单"),
    CODE_ORDER_ABNORMAL_STATUS(206002,"订单状态异常"),
    CODE_EXPRESS_NUMBER_WRONG(206100, "快递单号错误"),
    CODE_DELIVERY_STOCK_OUT(206200, "仓库缺货");

    private final Integer code;
    private final String description;
}
