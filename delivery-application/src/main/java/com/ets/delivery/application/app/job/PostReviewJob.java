package com.ets.delivery.application.app.job;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ets.delivery.application.app.business.PostReviewBusiness;
import com.ets.delivery.application.app.business.PostReviewQueueBusiness;
import com.ets.delivery.application.common.bo.ReviewLogBO;
import com.ets.delivery.application.common.consts.ReviewLogTypeEnum;
import com.ets.delivery.application.common.consts.postReviews.PostReviewConstant;
import com.ets.delivery.application.common.consts.postReviews.PostReviewStatusEnum;
import com.ets.delivery.application.infra.entity.PostReviews;
import com.ets.delivery.application.infra.entity.Reviews;
import com.ets.delivery.application.infra.service.PostReviewsService;
import com.ets.delivery.application.infra.service.ReviewsLogService;
import com.ets.delivery.application.infra.service.ReviewsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.*;

@Slf4j
@Component
public class PostReviewJob {

    @Autowired
    ReviewsService reviewsService;

    @Autowired
    PostReviewsService postReviewsService;

    @Autowired
    ReviewsLogService reviewsLogService;

    @Autowired
    PostReviewQueueBusiness postReviewQueueBusiness;

    @Autowired
    PostReviewBusiness postReviewBusiness;

    /**
     * 释放超时未审核后审单
     *
     * @param params 超时时长（单位小时）
     * @return 结果
     */
    @XxlJob("postReviewReleaseHandler")
    public ReturnT<String> postReviewReleaseHandler(String params) {
        log.info("后审单超时未审核取消领取，参数：" + params);
        long hour = Long.parseLong(params);
        // 超过2小时未审核取消领取
        List<PostReviews> list = postReviewsService.getOverTimeList(hour);
        if (!CollectionUtils.isEmpty(list)) {
            log.info("总条数：" + list.size());
            list.forEach(postReviews -> {
                // 更新为待审核
                PostReviews updatePostReview = new PostReviews();
                updatePostReview.setId(postReviews.getId());
                updatePostReview.setDrawTime(null);
                updatePostReview.setOperator("");
                updatePostReview.setReviewStatus(PostReviewStatusEnum.REVIEW_STATUS_DEFAULT.getValue());
                updatePostReview.setUpdatedAt(LocalDateTime.now());
                updatePostReview.updateById();

                // 移除个人领取记录
                postReviewQueueBusiness.removeUserPostReview(postReviews.getOperator(), postReviews.getReviewSn());
                // 重推到待审核队列
                postReviewQueueBusiness.rePushToPostReviewQueueList(postReviews.getReviewSn(), postReviews.getEmergencyType());

                // 记录日志
                Reviews reviews = reviewsService.getOneByReviewSn(postReviews.getReviewSn());
                ReviewLogBO logBO = new ReviewLogBO();
                logBO.setReviewId(reviews.getId());
                logBO.setOperator(postReviews.getOperator());
                logBO.setType(ReviewLogTypeEnum.TYPE_MODIFY.getValue());
                logBO.setOperateContent("【后审】已领取超过" + hour + "小时未审核，重置为待审核");
                reviewsLogService.addLog(logBO);
            });
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 初始化后审数据
     *
     * @param orderSn 订单号
     * @return 结果
     */
    @XxlJob("initPostReviewDataHandler")
    public ReturnT<String> initPostReviewDataHandler(String orderSn) {
        // 搜索自动审核单
        List<Reviews> reviewsList = reviewsService.getAutoAuditList(orderSn);
        if (!CollectionUtils.isEmpty(reviewsList)) {
            log.info("总条数：" + reviewsList.size());
            // 手动创建线程池
            ThreadFactory threadFactory = new ThreadFactoryBuilder().setNamePrefix("post-review-init-%d").build();

            ExecutorService poor = new ThreadPoolExecutor(PostReviewConstant.POOR_MIN_NUM,
                    PostReviewConstant.POOR_MAX_NUM, 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingDeque<>(PostReviewConstant.CACHE_SIZE),
                    threadFactory, new ThreadPoolExecutor.AbortPolicy());
            // 分组
            List<List<Reviews>> groupList = ListUtil.split(reviewsList, 100);

            groupList.forEach(group -> poor.execute(() -> group.forEach(reviews -> {
                // 检查是否已经生成后审单
                PostReviews postReviews = postReviewsService.getOneByReviewSn(reviews.getReviewSn());
                if (ObjectUtils.isNull(postReviews)) {
                    // 生成后审单
                    PostReviews newPostReview = new PostReviews();
                    newPostReview.setReviewSn(reviews.getReviewSn());
                    newPostReview.setCreatedAt(LocalDateTime.now());
                    newPostReview.setUpdatedAt(LocalDateTime.now());
                    newPostReview.insert();
                    log.info("生成后审单成功：" + reviews.getReviewSn());

                    // 缓存数据
                    postReviewBusiness.getPostReviewData(reviews);
                    log.info("缓存审核数据成功：" + reviews.getReviewSn());
                } else {
                    log.info("后审单已存在：" + reviews.getReviewSn());
                }
            })));
            // 线程池关闭
            poor.shutdown();
        }
        log.info("执行完毕");
        return ReturnT.SUCCESS;
    }
}
