package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.importFile.ImportFileFactory;
import com.ets.delivery.application.app.factory.importFile.impl.ImportFileBase;
import com.ets.delivery.application.common.consts.importFile.ImportFileImportTypeEnum;
import com.ets.delivery.application.common.consts.importFile.ImportFileUploadStatusEnum;
import com.ets.delivery.application.common.dto.rejectExpress.RejectExpressExportDTO;
import com.ets.delivery.application.common.dto.rejectExpress.RejectExpressImportListDTO;
import com.ets.delivery.application.common.vo.rejectExpress.RejectExpressExportVO;
import com.ets.delivery.application.common.vo.rejectExpress.RejectExpressImportListVO;
import com.ets.delivery.application.common.vo.rejectExpress.RejectExpressImportVO;
import com.ets.delivery.application.infra.entity.ImportFileRecord;
import com.ets.delivery.application.infra.entity.ImportRejectExpressDetail;
import com.ets.delivery.application.infra.service.ImportFileRecordService;
import com.ets.delivery.application.infra.service.ImportRejectExpressDetailService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Component
public class RejectExpressImportBusiness {

    @Autowired
    private ImportFileRecordService importFileRecordService;

    @Autowired
    private ImportRejectExpressDetailService importRejectExpressDetailService;

    public RejectExpressImportVO importFile(MultipartFile file) {
        ImportFileBase rejectExpress = ImportFileFactory.create(ImportFileImportTypeEnum.REJECT_EXPRESS_IMPORT);

        // 检查文件
        rejectExpress.importFileCheck(file);

        // 初始化导入记录
        ImportFileRecord importFileRecord = rejectExpress.initImportRecord(file.getOriginalFilename(), ImportFileImportTypeEnum.REJECT_EXPRESS_IMPORT.getType());

        // 读取Excel文件
        rejectExpress.importFile(file, importFileRecord);

        RejectExpressImportVO importVO = new RejectExpressImportVO();
        importVO.setBatchNo(importFileRecord.getBatchNo());
        return importVO;
    }

    public void exportFile(RejectExpressExportDTO exportDTO, HttpServletResponse response) {
        List<ImportRejectExpressDetail> detailList = importRejectExpressDetailService.getListByColumn(exportDTO.getBatchNo(), ImportRejectExpressDetail::getBatchNo);
        if (ObjectUtils.isEmpty(detailList) || detailList.size() == 0) {
            ToolsHelper.throwException("导出数据不存在");
        }

        List<RejectExpressExportVO> exportList = new ArrayList<>();
        detailList.forEach(detail -> {
            RejectExpressExportVO exportVO = BeanUtil.copyProperties(detail, RejectExpressExportVO.class);
            exportList.add(exportVO);
        });

        try {
            // 设置文本内省
            response.setContentType("application/vnd.ms-excel");
            // 设置字符编码
            response.setCharacterEncoding("utf-8");
            // 设置文件名
            String filename = URLEncoder.encode("拒收导入结果" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename);
            EasyExcel.write(response.getOutputStream(), RejectExpressExportVO.class)
                    .sheet("匹配结果")
                    .doWrite(exportList);
        } catch (IOException e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            ToolsHelper.throwException("导出文件失败");
        }
    }

    public IPage<RejectExpressImportListVO> getImportDataList(RejectExpressImportListDTO listDTO) {
        ImportFileRecord record = importFileRecordService.getByBatchNo(listDTO.getBatchNo());
        if (ObjectUtils.isEmpty(record)) {
            ToolsHelper.throwException("上传记录不存在");
        }

        if (record.getUploadStatus().equals(ImportFileUploadStatusEnum.FAIL.getValue())) {
            ToolsHelper.throwException(record.getErrorMsg());
        }

        IPage<ImportRejectExpressDetail> page = importRejectExpressDetailService.getPage(listDTO);
        return page.convert(detail -> BeanUtil.copyProperties(detail, RejectExpressImportListVO.class));
    }
}
