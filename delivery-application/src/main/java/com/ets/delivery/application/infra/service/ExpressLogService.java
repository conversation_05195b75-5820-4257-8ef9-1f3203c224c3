package com.ets.delivery.application.infra.service;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.delivery.application.common.bo.express.ExpressLogBO;
import com.ets.delivery.application.infra.entity.ExpressLog;
import com.ets.delivery.application.infra.mapper.ExpressLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;

/**
 * <p>
 * 物流单日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Service
@DS("db-service")
public class ExpressLogService extends BaseService<ExpressLogMapper, ExpressLog> {

    public void addLog(@Validated ExpressLogBO logBO) {
        ExpressLog log = BeanUtil.copyProperties(logBO, ExpressLog.class);
        log.setCreatedAt(LocalDateTime.now());
        log.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(log);
    }
}
