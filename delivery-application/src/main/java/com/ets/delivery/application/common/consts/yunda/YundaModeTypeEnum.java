package com.ets.delivery.application.common.consts.yunda;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum YundaModeTypeEnum {

    B2B("B2B", "B2B"),
    B2C("B2C", "B2C");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        map = Arrays.stream(YundaModeTypeEnum.values()).collect(Collectors.toMap(YundaModeTypeEnum::getValue, YundaModeTypeEnum::getDesc));
    }
}
