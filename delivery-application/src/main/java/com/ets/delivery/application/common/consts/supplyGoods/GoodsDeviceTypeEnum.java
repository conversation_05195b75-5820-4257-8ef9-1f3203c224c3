package com.ets.delivery.application.common.consts.supplyGoods;

import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
public enum GoodsDeviceTypeEnum {

    NORMAL(0, "普通设备"),
    RECHARGEABLE(1, "可充电式设备"),
    ONE_CHIP(2, "单片式设备");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    public static final List<Integer> list;

    GoodsDeviceTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        GoodsDeviceTypeEnum[] enums = GoodsDeviceTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Arrays.stream(enums).map(GoodsDeviceTypeEnum::getValue).collect(Collectors.toList());
    }
}
