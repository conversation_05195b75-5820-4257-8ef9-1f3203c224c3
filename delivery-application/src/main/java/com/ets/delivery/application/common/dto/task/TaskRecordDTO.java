package com.ets.delivery.application.common.dto.task;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskRecordDTO {

    /**
     * 任务流水号
     */
    private String taskSn;

    /**
     * 任务关联sn,对应order_sn之类
     */
    private String referSn;

    /**
     * 任务类型
     */
    private String referType;

    /**
     * 处理内容
     */
    private String notifyContent;

    /**
     * 任务状态：0待处理，1处理中，2处理完成3处理失败4暂停处理
     */
    private Integer status;


    /**
     * 下一次执行时间
     * 数据库时间精度为秒 LocalDateTime.now()精度为纳秒 精度丢失判断会出错
     */
    private LocalDateTime nextExecTime = LocalDateTime.now().minusSeconds(1);

    /**
     * 延时级别 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     */
    private int delayLevel = 0;
}
