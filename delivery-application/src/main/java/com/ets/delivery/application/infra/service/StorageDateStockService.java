package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.delivery.application.infra.entity.StorageDateStock;
import com.ets.delivery.application.infra.mapper.StorageDateStockMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 仓库库存记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Service
@DS("db-issuer-admin")
public class StorageDateStockService extends BaseService<StorageDateStockMapper, StorageDateStock> {

}
