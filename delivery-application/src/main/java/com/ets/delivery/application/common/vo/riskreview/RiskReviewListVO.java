package com.ets.delivery.application.common.vo.riskreview;

import com.ets.delivery.application.common.consts.PlateColorEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewRiskTypeEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import lombok.Data;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

@Data
public class RiskReviewListVO {
    private Integer id;

    private String riskReviewSn;

    private Integer riskType;

    private String riskTypeStr;

    private String riskRuleRemark;

    private String businessSn;

    private Integer issuerId;

    private String issuerName;

    private String plateNo;

    private Integer plateColor;

    private String plateColorStr;

    private Integer riskReviewStatus;

    private String riskReviewStatusStr;

    private Integer autoAudit;

    private String operator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime riskReviewTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    public String getRiskTypeStr() {
        return RiskReviewRiskTypeEnum.map.getOrDefault(riskType, "-");
    }

    public String getPlateColorStr() {
        return PlateColorEnum.map.getOrDefault(plateColor, "-");
    }

    public String getRiskReviewStatusStr() {
        return RiskReviewStatusEnum.map.getOrDefault(riskReviewStatus, "-");
    }
} 