package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_user")
public class User extends BaseEntity<User> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属仓储，多个以逗号隔开
     */
    private String storageCode;

    /**
     * 用户名
     */
    private String username;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 手机号码
     */
    private String phone;

    private String company;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    private String remark;

    private String authKey;

    /**
     * 密码
     */
    private String passwordHash;

    private String passwordResetToken;

    /**
     * 后台登录校验token
     */
    private String adminToken;

    /**
     * 登录校验token
     */
    private String token;

    /**
     * 小程序openid
     */
    private String minaOpenid;

    /**
     * 状态 10正常 
     */
    private Integer status;

    /**
     * 上次登录时间
     */
    private LocalDateTime lastLoginDate;

    private Integer createdAt;

    private Integer updatedAt;


}
