package com.ets.delivery.application.app.factory.storage.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.app.thirdservice.business.JdCloudBusiness;
import com.ets.delivery.application.app.thirdservice.request.jd.*;
import com.ets.delivery.application.app.thirdservice.response.jd.*;
import com.ets.delivery.application.common.bo.exwarehouse.ExWarehouseSplitDetailBO;
import com.ets.delivery.application.common.config.jd.JdExpressConfig;
import com.ets.delivery.application.common.consts.jd.JdCancelCodeEnum;
import com.ets.delivery.application.common.consts.jd.JdCurrentStatusEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeConstant;
import com.ets.delivery.application.common.dto.logistics.LogisticsCancelOrderDTO;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.common.vo.InventoryQueryVO;
import com.ets.delivery.application.common.vo.WarehouseStockListVO;
import com.ets.delivery.application.common.vo.alarm.JdCloudStockAlarmExcelVO;
import com.ets.delivery.application.common.vo.alarm.StorageStockAlarmFileVO;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.ExWarehouseService;
import com.ets.delivery.application.infra.service.StorageDateStockService;
import com.ets.delivery.application.infra.service.TaskRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Component
public class JdCloudStorageManage extends StorageBase {

    @Autowired
    private JdExpressConfig jdExpressConfig;

    @Autowired
    private ExWarehouseService exWarehouseService;

    @Autowired
    private JdCloudBusiness jdCloudBusiness;

    @Autowired
    private TaskRecordService taskRecordService;

    @Autowired
    private StorageDateStockService storageDateStockService;

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @Override
    public String addOrder(ExWarehouse exWarehouse, List<ExWarehouseDetail> detailList) {
        // 发货信息
        JdAddOrderDTO addOrderDTO = new JdAddOrderDTO();
        addOrderDTO.setIsvUUID(exWarehouse.getIsvUuid());
        addOrderDTO.setIsvSource(jdExpressConfig.getIsvSource());
        addOrderDTO.setShopNo(jdExpressConfig.getShopNo());
        addOrderDTO.setDepartmentNo(jdExpressConfig.getDepartmentNo());
        addOrderDTO.setConsigneeName(exWarehouse.getConsigneeName());
        addOrderDTO.setConsigneeMobile(exWarehouse.getConsigneeMobile());
        addOrderDTO.setConsigneeAddress(exWarehouse.getConsigneeProvince() +
                exWarehouse.getConsigneeCity() +
                exWarehouse.getConsigneeArea() +
                exWarehouse.getConsigneeAddress());

        // 发货商品
        AtomicReference<String> goodsNo = new AtomicReference<>("");
        AtomicReference<String> quantity = new AtomicReference<>("");
        detailList.forEach(detail -> {
            goodsNo.set(goodsNo + (detail.getGoodsNo() + ","));
            quantity.set(quantity + (detail.getQuantity() + ","));
        });
        addOrderDTO.setGoodsNo(goodsNo.get().substring(0, goodsNo.get().length() - 1));
        addOrderDTO.setQuantity(quantity.get().substring(0, quantity.get().length() - 1));

        JdAddOrderVO addOrderVO = jdCloudBusiness.addOrder(addOrderDTO);
        if (ObjectUtils.isEmpty(addOrderVO.getJdAddOrderResponse())) {
            ToolsHelper.throwException("京东返回结果为空");
        }

        if (!addOrderVO.getJdAddOrderResponse().getCode().equals("0")) {
            ToolsHelper.throwException("京东下单失败");
        }

        try {
            TaskRecord taskRecord = taskRecordService.getOneByCondition(exWarehouse.getIsvUuid(),
                    TaskRecordReferTypeConstant.LOGISTICS_QUERY_ORDER,
                    null
            );

            if (ObjectUtils.isEmpty(taskRecord)) {
                // 延时10s查询出库单状态
                TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
                taskRecordDTO.setReferSn(exWarehouse.getIsvUuid());
                taskRecordDTO.setReferType(TaskRecordReferTypeConstant.LOGISTICS_QUERY_ORDER);
                taskRecordDTO.setDelayLevel(3);
                TaskFactory.create(TaskRecordReferTypeConstant.LOGISTICS_QUERY_ORDER).addAndPush(taskRecordDTO);
            }
        } catch (Exception e) {
            log.error("【京东下单】创建延时查询出库单状态任务失败", e);
        }

        return addOrderVO.getJdAddOrderResponse().getEclpSoNo();
    }

    @Override
    public void cancelOrder(LogisticsCancelOrderDTO cancelOrderDTO) {
        // 查找出库单记录
        ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(cancelOrderDTO.getLogisticsSn());
        if (ObjectUtils.isNotEmpty(exWarehouse)) {
            // 已发货 不能取消
            if (ObjectUtils.isNotEmpty(exWarehouse.getWayBill())) {
                ToolsHelper.throwException("出库中,无法取消");
            }

            // 出库单详情如果包含10017 就不能取消
            JdQueryOrderDTO jdQueryOrderDTO = new JdQueryOrderDTO();
            jdQueryOrderDTO.setEclpSoNo(exWarehouse.getDeliverOrderNo());

            JdQueryOrderVO queryOrderVO = jdCloudBusiness.queryOrder(jdQueryOrderDTO);
            if (ObjectUtils.isNotEmpty(queryOrderVO.getJdQueryOrderResponse())) {
                List<JdQueryOrderVO.JdQueryOrderResponseVO.JdQueryOrderResult.OrderStatus> orderStatusList = queryOrderVO.getJdQueryOrderResponse().getResult().getOrderStatusList();
                if (ObjectUtils.isNotEmpty(orderStatusList)) {
                    orderStatusList.forEach(orderStatus -> {
                        if (orderStatus.getSoStatusCode().intValue() == JdCurrentStatusEnum.STATUS_10017.getValue()) {
                            ToolsHelper.throwException("出库中,无法取消");
                        }
                    });
                }
            }

            // 发起取消
            JdCancelOrderDTO jdCancelOrderDTO = new JdCancelOrderDTO();
            jdCancelOrderDTO.setEclpSoNo(exWarehouse.getDeliverOrderNo());

            JdCancelOrderVO cancelOrderVO = jdCloudBusiness.cancelOrder(jdCancelOrderDTO);
            if (ObjectUtils.isNotEmpty(cancelOrderVO.getJdCancelOrderResponse())) {
                if (cancelOrderVO.getJdCancelOrderResponse().getResult().getCode().equals(JdCancelCodeEnum.CANCEL_FAIL.getValue())) {
                    ToolsHelper.throwException("取消失败");
                } else if (cancelOrderVO.getJdCancelOrderResponse().getResult().getCode().equals(JdCancelCodeEnum.CANCEL_PROCESSING.getValue())) {
                    ToolsHelper.throwException("取消中,请稍后查询订单结果");
                }
            }
        }
    }

    @Override
    public ExWarehouse orderQuery(ExWarehouse exWarehouse) {
        JdQueryOrderDTO queryOrderDTO = new JdQueryOrderDTO();
        queryOrderDTO.setEclpSoNo(exWarehouse.getDeliverOrderNo());

        JdQueryOrderVO queryOrderVO = jdCloudBusiness.queryOrder(queryOrderDTO);
        if (ObjectUtils.isNotEmpty(queryOrderVO.getJdQueryOrderResponse())) {
            JdQueryOrderVO.JdQueryOrderResponseVO.JdQueryOrderResult result = queryOrderVO.getJdQueryOrderResponse().getResult();

            String wayBill = result.getWayBill();
            // 拆分子单
            if (result.getSplitFlag().equals("1")) {
                exWarehouse.setSplitFlag(1);
                String[] splitOrderNos = result.getSplitEclpSoNos().split(",");

                for (String splitOrderNo : splitOrderNos) {
                    // 更新子出库单
                    ExWarehouse splitExWarehouse = updateExWarehouseSplit(exWarehouse, splitOrderNo);
                    // 临时取第一个物流单号
                    if (StringUtils.isEmpty(wayBill) && ObjectUtils.isNotEmpty(splitExWarehouse) && ObjectUtils.isNotEmpty(splitExWarehouse.getWayBill())) {
                        wayBill = splitExWarehouse.getWayBill();
                    }
                }

                if (StringUtils.isEmpty(wayBill)) {
                    ToolsHelper.throwException("京东出库订单没有物流单号返回");
                }
            } else {
                if (StringUtils.isEmpty(wayBill)) {
                    ToolsHelper.throwException("京东出库订单没有物流单号返回");
                }
                if (StringUtils.isEmpty(exWarehouse.getWayBill())) {
                    exWarehouse.setWayBill(wayBill);
                    exWarehouse.setWarehouseNo(result.getWarehouseNo());
                }
            }

            // 更新出库单
            if (!Integer.valueOf(result.getCurrentStatus()).equals(exWarehouse.getCurrentStatus())) {
                exWarehouse.setShipperNo(result.getShipperNo());
                exWarehouse.setShipperName(result.getShipperName());
                exWarehouse.setDepartmentNo(result.getDepartmentNo());
                exWarehouse.setShopNo(result.getShopNo());
                exWarehouse.setCurrentStatus(Integer.valueOf(result.getCurrentStatus()));
                exWarehouse.setUpdatedAt(LocalDateTime.now());
                exWarehouseService.updateById(exWarehouse);
            }
        }
        return exWarehouse;
    }

    @Override
    public List<InventoryQueryVO> inventoryQuery(List<String> goodsCodeList) {
        List<InventoryQueryVO> inventoryQueryList = new ArrayList<>();
        // 获取有效库房
        JdQueryWarehouseDTO queryWarehouseDTO = new JdQueryWarehouseDTO();
        queryWarehouseDTO.setDeptNo(jdExpressConfig.getDepartmentNo());
        JdQueryWarehouseVO queryWarehouseVO = jdCloudBusiness.queryWarehouse(queryWarehouseDTO);
        if (ObjectUtils.isNotEmpty(queryWarehouseVO.getJdQueryWarehouseResponse())) {
            List<JdQueryWarehouseVO.JdQueryWarehouseResponseVO.JdQueryWarehouseResult> warehouseResultList = queryWarehouseVO.getJdQueryWarehouseResponse().getResult();
            if (ObjectUtils.isNotEmpty(warehouseResultList)) {

                warehouseResultList.forEach(result -> {
                    // 获取库存
                    JdQueryStockDTO queryStockDTO = new JdQueryStockDTO();
                    queryStockDTO.setDeptNo(jdExpressConfig.getDepartmentNo());
                    queryStockDTO.setWarehouseNo(result.getWarehouseNo());
                    JdQueryStockVO queryStockVO = jdCloudBusiness.queryStock(queryStockDTO);

                    if (ObjectUtils.isNotEmpty(queryStockVO.getJdQueryStockResponse())) {
                        List<JdQueryStockVO.JdQueryStockResponseVO.JdQueryStockResult> stockResult = queryStockVO.getJdQueryStockResponse().getResult();
                        if (ObjectUtils.isNotEmpty(stockResult)) {
                            stockResult.forEach(stock -> {
                                InventoryQueryVO inventoryQueryVO = new InventoryQueryVO();
                                inventoryQueryVO.setWarehouseNo(stock.getWarehouseNo());
                                inventoryQueryVO.setWarehouseName(stock.getWarehouseName());
                                inventoryQueryVO.setGoodsCode(stock.getGoodsNo());
                                inventoryQueryVO.setGoodsName(stock.getGoodsName());
                                inventoryQueryVO.setStockNum(stock.getTotalNum());
                                inventoryQueryVO.setStockUsableNum(stock.getUsableNum());
                                inventoryQueryList.add(inventoryQueryVO);
                            });
                        }
                    }

                });
            }
        }
        return inventoryQueryList;
    }

    @Override
    public List<WarehouseStockListVO> stockList(String startDate, String endDate) {
        List<WarehouseStockListVO> warehouseStockList = new ArrayList<>();
        // 查询数据库
        LambdaQueryWrapper<StorageDateStock> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StorageDateStock::getStorageCode, StorageCodeEnum.JD_CLOUD.getValue())
                .between(StorageDateStock::getStockDate, startDate, endDate)
                .orderByDesc(StorageDateStock::getStockDate)
                .orderByDesc(StorageDateStock::getGoodsCode);
        List<StorageDateStock> stockList = storageDateStockService.getListByWrapper(wrapper);
        if (ObjectUtils.isNotEmpty(stockList)) {
            stockList.forEach(storageDateStock -> {
                WarehouseStockListVO warehouseStockListVO = new WarehouseStockListVO();
                warehouseStockListVO.setDate(storageDateStock.getStockDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                warehouseStockListVO.setWarehouseNo(storageDateStock.getWarehouseNo());
                warehouseStockListVO.setWarehouseName(storageDateStock.getWarehouseName());
                warehouseStockListVO.setSku(storageDateStock.getGoodsCode());
                warehouseStockListVO.setStock(storageDateStock.getStockNum());
                warehouseStockListVO.setStockUsableNum(storageDateStock.getStockUsableNum());
                warehouseStockList.add(warehouseStockListVO);
            });
        }

        return warehouseStockList;
    }

    private ExWarehouse updateExWarehouseSplit(ExWarehouse exWarehouse, String splitOrderNo) {
        JdQueryOrderDTO splitQueryOrderDTO = new JdQueryOrderDTO();
        splitQueryOrderDTO.setEclpSoNo(splitOrderNo);

        JdQueryOrderVO splitQueryOrderVO = jdCloudBusiness.queryOrder(splitQueryOrderDTO);
        if (ObjectUtils.isNotEmpty(splitQueryOrderVO.getJdQueryOrderResponse())) {
            JdQueryOrderVO.JdQueryOrderResponseVO.JdQueryOrderResult result = splitQueryOrderVO.getJdQueryOrderResponse().getResult();

            // 初始化子单出库单
            ExWarehouse splitExWarehouse = initExWarehouseSplit(exWarehouse, splitOrderNo);

            // 初始化子单出库单明细
            List<ExWarehouseSplitDetailBO> splitDetailBOList = new ArrayList<>();
            result.getOrderDetailList().forEach(orderDetail -> {
                ExWarehouseSplitDetailBO splitDetailBO = new ExWarehouseSplitDetailBO();
                splitDetailBO.setGoodsNo(orderDetail.getGoodsNo());
                splitDetailBO.setQuantity(orderDetail.getQuantity().intValue());
                if (ObjectUtils.isNotEmpty(orderDetail.getPrice())) {
                    splitDetailBO.setPrice(new BigDecimal(orderDetail.getPrice().toString()));
                }
                if (ObjectUtils.isNotEmpty(orderDetail.getRealOutQty())) {
                    splitDetailBO.setRealOutQty(orderDetail.getRealOutQty().intValue());
                }
                splitDetailBOList.add(splitDetailBO);
            });
            initExWarehouseSplitDetail(splitExWarehouse, splitOrderNo, splitDetailBOList);

            // 更新快递单号
            if (StringUtils.isEmpty(splitExWarehouse.getWayBill())) {
                splitExWarehouse.setWayBill(result.getWayBill());
                splitExWarehouse.setWarehouseNo(result.getWarehouseNo());
                splitExWarehouse.setShipperNo(result.getShipperNo());
                splitExWarehouse.setShipperName(result.getShipperName());
                splitExWarehouse.setDepartmentNo(result.getDepartmentNo());
                splitExWarehouse.setShopNo(result.getShopNo());
            }
            // 更新出库单状态
            if (!Integer.valueOf(result.getCurrentStatus()).equals(splitExWarehouse.getCurrentStatus())) {
                splitExWarehouse.setCurrentStatus(Integer.valueOf(result.getCurrentStatus()));
                splitExWarehouse.setUpdatedAt(LocalDateTime.now());
                exWarehouseService.updateById(splitExWarehouse);
            }

            return splitExWarehouse;
        }
        return null;
    }

    @Override
    public StorageStockAlarmFileVO stockAlarm(List<SupplyGoods> goodsList) {
        StorageStockAlarmFileVO storageStockAlarmFileVO = new StorageStockAlarmFileVO();
        List<JdCloudStockAlarmExcelVO> jdCloudStockAlarmList = new ArrayList<>();

        // 查询最近8天所有库存记录
        String yesterday = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String eightDaysAgo = LocalDate.now().minusDays(8).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<WarehouseStockListVO> stockList = this.stockList(eightDaysAgo, yesterday);
        if (ObjectUtil.isEmpty(stockList)) {
            return storageStockAlarmFileVO;
        }
        Map<String, List<WarehouseStockListVO>> goodsStockListMap = stockList.stream().collect(Collectors.groupingBy(WarehouseStockListVO::getSku));

        List<String> storageSkuList = goodsList.stream().map(SupplyGoods::getGoodsCode).collect(Collectors.toList());

        // 统计我司昨日出库量
        Map<String, Double> yesterdaySumMap = logisticsBusiness.getLogisticsSumMap(StorageCodeEnum.JD_CLOUD.getValue(), storageSkuList, 1);

        goodsList.forEach(goods -> {
            Double yesterdaySum = yesterdaySumMap.getOrDefault(goods.getGoodsCode(), 0.0);

            JdCloudStockAlarmExcelVO jdCloudStockAlarmExcelVO = JdCloudStockAlarmExcelVO.builder()
                    .goodsUnionCode(goods.getGoodsUnionCode())
                    .goodsSku(goods.getGoodsCode())
                    .goodsName(goods.getGoodsName())
                    .yesterdayLogisticsNum(yesterdaySum.intValue())
                    .build();

            // 获取商品库存记录
            List<WarehouseStockListVO> goodsStockList = goodsStockListMap.get(goods.getGoodsCode());
            if (ObjectUtils.isNotEmpty(goodsStockList)) {
                // 商品昨日库存记录
                List<WarehouseStockListVO> yesterdayStockList = goodsStockList.stream()
                        .filter(goodsStock -> goodsStock.getDate().equals(yesterday))
                        .collect(Collectors.toList());

                // 商品前日库存记录
                List<WarehouseStockListVO> beforeYesterdayStockList = goodsStockList.stream()
                        .filter(goodsStock -> goodsStock.getDate().equals(LocalDate.now().minusDays(2).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))))
                        .collect(Collectors.toList());
                Map<String, WarehouseStockListVO> beforeYesterdayStockMap = beforeYesterdayStockList.stream()
                        .collect(Collectors.toMap(WarehouseStockListVO::getWarehouseNo, warehouseStockListVO -> warehouseStockListVO));

                // 商品4天前库存记录
                List<WarehouseStockListVO> fourDaysAgoStockList = goodsStockList.stream()
                        .filter(goodsStock -> goodsStock.getDate().equals(LocalDate.now().minusDays(4).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))))
                        .collect(Collectors.toList());
                Map<String, WarehouseStockListVO> fourDaysAgoStockMap = fourDaysAgoStockList.stream()
                        .collect(Collectors.toMap(WarehouseStockListVO::getWarehouseNo, warehouseStockListVO -> warehouseStockListVO));

                // 商品8天前库存记录
                List<WarehouseStockListVO> eightDaysAgoStockList = goodsStockList.stream()
                        .filter(goodsStock -> goodsStock.getDate().equals(LocalDate.now().minusDays(8).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))))
                        .collect(Collectors.toList());
                Map<String, WarehouseStockListVO> eightDaysAgoStockMap = eightDaysAgoStockList.stream()
                        .collect(Collectors.toMap(WarehouseStockListVO::getWarehouseNo, warehouseStockListVO -> warehouseStockListVO));

                // 循环商品的每个仓库
                if (ObjectUtils.isNotEmpty(yesterdayStockList)) {

                    // 昨日发货总量
                    AtomicInteger yesterdayLogisticsSum = new AtomicInteger();
                    yesterdayStockList.forEach(goodsStock -> {
                        // 前日各仓库库存
                        WarehouseStockListVO beforeYesterdayStock = beforeYesterdayStockMap.get(goodsStock.getWarehouseNo());
                        if (ObjectUtils.isNotEmpty(beforeYesterdayStock)) {
                            yesterdayLogisticsSum.addAndGet(Math.max(beforeYesterdayStock.getStockUsableNum() - goodsStock.getStockUsableNum(), 0));
                        }
                    });
                    jdCloudStockAlarmExcelVO.setJdYesterdayLogisticsSum(yesterdayLogisticsSum.get());
                    // 出库相差 = 昨日发货量 - 我司昨日出库量
                    jdCloudStockAlarmExcelVO.setLogisticsDiff(jdCloudStockAlarmExcelVO.getJdYesterdayLogisticsSum() - yesterdaySum.intValue());

                    yesterdayStockList.forEach(goodsStock -> {

                        // 昨日发货量
                        int yesterdayOutStockNum = 0;
                        // 3天发货量
                        int threeDayOutStockNum = 0;
                        // 7天发货量
                        int sevenDayOutStockNum = 0;
                        // 预计可发货天数
                        int predictDay = 0;
                        // 过去7天平均发货量
                        int sevenDayAvg = 0;
                        // 前日各仓库库存
                        WarehouseStockListVO beforeYesterdayStock = beforeYesterdayStockMap.get(goodsStock.getWarehouseNo());
                        if (ObjectUtils.isNotEmpty(beforeYesterdayStock)) {
                            yesterdayOutStockNum = Math.max(beforeYesterdayStock.getStockUsableNum() - goodsStock.getStockUsableNum(), 0);
                        }
                        // 4天前各仓库库存
                        WarehouseStockListVO fourDaysAgoStock = fourDaysAgoStockMap.get(goodsStock.getWarehouseNo());
                        if (ObjectUtils.isNotEmpty(fourDaysAgoStock)) {
                            threeDayOutStockNum = Math.max(fourDaysAgoStock.getStockUsableNum() - goodsStock.getStockUsableNum(), 0);
                        }
                        // 8天前各仓库库存
                        WarehouseStockListVO eightDaysAgoStock = eightDaysAgoStockMap.get(goodsStock.getWarehouseNo());
                        if (ObjectUtils.isNotEmpty(eightDaysAgoStock)) {
                            sevenDayOutStockNum = Math.max(eightDaysAgoStock.getStockUsableNum() - goodsStock.getStockUsableNum(), 0);
                            if (sevenDayOutStockNum != 0) {
                                sevenDayAvg = sevenDayOutStockNum / 7;
                            }
                        }
                        // 预计可发货天数 = 可用库存 / 7天平均发货量
                        if (sevenDayAvg != 0) {
                            predictDay = goodsStock.getStockUsableNum() / sevenDayAvg;
                        }

                        JdCloudStockAlarmExcelVO goodsWarehouseStockVo = BeanUtil.copyProperties(jdCloudStockAlarmExcelVO, JdCloudStockAlarmExcelVO.class);
                        goodsWarehouseStockVo.setWarehouseName(goodsStock.getWarehouseName());
                        goodsWarehouseStockVo.setStockNum(goodsStock.getStock());
                        goodsWarehouseStockVo.setStockUsableNum(goodsStock.getStockUsableNum());
                        goodsWarehouseStockVo.setJdYesterdayOutStockNum(yesterdayOutStockNum);
                        goodsWarehouseStockVo.setJdThreeDaysOutStockNum(threeDayOutStockNum);
                        goodsWarehouseStockVo.setJdSevenDaysOutStockNum(sevenDayOutStockNum);
                        goodsWarehouseStockVo.setPredictDayNum(predictDay);

                        jdCloudStockAlarmList.add(goodsWarehouseStockVo);
                    });
                }
            }
        });

        storageStockAlarmFileVO.setJdCloudStockAlarmList(jdCloudStockAlarmList);
        return storageStockAlarmFileVO;
    }
}
