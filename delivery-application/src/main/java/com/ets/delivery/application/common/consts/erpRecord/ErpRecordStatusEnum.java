package com.ets.delivery.application.common.consts.erpRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ErpRecordStatusEnum {

    // 状态[0-待处理 1-处理成功 2-处理失败]
    WAITING(0, "待处理"),
    SUCCESS(1, "处理成功"),
    FAIL(2, "处理失败");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(ErpRecordStatusEnum.values()).collect(Collectors.toMap(ErpRecordStatusEnum::getValue, ErpRecordStatusEnum::getDesc));
    }
}
