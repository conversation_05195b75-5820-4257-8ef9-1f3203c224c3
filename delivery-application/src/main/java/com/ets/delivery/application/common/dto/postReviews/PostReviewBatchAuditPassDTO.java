package com.ets.delivery.application.common.dto.postReviews;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class PostReviewBatchAuditPassDTO {

    @NotNull(message = "id参数缺失")
    @NotEmpty(message = "id不能为空")
    private List<Integer> postReviewIds;

    /**
     * 是否获取下一条
     */
    private boolean needNext;

    /**
     * 是否正序
     */
    private String sort = "asc";

    /**
     * 后审类型
     */
    private Integer emergencyType;
}
