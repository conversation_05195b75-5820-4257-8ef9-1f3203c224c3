package com.ets.delivery.application.common.consts.logistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExpressCorpEnum {
    EXPRESS_CORP_JD("jd","京东"),
    EXPRESS_CORP_YUNDA("yunda","韵达");

    private final String code;
    private final String description;

    public static String getDescByCode(String code) {
        for (ExpressCorpEnum node : ExpressCorpEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
