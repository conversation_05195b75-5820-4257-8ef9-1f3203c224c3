package com.ets.delivery.application.app.thirdservice.response.workWeChat;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class WorkWeChatUploadMediaVO {
    @JSONField(name = "errcode")
    private Integer errCode;

    @JSONField(name = "errmsg")
    private String errMsg;

    @JSONField(name = "type")
    private String type;

    @JSONField(name = "media_id")
    private String mediaId;

    @JSONField(name = "created_at")
    private String createdAt;
}
