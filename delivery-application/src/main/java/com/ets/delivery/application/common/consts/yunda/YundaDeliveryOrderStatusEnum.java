package com.ets.delivery.application.common.consts.yunda;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum YundaDeliveryOrderStatusEnum {

    //NEW-未开始处理,ACCEPT-仓库接单,PARTDELIVERED-部分发货完成,DELIVERED-发货完成,
    // EXCEPTION-异常,CANCELED-取消,CLOSED-关闭,REJECT-拒单,CANCELEDFAIL-取消失败
    NEW("NEW", "未开始处理"),
    ACCEPT("ACCEPT", "仓库接单"),
    PART_DELIVERED("PARTDELIVERED", "部分发货完成"),
    DELIVERED("DELIVERED", "发货完成"),
    EXCEPTION("EXCEPTION", "异常"),
    CANCELED("CANCELED", "取消"),
    CLOSED("CLOSED", "关闭"),
    REJECT("REJECT", "拒单"),
    CANCELED_FAIL("CANCELEDFAIL", "取消失败");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    YundaDeliveryOrderStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        YundaDeliveryOrderStatusEnum[] enums = YundaDeliveryOrderStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
