package com.ets.delivery.application.common.consts.aftersalesreviews;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum AftersalesReviewsOrderTypeEnum {
    // 重新激活、补办、维修换货
    REACTIVATE("reactivate", "重新激活"),
    REAPPLY("reapply", "补办"),
    AFTER_SALES_MAINTAIN_EXCHANGE("after_sales_maintain_exchange", "维修换货"),
    ;

    private final String value;
    private final String desc;

    public static final Map<String, String> map;
    static {
        map = Arrays.stream(AftersalesReviewsOrderTypeEnum.values()).collect(Collectors.toMap(AftersalesReviewsOrderTypeEnum::getValue, AftersalesReviewsOrderTypeEnum::getDesc));
    }

    /**
     * 检查给定的值是否为有效的枚举值
     * @param value 要检查的值
     * @return 如果值有效返回true，否则返回false
     */
    public static boolean isValidValue(String value) {
        if (value == null) {
            return false;
        }
        return Arrays.stream(AftersalesReviewsOrderTypeEnum.values())
                .anyMatch(enumValue -> enumValue.getValue().equals(value));
    }

    public static List<Map<String, String>> getSelectOptions() {
        return Arrays.stream(AftersalesReviewsOrderTypeEnum.values())
                .map(enumValue -> Map.of("label", enumValue.getDesc(), "value", enumValue.getValue()))
                .collect(Collectors.toList());
    }

}
