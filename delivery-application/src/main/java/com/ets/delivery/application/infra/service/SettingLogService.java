package com.ets.delivery.application.infra.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.delivery.application.common.bo.SettingLogBO;
import com.ets.delivery.application.infra.entity.SettingLog;
import com.ets.delivery.application.infra.mapper.SettingLogMapper;
import org.springframework.stereotype.Service;

import jakarta.validation.Valid;
import java.time.LocalDateTime;

/**
 * <p>
 * 配置日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-24
 */
@Service
@DS("db-issuer-admin")
public class SettingLogService extends BaseService<SettingLogMapper, SettingLog> {

    public void addLog(@Valid SettingLogBO logBO) {
        SettingLog log = BeanUtil.copyProperties(logBO, SettingLog.class);
        log.setCreatedAt(LocalDateTime.now());
        log.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(log);
    }
}
