package com.ets.delivery.application.common.vo.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class StockOutDetailVO {

    /**
     * 入库单号
     */
    private String stockOutSn;

    /**
     * 库存编号
     */
    private String storageCode;

    /**
     * 实际入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime outTime;

    /**
     * 申请入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 收件人
     */
    private String receiveName;

    /**
     * 收件手机号
     */
    private String receivePhone;

    /**
     * 收件地区
     */
    private String receiveArea;

    /**
     * 收件地址
     */
    private String receiveAddress;

    /**
     * 送货方式
     */
    private String deliveryTypeStr;

    /**
     * 出库类型
     */
    private String typeStr;

    /**
     * 商品属性
     */
    private String goodsQualityStr;

    /**
     * 出库状态
     */
    private String statusStr;

    /**
     * 运单号
     */
    private String expressCode;

    /**
     * 图片
     */
    private List<String> imageList;

    /**
     * 商品聚合信息
     */
    private StockGoodsUnionVO goodsUnion;

    /**
     * 商品信息列表
     */
    private List<StockGoodsInfoVO> goodsInfo;

    /**
     * 日志列表
     */
    private List<StockLogVO> logList;

}
