package com.ets.delivery.application.common.config.jd;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "jd.express")
public class JdExpressConfig {
    String apiUrl;
    String accessToken;
    String appKey;
    String appSecret;
    String v;
    String customerCode;
    String departmentNo;
    String shopNo;
    String isvSource;
}
