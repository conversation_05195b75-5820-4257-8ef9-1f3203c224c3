package com.ets.delivery.application.common.consts.supplyGoods;

import com.ets.delivery.application.common.vo.SelectOptionsVO;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
public enum GoodsManufacturerEnum {

    UNKNOWN(0, "未知"),
    AITESI(1, "埃特斯"),
    JINYI(2, "金溢"),
    JULI(3, "聚力"),
    WANJI(4, "万集"),
    CHENGGU(5, "成谷"),
    YUNXINGYU(6, "云星宇");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    public static final List<Integer> list;

    GoodsManufacturerEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        GoodsManufacturerEnum[] enums = GoodsManufacturerEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Arrays.stream(enums).map(GoodsManufacturerEnum::getValue).collect(Collectors.toList());
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        List<SelectOptionsVO> selectOptionsVOList = new ArrayList<>();
        for (GoodsManufacturerEnum node : GoodsManufacturerEnum.values()) {
            selectOptionsVOList.add(new SelectOptionsVO(node.getValue().toString(),node.getDesc()));
        }

        return selectOptionsVOList;
    }
}
