package com.ets.delivery.application.common.consts.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserStatus {

    INACTIVE(0, "无效"),

    ACTIVE(10, "有效");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (UserStatus node : UserStatus.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
