package com.ets.delivery.application.app.thirdservice.response.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.*;
import java.util.List;

@Data
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.FIELD)
public class YundaInventoryQueryXmlVO {

    private String flag;
    private Integer code;
    private String message;

    @XmlElementWrapper(name = "items")
    @XmlElement(name = "item")
    private List<Item> item;

    @Data
    public static class Item {
        private String ownerCode;
        private String warehouseCode;
        private String itemCode;
        private String itemId;
        private String inventoryType;
        private Integer quantity;
        private Integer lockQuantity;
    }
}
