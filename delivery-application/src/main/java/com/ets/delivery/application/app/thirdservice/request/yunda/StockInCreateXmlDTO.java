package com.ets.delivery.application.app.thirdservice.request.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.*;
import java.util.List;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class StockInCreateXmlDTO {

    @XmlElement(name = "entryOrder")
    private EntryOrder entryOrder;

    @XmlElementWrapper(name = "orderLines")
    @XmlElement(name = "orderLine")
    private List<OrderLine> orderLines;

    @Data
    public static class EntryOrder {
        String entryOrderCode;
        String ownerCode;
        String warehouseCode;
        String orderType;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class OrderLine {
        String ownerCode;
        String itemCode;
        Integer planQty;
        String inventoryType;
    }

}
