package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.storage.StorageFactory;
import com.ets.delivery.application.app.thirdservice.business.WorkWeChatBusiness;
import com.ets.delivery.application.app.thirdservice.business.YundaBusiness;
import com.ets.delivery.application.app.thirdservice.request.yunda.DeliveryConfirmDTO;
import com.ets.delivery.application.app.thirdservice.request.yunda.DeliveryOrderQueryXmlDTO;
import com.ets.delivery.application.app.thirdservice.response.yunda.DeliveryOrderQueryVO;
import com.ets.delivery.application.app.thirdservice.response.yunda.DeliveryOrderQueryXmlVO;
import com.ets.delivery.application.common.config.WeChatRobotConfig;
import com.ets.delivery.application.common.config.yunda.YundaConfig;
import com.ets.delivery.application.common.consts.erpRecord.ErpRecordOrderSourceEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderConfirmDTO;
import com.ets.delivery.application.infra.entity.ErpRecord;
import com.ets.delivery.application.infra.service.ErpRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ErpOrderBusiness {

    @Autowired
    private WeChatRobotConfig weChatRobotConfig;

    @Autowired
    private YundaConfig yundaConfig;

    @Autowired
    private YundaBusiness yundaBusiness;

    @Autowired
    private WorkWeChatBusiness workWeChatBusiness;

    @Autowired
    private ErpRecordService erpRecordService;

    public void erpOrderDailyCheck(String orderSource, LocalDateTime startTime, LocalDateTime endTime) {
        List<String> erpSystemOrderSnList = new ArrayList<>();

        // 查询erp系统订单
        if (orderSource.equals(ErpRecordOrderSourceEnum.YUNDA.getValue())) {
            DeliveryOrderQueryXmlDTO queryXmlDTO = new DeliveryOrderQueryXmlDTO();
            queryXmlDTO.setStartTime(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            queryXmlDTO.setEndTime(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            queryXmlDTO.setOwnerCode(yundaConfig.getCustomerId());
            queryXmlDTO.setWarehouseCode(yundaConfig.getWarehouseCode());
            DeliveryOrderQueryVO queryVO = yundaBusiness.deliveryOrderQuery(queryXmlDTO);
            DeliveryOrderQueryXmlVO queryXmlVO = queryVO.getXmlVO();
            if (ObjectUtils.isEmpty(queryXmlVO) || queryXmlVO.getFlag().equals("failure")) {
                ToolsHelper.throwException("【" + orderSource + "】查询失败");
            }
            while (ObjectUtils.isNotEmpty(queryXmlVO.getOrder())) {
                queryXmlVO.getOrder().forEach(order -> {
                    // 过滤ERP订单
                    if (order.getDeliveryOrder().getSourceOrder() == 0) {
                        erpSystemOrderSnList.add(order.getDeliveryOrder().getDeliveryOrderCode());
                    }
                });
                queryXmlDTO.setPage(queryXmlDTO.getPage() + 1);
                queryVO = yundaBusiness.deliveryOrderQuery(queryXmlDTO);
                queryXmlVO = queryVO.getXmlVO();
            }
        }

        // 查询我方系统订单
        List<ErpRecord> erpRecordList = erpRecordService.getByDeliveryTimeRange(orderSource, startTime, endTime);
        List<String> erpRecordOrderSnList = erpRecordList.stream().map(ErpRecord::getErpSn).collect(Collectors.toList());
        log.info("【ERP订单日检】【{}】ERP订单量：{}，我方系统订单量：{}", orderSource, erpSystemOrderSnList.size(), erpRecordOrderSnList.size());

        // 数量不一致 逐一对比
        if (erpSystemOrderSnList.size() != erpRecordOrderSnList.size()) {
            log.error("【ERP订单日检】【{}】数量不一致，ERP订单量：{}，我方系统订单量：{}", orderSource, erpSystemOrderSnList.size(), erpRecordOrderSnList.size());
            StringBuilder markdown = new StringBuilder();
            markdown.append(String.format("【ERP订单日检】【%s】<font color=\"warning\">数量不一致</font>，" +
                    "ERP订单量：<font color=\"warning\">%d</font>，" +
                    "我方系统订单量：<font color=\"warning\">%d</font>\n",
                    orderSource, erpSystemOrderSnList.size(), erpRecordOrderSnList.size()));
            erpSystemOrderSnList.forEach(erpSystemOrderSn -> {
                if (!erpRecordOrderSnList.contains(erpSystemOrderSn)) {
                    log.error("【ERP订单日检】【{}】ERP流水号：{}，未在我方系统找到", orderSource, erpSystemOrderSn);
                    markdown.append("><font color=\"info\">ERP流水号</font>：").append(erpSystemOrderSn).append("，未在我方系统找到\n");
                }
            });

            erpRecordOrderSnList.forEach(erpRecordOrderSn -> {
                if (!erpSystemOrderSnList.contains(erpRecordOrderSn)) {
                    log.error("【ERP订单日检】【{}】ERP流水号：{}，未在erp系统找到", orderSource, erpRecordOrderSn);
                    markdown.append("><font color=\"info\">ERP流水号</font>：").append(erpRecordOrderSn).append("，未在erp系统找到\n");
                }
            });
            workWeChatBusiness.sendMarkdown(markdown.toString(), weChatRobotConfig.getErpOrderAlarmKey());
        }
    }

    public void fixErpOrder(String erpSn) {
        DeliveryOrderQueryXmlDTO queryXmlDTO = new DeliveryOrderQueryXmlDTO();
        queryXmlDTO.setOwnerCode(yundaConfig.getCustomerId());
        queryXmlDTO.setWarehouseCode(yundaConfig.getWarehouseCode());
        queryXmlDTO.setOrderCode(erpSn);
        DeliveryOrderQueryVO queryVO = yundaBusiness.deliveryOrderQuery(queryXmlDTO);
        DeliveryOrderQueryXmlVO queryXmlVO = queryVO.getXmlVO();
        if (ObjectUtils.isEmpty(queryXmlVO) || queryXmlVO.getFlag().equals("failure") || ObjectUtils.isEmpty(queryXmlVO.getOrder())) {
            ToolsHelper.throwException("查询失败");
        }

        // 数据处理
        DeliveryOrderQueryXmlVO.Order order = queryXmlVO.getOrder().get(0);
        DeliveryConfirmDTO deliveryConfirmDTO = BeanUtil.copyProperties(order, DeliveryConfirmDTO.class);

        LogisticsOrderConfirmDTO confirmDTO = new LogisticsOrderConfirmDTO();
        confirmDTO.setStorageCode(StorageCodeEnum.YUNDA.getValue());
        confirmDTO.setRemark("数据缺失查询补偿修复");
        confirmDTO.setOrderConfirmData(deliveryConfirmDTO);
        confirmDTO.setRawData(queryVO.getRawData());
        confirmDTO.setOrderSource(2);
        StorageFactory.create(StorageCodeEnum.YUNDA.getValue()).erpOrderConfirm(confirmDTO);
    }

    public void erpOrderAlarm(String content) {
        String markdown = String.format("【ERP订单】<font color=\"warning\">订单处理异常</font>\n %s", content);
        workWeChatBusiness.sendMarkdown(markdown, weChatRobotConfig.getErpOrderAlarmKey());
    }
}
