package com.ets.delivery.application.common.consts.erpRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ErpRecordOrderSourceEnum {

    YUNDA("Yunda", "韵达"),
    MAOCHAO("MaoChao", "猫超");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        map = Arrays.stream(ErpRecordOrderSourceEnum.values()).collect(Collectors.toMap(ErpRecordOrderSourceEnum::getValue, ErpRecordOrderSourceEnum::getDesc));
    }
}
