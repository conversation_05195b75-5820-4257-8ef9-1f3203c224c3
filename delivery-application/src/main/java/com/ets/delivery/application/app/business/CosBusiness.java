package com.ets.delivery.application.app.business;

import cn.hutool.core.util.ObjectUtil;
import com.ets.base.feign.feign.CosFeign;
import com.ets.base.feign.request.CosDTO;
import com.ets.base.feign.request.CosGetSignDTO;
import com.ets.base.feign.response.CosVO;
import com.ets.base.feign.response.PrivateCosVO;
import com.ets.common.JsonResult;
import com.ets.delivery.application.common.config.CosConfig;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.model.StorageClass;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.HashMap;

@Slf4j
@Component
public class CosBusiness {

    @Resource(name = "com.ets.base.feign.feign.CosFeign")
    CosFeign cosFeign;

    @Autowired
    private CosConfig cosConfig;

    public String getAccessUrl(String imgUrl) {

        CosGetSignDTO dto = new CosGetSignDTO();
        HashMap<String, String> urls = new HashMap<>();
        urls.put("url", imgUrl);

        dto.setUrls(urls);

        JsonResult<PrivateCosVO> result = cosFeign.getSignUrl(dto);

        return result.getDataWithCheckError().getUrls().get("url");
    }

    /**
     * <a href="https://cloud.tencent.com/document/product/460/6929">参数参考</a>
     * @param url 图片链接
     * @param mode 模式
     * @param width 宽度
     * @param length 高度
     * @return 缩略图url
     */
    public String getThumbnail(String url, int mode, int width, int length) {
        String params;
        if (url.contains("?")) {
            params = "&imageView2/" + mode + "/w/" + width + "/h/" + length;
        } else {
            params = "?imageView2/" + mode + "/w/" + width + "/h/" + length;
        }
        return url + params;
    }

    public String upload(byte[] bytes, String filePath) {
        // 1 初始化秘钥信息
        COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(cosConfig.getRegion()));
        // 3 生成cos客户端
        COSClient cosClient = new COSClient(cred, clientConfig);

        // 处理文件路径
        filePath = filePath.startsWith("/") ? filePath : "/" + filePath;

        // 判断文件大小（小文件上传建议不超过20M）
        int length = bytes.length;

        // 获取文件流
        InputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        // 从输入流上传必须制定content length, 否则http客户端可能会缓存所有数据，存在内存OOM的情况
        objectMetadata.setContentLength(length);

        PutObjectRequest putObjectRequest = new PutObjectRequest(cosConfig.getBucket(), filePath, byteArrayInputStream, objectMetadata);
        // 设置 Content type, 默认是 application/octet-stream
        putObjectRequest.setMetadata(objectMetadata);
        // 设置存储类型, 默认是标准(Standard), 低频(standard_ia)
        putObjectRequest.setStorageClass(StorageClass.Standard);
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);

        String eTag = putObjectResult.getETag();
        log.info(eTag);
        // 关闭客户端
        cosClient.shutdown();
        return cosConfig.getBaseUrl() + filePath;
    }
}
