package com.ets.delivery.application.app.thirdservice.response.jd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class JdCancelOrderVO extends JdErrorResponseVO {

    @JSONField(name = "jingdong_eclp_order_cancelOrder_responce")
    private JdCancelOrderResponseVO jdCancelOrderResponse;

    @Data
    public static class JdCancelOrderResponseVO {
        private String code;

        @JSONField(name = "cancelorder_result")
        private CancelOrderResult result;

        @Data
        public static class CancelOrderResult {
            private String msg;
            private Integer code;
        }
    }
}
