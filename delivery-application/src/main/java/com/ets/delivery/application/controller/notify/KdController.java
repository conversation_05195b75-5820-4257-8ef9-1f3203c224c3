package com.ets.delivery.application.controller.notify;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import com.ets.delivery.application.app.thirdservice.request.kd.KdExpressNotifyDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/notify/kd100")
public class KdController {

    @Autowired
    private ExpressBusiness expressBusiness;

    @RequestMapping("/expressNotify")
    public JsonResult<?> expressNotify(@RequestBody @Valid KdExpressNotifyDTO notifyDTO) {
        log.info("【物流轨迹推送】【快递100】{}", notifyDTO);

        try {
            ExpressNotifyDTO expressNotifyDTO = new ExpressNotifyDTO();
            expressNotifyDTO.setExpressCode(ExpressCodeEnum.KD100.getValue());
            expressNotifyDTO.setNotifyData(notifyDTO);

            expressBusiness.expressNotify(expressNotifyDTO);
        } catch (Exception e) {
            log.error("【物流轨迹推送】异常：{}", e.getMessage());
        }
        return JsonResult.ok();
    }
}
