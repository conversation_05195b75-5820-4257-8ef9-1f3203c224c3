package com.ets.delivery.application.common.consts.storage;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum StorageCodeEnum {

    YUNDA(StorageCodeConstant.YUNDA, "韵达"),
    JD_CLOUD(StorageCodeConstant.JD_CLOUD, "京东云仓"),
    NORMAL(StorageCodeConstant.NORMAL, "Normal"),
    AITESI("aitesi", "埃特斯"),
    JS_HUANHUO("JShuanhuo", "江苏仓");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;

    static {
        StorageCodeEnum[] enums = StorageCodeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Stream.of(enums)
                .map(StorageCodeEnum::getValue)
                .collect(Collectors.toList());
    }
}
