package com.ets.delivery.application.common.consts.rejectExpress;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum RejectExpressResultLevelEnum {

    LEVEL_ERROR(1, "失败"),
    LEVEL_WARNING(2, "警告"),
    LEVEL_NORMAL(3, "正常");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        RejectExpressResultLevelEnum[] enums = RejectExpressResultLevelEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
