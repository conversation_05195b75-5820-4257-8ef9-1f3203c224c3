package com.ets.delivery.application.common.dto.aftersalesreviews;

import com.ets.delivery.application.common.consts.aftersalesreviews.AftersalesReviewsOrderTypeEnum;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.Valid;
import lombok.Data;

/**
 * 创建售后审核单请求参数
 */
@Data
public class CreateAfterSalesReviewDTO {

    /**
     * 订单流水号
     */
    @NotBlank(message = "订单流水号不能为空")
    private String orderSn;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String orderType;

    /**
     * 发卡方id
     */
    @NotNull(message = "发卡方id不能为空")
    private Integer issuerId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long uid;

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空")
    private String plateNo;

    /**
     * 车牌颜色
     */
    @NotNull(message = "车牌颜色不能为空")
    private Integer plateColor;

    /**
     * 回调通知地址
     */
    private String notifyUrl;

    /**
     * 审核行驶证信息
     */
    @NotNull(message = "审核行驶证信息不能为空")
    @Valid
    private VehicleInfoDTO reviewVehicleInfo;

    /**
     * 订单行驶证信息
     */
    @NotNull(message = "订单行驶证信息不能为空")
    @Valid
    private VehicleInfoDTO orderVehicleInfo;

    /**
     * 校验业务类型是否为有效的枚举值
     * @return 如果业务类型有效返回true，否则返回false
     */
    @AssertTrue(message = "业务类型不正确")
    public boolean isValidOrderType() {
        return AftersalesReviewsOrderTypeEnum.isValidValue(this.orderType);
    }

    /**
     * 行驶证信息数据类型
     */
    @Data
    public static class VehicleInfoDTO {
        /**
         * 车牌号
         */
        private String plateNo;

        /**
         * 车牌颜色
         */
        private Integer plateColor;

        /**
         * 所有人
         */
        @Size(max = 255, message = "所有人字符长度过长")
        private String owner;

        /**
         * 车辆类型
         */
        @Size(max = 20, message = "车辆类型字符长度过长")
        private String type;

        /**
         * 使用性质
         */
        @Size(max = 50, message = "使用性质字符长度过长")
        private String useCharacter;

        /**
         * 核定载人数
         */
        @Size(max = 50, message = "核定载人数字符长度过长")
        private String passengers;

        /**
         * 行驶证正面
         */
        private String frontImgUrl;

        /**
         * 行驶证反面
         */
        private String backImgUrl;

        /**
         * 车头正面照
         */
        private String frontCarImgUrl;

        /**
         * 车内照
         */
        private String gearActivateImgUrl;
    }
}
