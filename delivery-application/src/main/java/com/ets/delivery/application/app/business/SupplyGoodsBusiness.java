package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.common.bo.GoodsLogBO;
import com.ets.delivery.application.common.consts.SupplyGoodsCacheKey;
import com.ets.delivery.application.common.consts.SupplyGoodsLogTypeEnum;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.supplyGoods.GoodsStatusEnum;
import com.ets.delivery.application.common.dto.supplyGoods.SupplyGoodsAddDTO;
import com.ets.delivery.application.common.dto.supplyGoods.SupplyGoodsChangeStatusDTO;
import com.ets.delivery.application.common.dto.supplyGoods.SupplyGoodsEditDTO;
import com.ets.delivery.application.common.dto.supplyGoods.SupplyGoodsListDTO;
import com.ets.delivery.application.common.vo.SupplyGoodsVO;
import com.ets.delivery.application.infra.entity.SupplyGoods;
import com.ets.delivery.application.infra.entity.User;
import com.ets.delivery.application.infra.service.SupplyGoodsLogService;
import com.ets.delivery.application.infra.service.SupplyGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

@Component
public class SupplyGoodsBusiness {

    @Autowired
    private SupplyGoodsService supplyGoodsService;

    @Autowired
    private SupplyGoodsLogService supplyGoodsLogService;

    @Resource(name = "defaultRedisTemplate")
    StringRedisTemplate redisTemplate;

    public IPage<SupplyGoodsVO> getList(SupplyGoodsListDTO listDTO) {
        return supplyGoodsService.getList(listDTO).convert(
                supplyGoods -> BeanUtil.copyProperties(supplyGoods, SupplyGoodsVO.class)
        );
    }

    public void addGoods(SupplyGoodsAddDTO addDTO) {
        if (!addDTO.checkGoodsType()) {
            ToolsHelper.throwException("商品类型不正确");
        }
        if (!addDTO.checkDeviceType()) {
            ToolsHelper.throwException("设备类型不正确");
        }
        if (!addDTO.checkManufacturer()) {
            ToolsHelper.throwException("设备厂商不正确");
        }

        // 检查goods_code是否已经存在
        SupplyGoods goodsExist = supplyGoodsService.getOneByGoodsCode(addDTO.getGoodsCode());
        if (ObjectUtil.isNotNull(goodsExist)) {
            ToolsHelper.throwException("已有相同商品代码存在");
        }

        // 新增记录
        SupplyGoods goods = new SupplyGoods();
        BeanUtil.copyProperties(addDTO, goods);
        goods.setStatus(GoodsStatusEnum.STATUS_NORMAL.getValue());
        goods.setCreatedAt(LocalDateTime.now());
        goods.setUpdatedAt(LocalDateTime.now());
        supplyGoodsService.save(goods);

        // 记录日志
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        GoodsLogBO logBO = new GoodsLogBO();
        logBO.setSupplyGoodsId(goods.getId());
        logBO.setOperator(user.getUsername());
        logBO.setType(SupplyGoodsLogTypeEnum.TYPE_ADD.getValue());
        logBO.setOperateContent(user.getUsername() + SupplyGoodsLogTypeEnum.TYPE_ADD.getDesc() + JSON.toJSONString(addDTO));
        supplyGoodsLogService.addLog(logBO);

        // 清除缓存
        String cacheKey = SupplyGoodsCacheKey.getCodeNameListCacheKey(addDTO.getManufacturer(), addDTO.getDeviceType());
        redisTemplate.delete(cacheKey);
    }

    public void editGoods(SupplyGoodsEditDTO editDTO) {
        SupplyGoods goods = supplyGoodsService.getById(editDTO.getId());
        if (ObjectUtil.isNull(goods)) {
            ToolsHelper.throwException("商品id不存在");
        }

        if (!editDTO.checkGoodsType()) {
            ToolsHelper.throwException("商品类型不正确");
        }
        if (!editDTO.checkDeviceType()) {
            ToolsHelper.throwException("设备类型不正确");
        }
        if (!editDTO.checkManufacturer()) {
            ToolsHelper.throwException("设备厂商不正确");
        }

        // 检查goods_code是否已经存在
        SupplyGoods goodsExist = supplyGoodsService.getOneByGoodsCode(editDTO.getGoodsCode());
        if (ObjectUtil.isNotNull(goodsExist) && !goodsExist.getId().equals(editDTO.getId())) {
            ToolsHelper.throwException("已有相同商品代码存在");
        }

        // 修改记录
        BeanUtil.copyProperties(editDTO, goods);
        goods.setUpdatedAt(LocalDateTime.now());
        supplyGoodsService.updateById(goods);

        // 记录日志
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        GoodsLogBO logBO = new GoodsLogBO();
        logBO.setSupplyGoodsId(editDTO.getId());
        logBO.setOperator(user.getUsername());
        logBO.setType(SupplyGoodsLogTypeEnum.TYPE_MODIFY.getValue());
        logBO.setOperateContent(user.getUsername() + SupplyGoodsLogTypeEnum.TYPE_MODIFY.getDesc() + JSON.toJSONString(editDTO));
        supplyGoodsLogService.addLog(logBO);

        // 清除缓存
        String cacheKey = SupplyGoodsCacheKey.getCodeNameListCacheKey(editDTO.getManufacturer(), editDTO.getDeviceType());
        redisTemplate.delete(cacheKey);
    }

    public void changeStatus(SupplyGoodsChangeStatusDTO changeStatusDTO) {
        if (!changeStatusDTO.checkStatus()) {
            ToolsHelper.throwException("状态不正确");
        }

        SupplyGoods goods = supplyGoodsService.getById(changeStatusDTO.getId());
        if (ObjectUtil.isNull(goods)) {
            ToolsHelper.throwException("商品不存在");
        }

        if (goods.getStatus().equals(changeStatusDTO.getStatus())) {
            return;
        }

        // 修改状态
        goods.setStatus(changeStatusDTO.getStatus());
        goods.setUpdatedAt(LocalDateTime.now());
        supplyGoodsService.updateById(goods);

        // 记录日志
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        GoodsLogBO logBO = new GoodsLogBO();
        logBO.setSupplyGoodsId(changeStatusDTO.getId());
        logBO.setOperator(user.getUsername());
        logBO.setType(SupplyGoodsLogTypeEnum.TYPE_MODIFY.getValue());
        logBO.setOperateContent(user.getUsername() + "修改状态为：" + GoodsStatusEnum.map.get(changeStatusDTO.getStatus()));
        supplyGoodsLogService.addLog(logBO);

        // 清除缓存
        String cacheKey = SupplyGoodsCacheKey.getCodeNameListCacheKey(goods.getManufacturer(), goods.getDeviceType());
        redisTemplate.delete(cacheKey);
    }
}
