package com.ets.delivery.application.app.factory.express.impl;

import cn.hutool.core.convert.ConverterRegistry;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.business.PickUpBusiness;
import com.ets.delivery.application.app.thirdservice.business.JdCloudBusiness;
import com.ets.delivery.application.app.thirdservice.business.WorkWeChatBusiness;
import com.ets.delivery.application.app.thirdservice.request.jd.JdExpressNotifyDTO;
import com.ets.delivery.application.app.thirdservice.request.jd.JdExpressTraceGetDTO;
import com.ets.delivery.application.app.thirdservice.response.jd.JdExpressTraceGetVO;
import com.ets.delivery.application.common.bo.PickUpLogBO;
import com.ets.delivery.application.common.bo.SendBackLogBO;
import com.ets.delivery.application.common.bo.express.ExpressBO;
import com.ets.delivery.application.common.bo.express.ExpressDataBO;
import com.ets.delivery.application.common.bo.express.ExpressLogBO;
import com.ets.delivery.application.common.config.WeChatRobotConfig;
import com.ets.delivery.application.common.config.jd.JdConfig;
import com.ets.delivery.application.common.config.jd.JdExpressConfig;
import com.ets.delivery.application.common.consts.sendback.SendBackLogTypeEnum;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeStatusEnum;
import com.ets.delivery.application.common.consts.jd.JdOpeTitleEnum;
import com.ets.delivery.application.common.consts.jd.JdTradeNodeExpressEnum;
import com.ets.delivery.application.common.consts.jd.JdTradeNodePickupEnum;
import com.ets.delivery.application.common.consts.pickUp.PickUpLogTypeEnum;
import com.ets.delivery.application.common.consts.pickUp.PickUpStatusEnum;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import com.ets.delivery.application.infra.entity.Express;
import com.ets.delivery.application.infra.entity.LogisticsSendBack;
import com.ets.delivery.application.infra.entity.Pickup;
import com.ets.delivery.application.infra.service.*;
import com.ets.redisson.template.DistributedLockTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class JdCloudExpressManage extends ExpressBase {

    @Autowired
    private JdConfig jdConfig;

    @Autowired
    private JdExpressConfig jdExpressConfig;

    @Autowired
    private WeChatRobotConfig wechatRobotConfig;

    @Autowired
    private JdCloudBusiness jdCloudBusiness;

    @Autowired
    private ExpressBusiness expressBusiness;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private ExpressLogService expressLogService;

    @Autowired
    private PickUpBusiness pickUpBusiness;

    @Autowired
    private PickupService pickupService;

    @Autowired
    private PickupLogService pickupLogService;

    @Autowired
    private LogisticsSendBackService sendBackService;

    @Autowired
    private LogisticsSendbackLogService sendbackLogService;

    @Autowired
    private DistributedLockTemplate distributedLockTemplate;

    @Autowired
    private WorkWeChatBusiness workWeChatBusiness;

    @Override
    public Express expressNotify(ExpressNotifyDTO notifyDTO) {
        JdExpressNotifyDTO jdExpressNotifyDTO = ConverterRegistry.getInstance().convert(JdExpressNotifyDTO.class, notifyDTO.getNotifyData());

        String body = jdExpressNotifyDTO.getRequest_body();
        JdExpressNotifyDTO.RequestBody requestBody = JSON.parseObject(body, JdExpressNotifyDTO.RequestBody.class);

        return expressNotifyByRequestBody(requestBody);
    }

    public Express expressNotifyByRequestBody(JdExpressNotifyDTO.RequestBody requestBody) {

        if (ObjectUtils.isEmpty(requestBody)) {
            ToolsHelper.throwException("推送内容格式不正确");
        }

        // 过滤京东仓发货轨迹
        String prefix = requestBody.getOrderId().substring(0, 3);
        if (prefix.equals("ESL")) {
            return null;
        }

        String lockKey = "delivery:express:jd:" + requestBody.getWaybillCode();
        return distributedLockTemplate.tryLock(lockKey, 10, 20, TimeUnit.SECONDS, () -> {
            // 取件状态推送
            updatePickUp(requestBody);

            // 物流轨迹推送
            return updateExpress(requestBody);
        });
    }

    @Override
    public Express expressQuery(Express express) {
        JdExpressTraceGetDTO traceGetDTO = new JdExpressTraceGetDTO();
        traceGetDTO.setCustomerCode(jdExpressConfig.getCustomerCode());
        traceGetDTO.setWaybillCode(express.getExpressNumber());

        JdExpressTraceGetVO traceGetVO = jdCloudBusiness.traceGet(traceGetDTO);
        if (ObjectUtils.isNotEmpty(traceGetVO.getJdExpressTraceGetResponse())) {
            JdExpressTraceGetVO.JdTraceGetResponseVO.QueryTraceResult result = traceGetVO.getJdExpressTraceGetResponse().getResult();

            // 有历史记录数据
            List<ExpressDataBO> expressDataList = new ArrayList<>();
            if (StringUtils.isNotEmpty(express.getData())) {
                expressDataList = JSON.parseObject(express.getData(), new TypeReference<List<ExpressDataBO>>() {});
            }

            // 有新的物流轨迹
            if (ObjectUtils.isNotEmpty(result.getData()) && expressDataList.size() != result.getData().size()) {

                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                // 格式化物流轨迹
                List<ExpressDataBO> newExpressDataList = new ArrayList<>();
                result.getData().forEach(trace -> {
                    ExpressDataBO expressDataBO = new ExpressDataBO();
                    expressDataBO.setStatus(trace.getOpeTitle());
                    expressDataBO.setContext(trace.getOpeRemark());
                    expressDataBO.setFtime(LocalDateTime.parse(trace.getOpeTime(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")).format(dtf));
                    newExpressDataList.add(expressDataBO);

                    // 包含退回状态 标记状态和时间
                    Integer state = JdOpeTitleEnum.stateMap.getOrDefault(trace.getOpeTitle(), ExpressStateEnum.ON_THE_WAY.getValue());
                    if (Arrays.asList(ExpressStateEnum.SEND_BACK.getValue(),
                            ExpressStateEnum.RECEIVER_REJECT.getValue(),
                            ExpressStateEnum.SEND_ANOTHER.getValue()).contains(state)) {
                        express.setIsBack(1);
                        express.setBackTime(LocalDateTime.parse(trace.getOpeTime(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
                    }
                });

                // 按时间倒序
                newExpressDataList.sort((t1, t2) -> t2.getFtime().compareTo(t1.getFtime()));
                String dataList = JSON.toJSONString(newExpressDataList);

                // 最初状态
                ExpressDataBO firstExpress = newExpressDataList.get(newExpressDataList.size() - 1);
                if (StringUtils.isEmpty(express.getFirstContext()) || !express.getFirstStatus().equals(firstExpress.getStatus())) {
                    express.setFirstStatus(firstExpress.getStatus());
                    express.setFirstContext(firstExpress.getContext());
                    express.setFirstExpressTime(LocalDateTime.parse(firstExpress.getFtime(), dtf));
                }

                // 最新状态
                ExpressDataBO lastExpress = newExpressDataList.get(0);
                if (StringUtils.isEmpty(express.getLastContext()) || !express.getLastStatus().equals(lastExpress.getStatus())) {
                    express.setLastStatus(lastExpress.getStatus());
                    express.setLastContext(lastExpress.getContext());
                    express.setLastExpressTime(LocalDateTime.parse(lastExpress.getFtime(), dtf));
                }

                express.setData(dataList);
                express.setState(JdOpeTitleEnum.stateMap.getOrDefault(lastExpress.getStatus(), ExpressStateEnum.ON_THE_WAY.getValue()));
                express.setSubscribeStatus(JdOpeTitleEnum.subscribeStatusMap.getOrDefault(lastExpress.getStatus(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue()));

                // 处理 拒收报废 情况
                if (lastExpress.getStatus().equals(JdOpeTitleEnum.EXPRESS_REJECT.getValue()) &&
                        lastExpress.getContext().contains("报废")) {
                    express.setSubscribeStatus(ExpressSubscribeStatusEnum.SUBSCRIBE_ABORT.getValue());
                }

                // 签收时间
                if (lastExpress.getStatus().equals(JdOpeTitleEnum.EXPRESS_DELIVERED.getValue())) {
                    express.setReceivedTime(LocalDateTime.parse(lastExpress.getFtime(), dtf));
                }

                express.setUpdatedAt(LocalDateTime.now());
                expressService.updateById(express);

                // 记录日志
                ExpressLogBO logBO = new ExpressLogBO();
                logBO.setExpressSn(express.getExpressSn());
                logBO.setExpressNumber(express.getExpressNumber());
                logBO.setSubscribeStatus(express.getSubscribeStatus());
                logBO.setContent("物流轨迹定时查询修改:" + JSON.toJSONString(express));
                expressLogService.addLog(logBO);
            }
        }
        return express;
    }

    private void updatePickUp(JdExpressNotifyDTO.RequestBody requestBody) {
        if (JdTradeNodePickupEnum.list.contains(requestBody.getTraceNode())) {
            String orderSn = requestBody.getOrderId();
            Pickup pickup = pickupService.getOneByPickUpSn(orderSn);
            if (ObjectUtils.isEmpty(pickup)) {
                ToolsHelper.throwException("取件单不存在");
            }

            // 更新状态
            Integer newPickUpStatus = JdTradeNodePickupEnum.statusMap.getOrDefault(requestBody.getTraceNode(),
                    PickUpStatusEnum.STATUS_PICKUP_ERROR.getValue());
            if (newPickUpStatus >= pickup.getPickupStatus()) {
                pickup.setPickupStatus(newPickUpStatus);
            }
            pickup.setPickupRemark(requestBody.getTraceMark());
            pickup.setUpdatedAt(LocalDateTime.now());


            // 提取收件配送员信息
            if (requestBody.getTraceNode().equals(JdTradeNodePickupEnum.PICKUP_ASSIGN.getValue())) {
                String pattern = "员：([\\u4e00-\\u9fa5]+)已出发，手机号：(\\d+)";
                Pattern r = Pattern.compile(pattern);
                Matcher m = r.matcher(requestBody.getTraceMark());
                if (m.find()) {
                    String name = m.group(1);
                    String mobile = m.group(2);
                    pickup.setPickupName(name);
                    pickup.setPickupMobile(mobile);
                }
            }

            // 提取取件运单号、取件完成时间
            if (requestBody.getTraceNode().equals(JdTradeNodePickupEnum.PICKUP_FINISH.getValue())) {
                String pattern = "取件完成,运单号:([A-Za-z0-9]+)";
                Pattern r = Pattern.compile(pattern);
                Matcher m = r.matcher(requestBody.getTraceMark());
                if (m.find()) {
                    String expressNumber = m.group(1);
                    pickup.setExpressCorp("京东物流");
                    pickup.setExpressNumber(expressNumber);
                }
                pickup.setPickupTime(LocalDateTime.parse(requestBody.getOperateTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                // 更新寄回件快递单号
                LogisticsSendBack sendBack = sendBackService.getUnfinishedOrder(pickup.getOrderSn());
                if (ObjectUtils.isNotEmpty(sendBack)) {
                    sendBack.setExpressNumber(pickup.getExpressNumber());
                    sendBack.setExpressCorp(pickup.getExpressCorp());
                    sendBackService.updateById(sendBack);

                    // 记录日志
                    SendBackLogBO logBO = new SendBackLogBO();
                    logBO.setSendbackId(sendBack.getId());
                    logBO.setOperator("system");
                    logBO.setType(SendBackLogTypeEnum.TYPE_MODIFY.getValue());
                    logBO.setOperateContent("上门取件更新寄回快递信息，快递单号" + sendBack.getExpressNumber() + "，快递公司" + sendBack.getExpressCorp());
                    sendbackLogService.addLog(logBO);
                }
            }

            // 取件妥投
            if (requestBody.getTraceNode().equals(JdTradeNodePickupEnum.PICKUP_DELIVERED.getValue())) {
                // 缺少运单号 发出预警
                if (ObjectUtils.isEmpty(pickup.getExpressNumber())) {
                    String markdown = String.format(
                            "【上门取件】<font color=\"warning\">状态推送异常</font>\n " +
                            "><font color=\"info\">取件单号</font>：%s\n" +
                            "><font color=\"info\">通知节点</font>：%s\n" +
                            "><font color=\"warning\">异常原因</font>：快递单号缺失\n",
                            requestBody.getWaybillCode(),
                            requestBody.getTraceNode());
                    workWeChatBusiness.sendMarkdown(markdown, wechatRobotConfig.getPickUpAlarmKey());
                }
            }
            pickupService.updateById(pickup);

            // 记录日志
            PickUpLogBO logBO = new PickUpLogBO();
            logBO.setPickupId(pickup.getId())
                .setType(PickUpLogTypeEnum.NOTIFY.getValue())
                .setOperateContent(requestBody.getTraceMark());
            pickupLogService.addLog(logBO);

            // 通知
            pickUpBusiness.sendPickUpNotify(pickup);
        }
    }

    private Express updateExpress(JdExpressNotifyDTO.RequestBody requestBody) {
        if (JdTradeNodeExpressEnum.list.contains(requestBody.getTraceNode())) {
            Express express = expressService.getOneByExpressNumber(requestBody.getWaybillCode());

            // 初始化物流信息
            if (ObjectUtils.isEmpty(express)) {
                ExpressBO expressBO = new ExpressBO();
                expressBO.setExpressCode(ExpressCodeEnum.JD_CLOUD.getValue());
                expressBO.setExpressNumber(requestBody.getWaybillCode());
                expressBO.setExpressCompany("jd");
                expressBO.setPhone(jdConfig.getReceiverPhone());
                express = expressBusiness.initExpress(expressBO);
            }

            if (ObjectUtils.isNotEmpty(express)) {
                // 有历史记录数据
                List<ExpressDataBO> expressDataList = new ArrayList<>();
                if (StringUtils.isNotEmpty(express.getData())) {
                    expressDataList = JSON.parseObject(express.getData(), new TypeReference<List<ExpressDataBO>>() {});
                }

                // 格式化数据
                ExpressDataBO expressData = new ExpressDataBO();
                expressData.setFtime(requestBody.getOperateTime());
                expressData.setContext(requestBody.getTraceMark());
                expressData.setStatus(requestBody.getTraceNode());
                expressDataList.add(0, expressData);
                expressDataList.sort((t1, t2) -> t2.getFtime().compareTo(t1.getFtime()));
                String dataList = JSON.toJSONString(expressDataList);

                Integer state = JdTradeNodeExpressEnum.stateMap.getOrDefault(requestBody.getTraceNode(),
                        ExpressStateEnum.ON_THE_WAY.getValue());
                express.setData(dataList);
                express.setState(state);
                express.setSubscribeStatus(JdTradeNodeExpressEnum.subscribeStatusMap.getOrDefault(requestBody.getTraceNode(),
                        ExpressSubscribeStatusEnum.SUBSCRIBING.getValue()));

                // 最早状态
                LocalDateTime operateTime = LocalDateTime.parse(requestBody.getOperateTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                if (StringUtils.isEmpty(express.getFirstContext())) {
                    express.setFirstStatus(requestBody.getTraceNode());
                    express.setFirstContext(requestBody.getTraceMark());
                    express.setFirstExpressTime(operateTime);
                }

                // 最新状态
                express.setLastStatus(requestBody.getTraceNode());
                express.setLastContext(requestBody.getTraceMark());
                express.setLastExpressTime(operateTime);

                // 签收时间
                if (requestBody.getTraceNode().equals(JdTradeNodeExpressEnum.EXPRESS_DELIVERED.getValue())) {
                    express.setReceivedTime(operateTime);
                }

                // 退回、拒收标记
                if (Arrays.asList(ExpressStateEnum.SEND_BACK.getValue(),
                        ExpressStateEnum.RECEIVER_REJECT.getValue(),
                        ExpressStateEnum.SEND_ANOTHER.getValue()).contains(state)) {
                    express.setIsBack(1);
                    express.setBackTime(operateTime);
                }

                // 更新物流状态
                express.setUpdatedAt(LocalDateTime.now());
                expressService.updateById(express);

                // 记录日志
                ExpressLogBO logBO = new ExpressLogBO();
                logBO.setExpressSn(express.getExpressSn());
                logBO.setExpressNumber(express.getExpressNumber());
                logBO.setSubscribeStatus(express.getSubscribeStatus());
                logBO.setContent("物流订阅回调修改:" + JSON.toJSONString(express));
                expressLogService.addLog(logBO);
            }

            return express;
        }
        return null;
    }
}
