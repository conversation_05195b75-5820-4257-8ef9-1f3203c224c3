package com.ets.delivery.application.common.dto.stock;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class StockInCreateDTO {

    /**
     * 仓库编号
     */
    @NotBlank(message = "请选择仓库")
    private String storageCode;

    /**
     * 入库类型
     */
    @NotNull(message = "请选择入库类型")
    private Integer type;

    /**
     * 商品属性
     */
    @NotNull(message = "请选择商品属性")
    private Integer goodsQuality;

    /**
     * 备注
     */
    private String remark;

    /**
     * 图片，逗号分隔
     */
    private String images;

    /**
     * 商品信息
     */
    @NotNull(message = "请填写商品信息")
    private @Valid List<StockGoodsInfoDTO> goodsInfo;

}
