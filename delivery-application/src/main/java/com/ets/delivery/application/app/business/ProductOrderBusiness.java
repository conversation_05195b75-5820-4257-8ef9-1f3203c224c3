package com.ets.delivery.application.app.business;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.ApplyFeign;
import com.ets.delivery.application.app.thirdservice.request.apply.ProductOrderExternalCreateDTO;
import com.ets.delivery.application.common.bo.productOrder.CreateOrderFromExternalBO;
import com.ets.delivery.application.common.vo.productOrder.ProductOrderExternalCreateVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component(value = "ProductOrderBusiness")
public class ProductOrderBusiness {

    @Autowired
    private ApplyFeign applyFeign;

    public List<ProductOrderExternalCreateVO> createOrderFromExternal(CreateOrderFromExternalBO bo) {

        List<ProductOrderExternalCreateVO> result = new ArrayList<>();

        int j = 0;
        for (CreateOrderFromExternalBO.Item item: bo.getItems()) {

            if (bo.getItems().size() > 1) {
                j++;
            }

            String originThirdOrderSn = StringUtils.isNotEmpty(item.getThirdOrderSn())
                    ? item.getThirdOrderSn() : bo.getThirdOrderSn();

            String thirdOrderSn = j > 0 ? originThirdOrderSn + "_" + j : originThirdOrderSn;

            BigDecimal paidAmount = bo.getPaidAmount();

            if (item.getCount() > 1) {

                if (item.getItemAmount() != null) {
                    paidAmount = item.getItemAmount().divide(BigDecimal.valueOf(item.getCount()), BigDecimal.ROUND_HALF_UP);
                }

                // 循环
                for (int i=1;i<=item.getCount();i++) {

                    ProductOrderExternalCreateVO vo = createOrderOne(bo, thirdOrderSn + "_" + i, item.getPackageSn(), paidAmount);

                    result.add(vo);
                }
            } else {
                if (item.getItemAmount() != null) {
                    paidAmount = item.getItemAmount();
                }

                ProductOrderExternalCreateVO vo = createOrderOne(bo, thirdOrderSn, item.getPackageSn(), paidAmount);

                result.add(vo);
            }
        }

        return result;
    }

    public ProductOrderExternalCreateVO createOrderOne(CreateOrderFromExternalBO bo, String thirdOrderSn, String packageSn, BigDecimal paidAmount) {

        ProductOrderExternalCreateDTO dto = new ProductOrderExternalCreateDTO();

        dto.setThirdOrderSn(thirdOrderSn);

        dto.setSendPhone(bo.getPhone());
        dto.setEtcPhone(bo.getPhone());

        dto.setPaidAmount(paidAmount);
        dto.setSendName(bo.getSendName());
        dto.setSendArea(bo.getSendArea());
        if (StringUtils.isEmpty(bo.getSendAddress())) {
            // 申办时替换成身份证上的地址
            dto.setSendAddress("****");
        } else {
            dto.setSendAddress(bo.getSendAddress());
        }
        dto.setLogisticCompany(bo.getLogisticCompany());
        dto.setLogisticNumber(bo.getLogisticNumber());
        dto.setSendTime(bo.getSendTime());

        dto.setPackageSn(packageSn);

        JsonResult<ProductOrderExternalCreateVO> result = applyFeign.createOrderFromExternal(dto);

        return result.getDataWithCheckError();
    }

}
