package com.ets.delivery.application.common.consts.yunda;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum YundaInventoryTypeEnum {

    ZP("ZP", "正品"),
    CC("CC", "残次"),
    JS("JS", "机损"),
    XS("XS", " 箱损"),
    ZT("ZT", "在途库存");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    
    YundaInventoryTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    
    static {
        YundaInventoryTypeEnum[] enums = YundaInventoryTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
