package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 审核行驶证数据
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_reviews_vehicles")
public class ReviewsVehicles extends BaseEntity<ReviewsVehicles> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审核单sn
     */
    private String reviewSn;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 1、蓝色 2、绿色 3、黄色
     */
    private Integer plateColor;

    /**
     * 车身颜色
     */
    private String bodyColor;

    /**
     * 车辆类型
     */
    private String type;

    /**
     * 所有人
     */
    private String owner;

    /**
     * 住址
     */
    private String address;

    /**
     * 使用性质
     */
    private String useCharacter;

    /**
     * 品牌型号
     */
    private String model;

    /**
     * 车辆识别代码
     */
    private String vin;

    /**
     * 发动机号码
     */
    private String engineNo;

    /**
     * 注册日期
     */
    private String registerDate;

    /**
     * 发证日期
     */
    private String issueDate;

    /**
     * 核定载人数
     */
    private String passengers;

    /**
     * 车身长度（单位：mm）
     */
    private Integer carLength;

    /**
     * 车身宽度（单位：mm）
     */
    private Integer carWidth;

    /**
     * 车身高度（单位：mm）
     */
    private Integer carHeight;

    /**
     * 红章
     */
    private String redChapter;

    /**
     * 档案编号
     */
    private String fileNumber;

    /**
     * 整备质量
     */
    private String weight;

    /**
     * 总质量
     */
    private String totalWeight;

    /**
     * 备注
     */
    private String remark;

    /**
     * 检验记录
     */
    private String record;

    /**
     * 核定载质量
     */
    private String carryWeight;

    /**
     * 准牵引总质量
     */
    private String tractionWeight;

    /**
     * 车轴数
     */
    private Integer axles;

    /**
     * 行驶证正面
     */
    private String frontImgUrl;

    /**
     * 行驶证反面
     */
    private String backImgUrl;

    /**
     * 车头照
     */
    private String carImgUrl;

    /**
     * 补充照
     */
    private String supplementImgUrl;

    /**
     * 道路运输证
     */
    private String roadTransportImgUrl;

    /**
     * 货车类型：0非货车1普通货车2牵引车集装箱
     */
    private Integer truckType;

    /**
     * 经营范围:0无经营范围给1非货物专用运输车辆 2专用集装箱运输车辆 3混用集装箱运输车辆
     */
    private Integer businessScope;

    /**
     * 业务类型：0默认为空1货车运政车
     */
    private Integer businessType;

    /**
     * 使用性质枚举值[1-营运 2-非营运 3-普通货运（江苏） 4-专用集装箱（江苏） 5-混用集装箱（江苏）]
     */
    private Integer useCharacterType;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
