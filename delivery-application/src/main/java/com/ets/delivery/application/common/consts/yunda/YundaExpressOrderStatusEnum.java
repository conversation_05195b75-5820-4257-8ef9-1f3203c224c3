package com.ets.delivery.application.common.consts.yunda;

import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsExpressStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum YundaExpressOrderStatusEnum {

    WAIT("待揽收", LogisticsExpressStatusEnum.SENT.getValue(), ExpressStateEnum.DEFAULT.getValue(), "待揽收"),
    GOT("已揽收", LogisticsExpressStatusEnum.PICKED.getValue(), ExpressStateEnum.ACCEPT.getValue(), "已揽收"),
    TRANSIT("运输中", LogisticsExpressStatusEnum.TRANSIT.getValue(), ExpressStateEnum.ON_THE_WAY.getValue(), "运输中"),
    DELIVERY("派件中", LogisticsExpressStatusEnum.DELIVERY.getValue(), ExpressStateEnum.SENDING.getValue(), "派件中"),
    SIGNED("已签收", LogisticsExpressStatusEnum.SIGNED.getValue(), ExpressStateEnum.RECEIVED.getValue(), "已签收"),
    DELIVER_EXCEPTION("问题件", LogisticsExpressStatusEnum.EXCEPTION.getValue(), ExpressStateEnum.ON_TROUBLE.getValue(), "问题件"),
    RETURN("退回", LogisticsExpressStatusEnum.EXCEPTION.getValue(), ExpressStateEnum.SEND_BACK.getValue(), "退回"),
    OTHER("其他", LogisticsExpressStatusEnum.EXCEPTION.getValue(), ExpressStateEnum.OTHER.getValue(), "其他");


    private final String value;
    private final Integer status;
    private final Integer state;
    private final String desc;
    public static final Map<String, String> map;
    public static final Map<String, Integer> statusMap;
    public static final Map<String, Integer> stateMap;

    static {
        map = Arrays.stream(YundaExpressOrderStatusEnum.values())
                .collect(Collectors.toMap(YundaExpressOrderStatusEnum::getValue, YundaExpressOrderStatusEnum::getDesc));
        statusMap = Arrays.stream(YundaExpressOrderStatusEnum.values())
                .collect(Collectors.toMap(YundaExpressOrderStatusEnum::getValue, YundaExpressOrderStatusEnum::getStatus));
        stateMap = Arrays.stream(YundaExpressOrderStatusEnum.values())
                .collect(Collectors.toMap(YundaExpressOrderStatusEnum::getValue, YundaExpressOrderStatusEnum::getState));
    }
}
