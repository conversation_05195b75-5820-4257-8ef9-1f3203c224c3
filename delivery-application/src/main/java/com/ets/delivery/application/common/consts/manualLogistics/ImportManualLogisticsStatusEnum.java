package com.ets.delivery.application.common.consts.manualLogistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum ImportManualLogisticsStatusEnum {

    NORMAL(1, "正常"),
    DELETED(-1, "删除");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        ImportManualLogisticsStatusEnum[] enums = ImportManualLogisticsStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
