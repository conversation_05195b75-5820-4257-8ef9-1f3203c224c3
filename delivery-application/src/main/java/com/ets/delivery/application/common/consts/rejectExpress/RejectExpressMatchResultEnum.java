package com.ets.delivery.application.common.consts.rejectExpress;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum RejectExpressMatchResultEnum {

    DEFAULT(0, ""),
    MATCH(1, "回寄快递单成功关联业务订单"),
    NOT_MATCH(2, "回寄快递单号无关联业务订单");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        RejectExpressMatchResultEnum[] enums = RejectExpressMatchResultEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
