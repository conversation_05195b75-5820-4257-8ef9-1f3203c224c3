package com.ets.delivery.application.common.consts.logistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum LogisticsOrderTypeEnum {

    APPLY("apply", "申办"),
    AFTER_SALES_RETURN("after_sales_return", "售后退货"),
    AFTER_SALES_CANCEL("after_sales_cancel", "售后注销"),
    AFTER_SALES_MAINTAIN_EXCHANGE("after_sales_maintain_exchange", "维修换货"),
    AFTER_SALES_MAINTAIN_EXCHANGE_APPLY("after_sales_maintain_exchange_apply", "维修换货-未激活"),
    AFTER_SALES_EXCHANGE("after_sales_exchange", "售后换货"),
    AFTER_SALES_EXCHANGE_APPLY("after_sales_exchange_apply", "售后换货-未激活"),
    AFTER_SALES_RECALL_EXCHANGE("after_sales_recall_exchange", "召回换货"),
    REAPPLY("reapply", "补办"),
    JD_APPLY("jd_apply", "商城订单"),
    SHOP_AFTER_SALES("shop_after_sales", "商城订单售后"),
    REISSUE_LOST("reissue_lost", "补发-丢件"),
    REISSUE_HISTORY("reissue_history", "补发-历史发货失败"),
    REISSUE_REAPPLY("reissue_reapply", "补发-补办"),
    REISSUE_MAINTAIN_EXCHANGE("reissue_maintain_exchange", "补发-维修换货"),
    REISSUE_APPLY("reissue_apply", "补发-申办"),
    OFFLINE("offline", "地推"),
    UPGRADE("upgrade", "设备升级"),
    GOODS("goods", "商品订单"),
    MANUAL("manual", "手动下单");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        LogisticsOrderTypeEnum[] enums = LogisticsOrderTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
