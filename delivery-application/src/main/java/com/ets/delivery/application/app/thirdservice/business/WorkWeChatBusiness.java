package com.ets.delivery.application.app.thirdservice.business;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.thirdservice.feign.WorkWeChatFeign;
import com.ets.delivery.application.app.thirdservice.request.workWeChat.WorkWeChatSendDTO;
import com.ets.delivery.application.app.thirdservice.response.workWeChat.WorkWeChatSendVO;
import com.ets.delivery.application.app.thirdservice.response.workWeChat.WorkWeChatUploadMediaVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

@Slf4j
@Component
public class WorkWeChatBusiness {

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Autowired
    private WorkWeChatFeign workWeChatFeign;

    public void sendFile(File file, String key) throws IOException {
        WorkWeChatSendDTO sendDTO = new WorkWeChatSendDTO();
        sendDTO.setMsgType("file");

        // 上传文件
        FileInputStream input = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile("media", file.getName(), ContentType.MULTIPART_FORM_DATA.getMimeType(), IOUtils.toByteArray(input));
        String result = workWeChatFeign.uploadMedia(multipartFile, key, "file");
        WorkWeChatUploadMediaVO uploadMediaVO = JSON.parseObject(result, WorkWeChatUploadMediaVO.class);
        if (ObjectUtil.isEmpty(uploadMediaVO)) {
            ToolsHelper.throwException("【企业微信机器人】上传结果解析失败");
        }
        if (uploadMediaVO.getErrCode() != 0) {
            ToolsHelper.throwException("【企业微信机器人】上传文件失败：" + uploadMediaVO.getErrMsg());
        }
        String mediaId = uploadMediaVO.getMediaId();

        // 组装参数
        WorkWeChatSendDTO.File sendFile = new WorkWeChatSendDTO.File();
        sendFile.setMediaId(mediaId);
        sendDTO.setFile(sendFile);

        String json = JSON.toJSONString(sendDTO);
        WorkWeChatSendVO sendVO = workWeChatFeign.send(json, key);
        if (sendVO.getErrcode() != 0) {
            ToolsHelper.throwException("【企业微信机器人】发送文件失败：" + sendVO.getErrmsg());
        }
    }

    public void sendMarkdown(String content, String key) {
        try {
            WorkWeChatSendDTO sendDTO = new WorkWeChatSendDTO();
            sendDTO.setMsgType("markdown");

            WorkWeChatSendDTO.Markdown markdown = new WorkWeChatSendDTO.Markdown();
            markdown.setContent("【" + ACTIVE + "环境】" + content);
            sendDTO.setMarkdown(markdown);

            String json = JSON.toJSONString(sendDTO);
            WorkWeChatSendVO sendVO = workWeChatFeign.send(json, key);
            if (sendVO.getErrcode() != 0) {
                ToolsHelper.throwException("【企业微信机器人】发送Markdown失败：" + sendVO.getErrmsg());
            }
        } catch (Throwable e) {
            log.warn("企微发送markdown预警异常：", e);
        }
    }
}
