package com.ets.delivery.application.app.business.stock;

import com.alibaba.fastjson.JSON;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.thirdservice.request.yunda.StockInConfirmDTO;
import com.ets.delivery.application.app.thirdservice.request.yunda.StockOutConfirmDTO;
import com.ets.delivery.application.common.bo.stock.StockInExtraBO;
import com.ets.delivery.application.common.bo.stock.StockOutExtraBO;
import com.ets.delivery.application.common.consts.stock.StockInStatusEnum;
import com.ets.delivery.application.common.consts.stock.StockOutStatusEnum;
import com.ets.delivery.application.common.consts.stock.StockTypeEnum;
import com.ets.delivery.application.infra.entity.StockInOrder;
import com.ets.delivery.application.infra.entity.StockOutOrder;
import com.ets.delivery.application.infra.service.StockGoodsInfoService;
import com.ets.delivery.application.infra.service.StockInOrderService;
import com.ets.delivery.application.infra.service.StockLogService;
import com.ets.delivery.application.infra.service.StockOutOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class StockNotifyBusiness {

    @Autowired
    private StockLogService stockLogService;

    @Autowired
    private StockInOrderService stockInOrderService;

    @Autowired
    private StockOutOrderService stockOutOrderService;

    @Autowired
    private StockGoodsInfoService goodsInfoService;

    public void stockInNotify(StockInConfirmDTO dto) {

        if (dto.getEntryOrder() == null) {
            ToolsHelper.throwException("entryOrder不能为空");
        }

        String stockSn = dto.getEntryOrder().getEntryOrderCode();
        StockInOrder inOrder = stockInOrderService.getBySn(stockSn);
        if (inOrder == null) {
            return;
        }

        StockInStatusEnum statusEnum = StockInStatusEnum.getByYunDaCode(dto.getEntryOrder().getStatus());
        if (statusEnum == null) {
            return;
        }

        StockInExtraBO extraBO = inOrder.getExtraBO();
        if (extraBO.getBizCodeList().contains(dto.getEntryOrder().getOutBizCode())) {
            // 防重复调用
            return;
        }

        String inTime = "";
        String countInfo = "";
        if (statusEnum.equals(StockInStatusEnum.FULFILLED) || statusEnum.equals(StockInStatusEnum.PARTFULFILLED) ) {
            inTime = dto.getEntryOrder().getOperateTime();

            // 商品实际数量更新
            for (StockInConfirmDTO.OrderLine orderLine: dto.getOrderLines()) {
                goodsInfoService.updateRealCount(stockSn, orderLine.getItemCode(), StockTypeEnum.IN.getCode(), orderLine.getActualQty());
                countInfo = countInfo + " " +
                        (StringUtils.isNotEmpty(orderLine.getItemName()) ? orderLine.getItemName() : orderLine.getItemCode())
                        + ":" + orderLine.getActualQty();
            }
        }

        extraBO.getBizCodeList().add(dto.getEntryOrder().getOutBizCode());
        String extra = JSON.toJSONString(extraBO);

        stockInOrderService.updateStatus(stockSn, statusEnum.getCode(), inTime, extra);

        String logContent = statusEnum.getDescription() + countInfo;

        stockLogService.addLog(
                stockSn, StockTypeEnum.IN, statusEnum.getCode(), logContent, "系统"
        );
    }

    public void stockOutNotify(StockOutConfirmDTO dto) {

        if (dto.getDeliveryOrder() == null) {
            ToolsHelper.throwException("deliveryOrder不能为空");
        }

        String stockSn = dto.getDeliveryOrder().getDeliveryOrderCode();
        StockOutOrder outOrder = stockOutOrderService.getBySn(stockSn);
        if (outOrder == null) {
            return;
        }

        StockOutStatusEnum statusEnum = StockOutStatusEnum.getByYunDaCode(dto.getDeliveryOrder().getStatus());
        if (statusEnum == null) {
            return;
        }

        StockOutExtraBO extraBO = outOrder.getExtraBO();

        if (extraBO.getBizCodeList().contains(dto.getDeliveryOrder().getOutBizCode())) {
            // 防重复调用
            return;
        }

        String outTime = "";
        String countInfo = "";
        String expressCode = "";
        if (statusEnum.equals(StockOutStatusEnum.DELIVERED) || statusEnum.equals(StockOutStatusEnum.PARTDELIVERED)) {
            outTime = StringUtils.isNotEmpty(dto.getDeliveryOrder().getOrderConfirmTime()) ? dto.getDeliveryOrder().getOrderConfirmTime() : ToolsHelper.getDateTime();

            // 商品实际数量更新
            for (StockOutConfirmDTO.OrderLine orderLine: dto.getOrderLines()) {
                goodsInfoService.updateRealCount(stockSn, orderLine.getItemCode(), StockTypeEnum.OUT.getCode(), orderLine.getActualQty());
                countInfo = countInfo + " " +
                        (StringUtils.isNotEmpty(orderLine.getItemName()) ? orderLine.getItemName() : orderLine.getItemCode())
                        + ":" + orderLine.getActualQty();
            }

            if (StringUtils.isNotEmpty(dto.getDeliveryOrder().getExpressCode())) {
                expressCode = dto.getDeliveryOrder().getExpressCode();
            }

            if (dto.getPackageList() != null) {
                for (StockOutConfirmDTO.DeliveryPackage deliveryPackage: dto.getPackageList()) {
                    if (StringUtils.isEmpty(expressCode)) {
                        expressCode = deliveryPackage.getExpressCode();
                    } else {
                        expressCode = expressCode + "," + deliveryPackage.getExpressCode();
                    }
                }
            }
        }

        extraBO.getBizCodeList().add(dto.getDeliveryOrder().getOutBizCode());
        String extra = JSON.toJSONString(extraBO);

        stockOutOrderService.updateStatus(stockSn, statusEnum.getCode(), outTime, expressCode, extra);

        String logContent = statusEnum.getDescription() + countInfo;

        stockLogService.addLog(
                stockSn, StockTypeEnum.OUT, statusEnum.getCode(), logContent, "系统"
        );
    }

}
