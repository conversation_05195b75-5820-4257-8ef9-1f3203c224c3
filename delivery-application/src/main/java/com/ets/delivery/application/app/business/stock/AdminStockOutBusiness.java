package com.ets.delivery.application.app.business.stock;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.BeanHelper;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.BaseBusiness;
import com.ets.delivery.application.app.business.storageMina.StorageBusiness;
import com.ets.delivery.application.app.thirdservice.business.YundaBusiness;
import com.ets.delivery.application.app.thirdservice.request.yunda.StockCancelXmlDTO;
import com.ets.delivery.application.app.thirdservice.request.yunda.StockOutCreateXmlDTO;
import com.ets.delivery.application.app.thirdservice.response.yunda.StockResponseVO;
import com.ets.delivery.application.common.config.yunda.YundaConfig;
import com.ets.delivery.application.common.consts.stock.*;
import com.ets.delivery.application.common.dto.stock.*;
import com.ets.delivery.application.common.utils.AdminUserUtil;
import com.ets.delivery.application.common.utils.StockOutUtil;
import com.ets.delivery.application.common.vo.stock.*;
import com.ets.delivery.application.infra.entity.StockOutOrder;
import com.ets.delivery.application.infra.entity.Storage;
import com.ets.delivery.application.infra.relation.StockOutBindGoodsInfoRelation;
import com.ets.delivery.application.infra.service.StockGoodsInfoService;
import com.ets.delivery.application.infra.service.StockLogService;
import com.ets.delivery.application.infra.service.StockOutOrderService;
import com.ets.delivery.application.infra.service.StorageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class AdminStockOutBusiness {

    @Autowired
    private BaseBusiness baseBusiness;

    @Autowired
    private StockOutOrderService stockOutOrderService;

    @Autowired
    private AdminStockBusiness adminStockBusiness;

    @Autowired
    private StockGoodsInfoService goodsInfoService;

    @Autowired
    private StorageBusiness storageBusiness;

    @Autowired
    private YundaBusiness yundaBusiness;

    @Autowired
    private YundaConfig yundaConfig;

    @Autowired
    private StorageService storageService;

    @Autowired
    private StockLogService stockLogService;

    public void exportFile(StockOutListDTO dto, HttpServletResponse response) {

        // 获取数据
        List<StockOutListVO> exportList = getList(dto).getRecords();

        try {
            // 设置文本内省
            response.setContentType("application/vnd.ms-excel");
            // 设置字符编码
            response.setCharacterEncoding("utf-8");
            // 设置文件名
            String filename = URLEncoder.encode("出库单导出结果" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename);
            EasyExcel.write(response.getOutputStream(), StockOutExportVO.class)
                    .sheet("导出结果")
                    .doWrite(exportList);

        } catch (IOException e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            ToolsHelper.throwException("导出文件失败");
        }
    }

    public IPage<StockOutListVO> getList(StockOutListDTO dto) {
        // 分页设置
        IPage<StockOutOrder> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);
        // 查询条件设置
        LambdaQueryWrapper<StockOutOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(dto.getStockOutSn()), StockOutOrder::getStockOutSn, dto.getStockOutSn())
                .eq(dto.getStatus() != null, StockOutOrder::getStatus, dto.getStatus())
                .eq(dto.getType() != null, StockOutOrder::getType, dto.getType())
                .eq(dto.getGoodsQuality() != null, StockOutOrder::getGoodsQuality, dto.getGoodsQuality())
                .gt(StringUtils.isNotEmpty(dto.getApplyTimeBegin()), StockOutOrder::getApplyTime, dto.getApplyTimeBegin())
                .lt(StringUtils.isNotEmpty(dto.getApplyTimeEnd()), StockOutOrder::getApplyTime, dto.getApplyTimeEnd() + " 23:59:59")
                .gt(StringUtils.isNotEmpty(dto.getOutTimeBegin()), StockOutOrder::getOutTime, dto.getOutTimeBegin())
                .lt(StringUtils.isNotEmpty(dto.getOutTimeEnd()), StockOutOrder::getOutTime, dto.getOutTimeEnd() + " 23:59:59")
                .orderByDesc(StockOutOrder::getCreatedAt);

        if (StringUtils.isNotEmpty(dto.getDeliveryGoodsCode())) {

            List<String> stockSnList = adminStockBusiness.getStockSnListByGoods(dto.getDeliveryGoodsCode(), StockTypeEnum.OUT);
            if (stockSnList == null || stockSnList.isEmpty()) {
                return null;
            }

            wrapper.in(StockOutOrder::getStockOutSn, stockSnList);
        }

        IPage<StockOutOrder> pageList = stockOutOrderService.getPageListByWrapper(oPage, wrapper);
        if (pageList.getSize() == 0) {
            return null;
        }

        // 绑定goodsInfo
        goodsInfoService.bindListToMasterEntityList(pageList.getRecords(), StockOutBindGoodsInfoRelation.class);

        Map<String, Storage> storageMap = storageBusiness.getListMap();

        IPage<StockOutListVO> list = pageList.convert(entity -> {

            StockOutListVO vo = BeanUtil.copyProperties(entity, StockOutListVO.class);

            vo.setStorageName(storageMap.get(entity.getStorageCode()).getName());
            vo.setTypeStr(StockOutTypeEnum.getDescByCode(entity.getType()));
            vo.setGoodsQualityStr(StockGoodsQualityEnum.getDescByCode(entity.getGoodsQuality()));
            vo.setStatusStr(StockOutStatusEnum.getDescByCode(entity.getStatus()));
            vo.setAllowCancel(StockOutUtil.allowCancelStatus().contains(entity.getStatus()));
            vo.setAllowEdit(! StockOutUtil.notAllowEditStatus().contains(entity.getStatus()));
            vo.setApplyTime(ToolsHelper.localDateTimeToString(entity.getApplyTime()));
            vo.setOutTime(ToolsHelper.localDateTimeToString(entity.getOutTime()));

            StockGoodsUnionInfoVO unionInfoVO = adminStockBusiness.getGoodsUnionInfo(entity.getGoodsInfo());
            vo.setGoodsNameInfo(unionInfoVO.getGoodsNameInfo());
            vo.setGoodsApplyCount(unionInfoVO.getGoodsApplyCount());
            vo.setGoodsRealCount(unionInfoVO.getGoodsRealCount());

            return vo;
        });

        return list;
    }

    public StockOutDataVO getData(StockOutSnDTO dto) {

        StockOutOrder outOrder = getBySn(dto.getStockOutSn());

        StockOutDataVO vo = new StockOutDataVO();
        vo.setOrder(outOrder);

        vo.setGoodsInfo(adminStockBusiness.getGoodsInfoList(dto.getStockOutSn(), StockTypeEnum.OUT));

        return vo;
    }

    public static void checkArea(String area) {

        if (StringUtils.isEmpty(area)) {
            return;
        }

        String[] sendAreas = area.trim().split("\\s+");
        if(sendAreas.length != 3){
            ToolsHelper.throwException("收件地区格式不正确（省 市 区）");
        }
    }

    public void checkShipParam(String area, String address, String consignee, String phone) {

        if (StringUtils.isEmpty(area)) {
            ToolsHelper.throwException("收货地区不能为空");
        }
        checkArea(area);

        if (StringUtils.isEmpty(address)) {
            ToolsHelper.throwException("详细地址不能为空");
        }

        if (StringUtils.isEmpty(consignee)) {
            ToolsHelper.throwException("联系人不能为空");
        }

        if (StringUtils.isEmpty(phone)) {
            ToolsHelper.throwException("收货手机号不能为空");
        }
    }

    public String makeStockOutSn(Integer goodsQuality) {

        String stockOutSn = goodsQuality.equals(StockGoodsQualityEnum.GOOD.getCode()) ? "LC" : "CC";

        stockOutSn = stockOutSn + baseBusiness.getDate() + baseBusiness.getSnNumber("stockOutSnNumber", 3);

        return stockOutSn;
    }

    @DSTransactional
    public StockOutOrder create(StockOutCreateDTO dto) {

        // 送货方式为快递时，需检测快递信息字段
        if (dto.getDeliveryType().equals(StockDeliveryTypeEnum.EXPRESS.getCode())) {
            checkShipParam(dto.getReceiveArea(), dto.getReceiveAddress(), dto.getReceiveName(), dto.getReceivePhone());
        }

        StockOutTypeEnum typeEnum = StockOutTypeEnum.getByCode(dto.getType());
        if (typeEnum == null) {
            ToolsHelper.throwException("不支持的类型");
        }

        StockOutOrder outOrder = BeanHelper.copy(StockOutOrder.class, dto);
        outOrder.setStockOutSn(makeStockOutSn(dto.getGoodsQuality()));
        outOrder.setOperator(AdminUserUtil.getOperatorName());

        stockOutOrderService.create(outOrder);

        List<StockOutCreateXmlDTO.OrderLine> orderLines = new ArrayList<>();

        dto.getGoodsInfo().forEach(stockGoodsInfoDTO -> {

            adminStockBusiness.createGoodsInfo(stockGoodsInfoDTO, StockTypeEnum.OUT, outOrder.getStockOutSn(), dto.getStorageCode());

            StockOutCreateXmlDTO.OrderLine orderLine = new StockOutCreateXmlDTO.OrderLine();
            orderLine.setOwnerCode(yundaConfig.getCustomerId());
            orderLine.setItemCode(stockGoodsInfoDTO.getStorageSku());
            orderLine.setPlanQty(stockGoodsInfoDTO.getApplyCount());
            orderLine.setInventoryType(StockGoodsQualityEnum.getInventoryTypeByCode(dto.getGoodsQuality()));

            orderLines.add(orderLine);
        });

        // 调韵达接口
        StockOutCreateXmlDTO xmlDTO = new StockOutCreateXmlDTO();
        StockOutCreateXmlDTO.DeliveryOrder deliveryOrder = new StockOutCreateXmlDTO.DeliveryOrder();
        deliveryOrder.setDeliveryOrderCode(outOrder.getStockOutSn());
        deliveryOrder.setLogisticsCode("SF");
        deliveryOrder.setOrderType(typeEnum.getYunDaCode());
        deliveryOrder.setWarehouseCode(yundaConfig.getWarehouseCode());
        deliveryOrder.setCreateTime(ToolsHelper.getDateTime());
        deliveryOrder.setTransportMode(StockDeliveryTypeEnum.getDescByCode(outOrder.getDeliveryType()));

        if (outOrder.getDeliveryType().equals(StockDeliveryTypeEnum.EXPRESS.getCode())) {
            StockOutCreateXmlDTO.ReceiverInfo receiverInfo = new StockOutCreateXmlDTO.ReceiverInfo();
            String[] sendAreas = dto.getReceiveArea().trim().split("\\s+");
            receiverInfo.setProvince(sendAreas[0]);
            receiverInfo.setCity(sendAreas[1]);
            receiverInfo.setArea(sendAreas[2]);
            receiverInfo.setDetailAddress(dto.getReceiveAddress());
            receiverInfo.setName(dto.getReceiveName());
            receiverInfo.setMobile(dto.getReceivePhone());

            deliveryOrder.setReceiverInfo(receiverInfo);
        }

        xmlDTO.setDeliveryOrder(deliveryOrder);

        xmlDTO.setOrderLines(orderLines);

        StockResponseVO response = yundaBusiness.stockOutCreate(xmlDTO);

        stockOutOrderService.applySuccess(outOrder.getStockOutSn(), response);

        return outOrder;
    }

    public StockOutOrder getBySn(String stockOutSn) {

        StockOutOrder outOrder = stockOutOrderService.getBySn(stockOutSn);
        if (outOrder == null) {
            ToolsHelper.throwException("入库单不存在");
        }

        return outOrder;
    }

    public void edit(StockOutEditDTO dto) {

        stockOutOrderService.edit(dto);

        // 商品信息号段修改
        if (dto.getGoodsEditInfo() != null) {
            dto.getGoodsEditInfo().forEach(editInfo -> {
                adminStockBusiness.updateNumber(editInfo.getId(), editInfo.getNumberInfoList());
            });
        }
    }

    public StockOutDetailVO detail(StockOutSnDTO dto) {

        StockOutOrder outOrder = getBySn(dto.getStockOutSn());

        StockOutDetailVO vo = BeanHelper.copy(StockOutDetailVO.class, outOrder);
        vo.setTypeStr(StockOutTypeEnum.getDescByCode(outOrder.getType()));
        vo.setGoodsQualityStr(StockGoodsQualityEnum.getDescByCode(outOrder.getGoodsQuality()));
        vo.setStatusStr(StockOutStatusEnum.getDescByCode(outOrder.getStatus()));
        vo.setImageList(outOrder.getImageList());
        vo.setDeliveryTypeStr(StockDeliveryTypeEnum.getDescByCode(outOrder.getDeliveryType()));
        vo.setStorageCode(storageService.getStorageNameByCode(outOrder.getStorageCode()));

        StockGoodsUnionVO unionVO = new StockGoodsUnionVO();
        vo.setGoodsInfo(adminStockBusiness.getGoodsInfoVOList(dto.getStockOutSn(), StockTypeEnum.OUT, unionVO));
        vo.setGoodsUnion(unionVO);

        vo.setLogList(adminStockBusiness.getLogList(dto.getStockOutSn(), StockTypeEnum.OUT));

        return vo;
    }

    public void cancel(StockOutSnDTO dto) {

        StockOutOrder outOrder = getBySn(dto.getStockOutSn());

        if (outOrder.getStatus().equals(StockOutStatusEnum.CANCELED.getCode())) {
            return;
        }

        if (! StockOutUtil.allowCancelStatus().contains(outOrder.getStatus())) {
            ToolsHelper.throwException("货物已出库，不能取消");
        }

        // 调韵达接口取消入库
        StockCancelXmlDTO xmlDTO = new StockCancelXmlDTO();
        xmlDTO.setOwnerCode(yundaConfig.getCustomerId());
        xmlDTO.setWarehouseCode(yundaConfig.getWarehouseCode());
        xmlDTO.setOrderCode(dto.getStockOutSn());
        xmlDTO.setOrderType(StockOutTypeEnum.getByCode(outOrder.getType()).getYunDaCode());
        yundaBusiness.stockCancel(xmlDTO);

        stockOutOrderService.cancel(dto.getStockOutSn());

        stockLogService.addLog(outOrder.getStockOutSn(), StockTypeEnum.OUT, StockOutStatusEnum.CANCELED.getCode(), "出库单取消", RequestHelper.getAdminOperator());
    }

}
