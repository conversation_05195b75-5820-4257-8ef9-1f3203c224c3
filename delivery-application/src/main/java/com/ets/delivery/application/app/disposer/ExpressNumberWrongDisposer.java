package com.ets.delivery.application.app.disposer;

import cn.hutool.core.convert.ConverterRegistry;
import com.ets.common.JsonResult;
import com.ets.common.queue.BaseDisposer;
import com.ets.delivery.application.app.business.SendBackBusiness;
import com.ets.delivery.application.app.thirdservice.feign.RefundFeign;
import com.ets.delivery.application.common.bo.SendBackLogBO;
import com.ets.delivery.application.common.bo.express.ExpressNumberWrongEventBO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.consts.sendback.SendBackLogTypeEnum;
import com.ets.delivery.application.common.vo.SendBackInfoVO;
import com.ets.delivery.application.infra.service.LogisticsSendbackLogService;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@NoArgsConstructor
@Component(value = "ExpressNumberWrongJobBean")
public class ExpressNumberWrongDisposer extends BaseDisposer {

    @Autowired
    DeliveryConfig deliveryConfig;

    @Autowired
    SendBackBusiness sendBackBusiness;

    @Autowired
    LogisticsSendbackLogService sendbackLogService;

    @Autowired
    RefundFeign refundFeign;

    public ExpressNumberWrongDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "ExpressNumberWrongJobBean";
    }

    @Override
    public void execute(Object content) throws Exception {

        ExpressNumberWrongEventBO eventBO = ConverterRegistry.getInstance().convert(ExpressNumberWrongEventBO.class, content);

        // 获取寄回件订单
        SendBackInfoVO sendBackInfoVO = sendBackBusiness.getOrderInfoByLogisticsSn(eventBO.getOrderSn());

        if (ObjectUtils.isNotEmpty(sendBackInfoVO)) {
            // 限制订单类型
            if (!deliveryConfig.getExpressNumberWrongOrderTypeList().contains(sendBackInfoVO.getOrderType())) {
                return;
            }

            try {
                // 通知售后
                JsonResult<?> result = refundFeign.subscribeFailed(sendBackInfoVO.getOrderSn());
                result.checkError();

                // 记录日志
                SendBackLogBO logBO = new SendBackLogBO();
                logBO.setSendbackId(sendBackInfoVO.getId());
                logBO.setOperator("system");
                logBO.setType(SendBackLogTypeEnum.TYPE_CHECK.getValue());
                logBO.setOperateContent("通知业务方物流订阅失败，快递单号：" + sendBackInfoVO.getExpressNumber());
                sendbackLogService.addLog(logBO);
            } catch (Throwable e) {
                // 记录日志
                log.error("【物流订阅失败通知】错误：{}", e.getLocalizedMessage());

                SendBackLogBO logBO = new SendBackLogBO();
                logBO.setSendbackId(sendBackInfoVO.getId());
                logBO.setOperator("system");
                logBO.setType(SendBackLogTypeEnum.TYPE_CHECK.getValue());
                logBO.setOperateContent("通知业务方请求失败");
                sendbackLogService.addLog(logBO);
            }
        }
    }
}
