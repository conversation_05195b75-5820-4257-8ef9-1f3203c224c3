package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.common.consts.stock.StockTypeEnum;
import com.ets.delivery.application.infra.entity.StockLog;
import com.ets.delivery.application.infra.mapper.StockLogMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 库存处理记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Service
@DS("db-issuer-admin-minor")
public class StockLogService extends BaseService<StockLogMapper, StockLog> {

    @Override
    public int updateByWrapper(LambdaUpdateWrapper<StockLog> lambdaUpdateWrapper) {
        lambdaUpdateWrapper.set(StockLog::getUpdatedAt, ToolsHelper.getDateTime());

        return super.updateByWrapper(lambdaUpdateWrapper);
    }

    public List<StockLog> getList(String stockSn, Integer stockType) {

        LambdaQueryWrapper<StockLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockLog::getStockSn, stockSn)
                .eq(StockLog::getType, stockType);

        return getListByWrapper(wrapper);
    }

    public void addLog(
            String stockSn,
            StockTypeEnum stockTypeEnum,
            Integer operateType,
            String content,
            String operator
    ) {
        StockLog stockLog = new StockLog();
        stockLog.setStockSn(stockSn);
        stockLog.setType(stockTypeEnum.getCode());
        stockLog.setOperateType(operateType);
        stockLog.setContent(content);
        stockLog.setOperator(operator);

        create(stockLog);
    }

}
