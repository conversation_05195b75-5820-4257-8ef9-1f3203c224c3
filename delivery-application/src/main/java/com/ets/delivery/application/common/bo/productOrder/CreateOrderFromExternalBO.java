package com.ets.delivery.application.common.bo.productOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CreateOrderFromExternalBO {

    /**
     * 外部订单号
     */
    private String thirdOrderSn;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 实际销售价格
     */
    private BigDecimal paidAmount;

    /**
     * 收货人姓名
     */
    private String sendName;

    /**
     * 省 市 区；空格分隔
     */
    private String sendArea;

    /**
     * 详细地址，如果不准确则不要传该值
     */
    private String sendAddress;

    /**
     * 快递公司
     */
    private String logisticCompany;

    /**
     * 快递单号
     */
    private String logisticNumber;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;

    /**
     * 商品信息
     */
    private List<Item> items;

    @Data
    public static class Item {

        /**
         * 产品包编号
         */
        private String packageSn;

        /**
         * 数量
         */
        private Integer count;

        /**
         * 商品金额
         */
        private BigDecimal itemAmount;

        /**
         * 渠道订单号
         */
        private String thirdOrderSn;

    }

}
