package com.ets.delivery.application.controller.admin;


import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.SettingBusiness;
import com.ets.delivery.application.common.dto.CategoryMapDTO;
import com.ets.delivery.application.common.dto.setting.SettingChangeStatusDTO;
import com.ets.delivery.application.common.dto.setting.SettingEditDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Map;

@RestController
@RequestMapping("/admin/setting")
public class SettingController {

    @Autowired
    private SettingBusiness settingBusiness;

    @RequestMapping("/getCategory")
    public JsonResult<Map<String, CategoryMapDTO>> getCategory() {
        return JsonResult.ok(settingBusiness.getCategory());
    }

    @RequestMapping("/changeStatus")
    public JsonResult<?> changeStatus(@Valid @RequestBody SettingChangeStatusDTO changeStatusDTO) {
        settingBusiness.changeStatus(changeStatusDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/edit")
    public JsonResult<?> edit(@Valid @RequestBody SettingEditDTO editDTO) {
        settingBusiness.editSetting(editDTO);
        return JsonResult.ok();
    }
}
