package com.ets.delivery.application.common.consts;

public class StockAlarmConstant {
    // 线程池常量: 最小数
    public static final Integer POOR_MIN_NUM = 5;

    // 线程池：最大数
    public static final Integer POOR_MAX_NUM = 100;

    // 每个线程预配置内存
    public static final Integer CACHE_SIZE = 1024;

    public static final long STOCK_ALARM_CACHE_TIME = 2 * 3600;

    public static String getLogisticsAverageCacheKey(String goodsCode, Integer beforeDay, Integer avgDay) {
        return "StockAlarm:LogisticsAverage:" + goodsCode + ":" + beforeDay.toString() + ":" + avgDay.toString();
    }

    public static String getGoodsStockCacheKey(String storageCode, String goodsCode) {
        return "StockAlarm:GoodsStock:" + storageCode + ":" + goodsCode;
    }
}
