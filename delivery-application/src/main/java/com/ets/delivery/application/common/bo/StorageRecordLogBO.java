package com.ets.delivery.application.common.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Accessors(chain = true)
@Data
public class StorageRecordLogBO {
    @NotNull(message = "入库记录id不能为空")
    private Integer recordId;

    @NotBlank(message = "操作人不能为空")
    private String operator = "system";

    @NotBlank(message = "操作类型不能为空")
    private String type;

    @NotBlank(message = "操作内容不能为空")
    private String operateContent;
}
