package com.ets.delivery.application.common.dto.postReviews;

import com.ets.delivery.application.common.consts.EmergencyTypeEnum;
import lombok.Data;

@Data
public class PostReviewBatchReceiveDTO {

    /**
     * 是否正序
     */
    private String sort = "asc";

    /**
     * 后审类型
     */
    private Integer emergencyType;

    public Integer getEmergencyType() {
        return emergencyType != null && emergencyType.equals(EmergencyTypeEnum.NO_NEED.getValue()) ? null : emergencyType;
    }
}
