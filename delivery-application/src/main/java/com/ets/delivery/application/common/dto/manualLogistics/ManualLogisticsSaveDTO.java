package com.ets.delivery.application.common.dto.manualLogistics;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.util.List;

@Data
public class ManualLogisticsSaveDTO {

    /**
     * 原发货流水号
     */
    private String originOrderSn;

    /**
     * 所属仓储
     */
    @NotEmpty(message = "发货仓库不能为空")
    private String storageCode;

    /**
     * 指定发货物流编码
     */
    @NotEmpty(message = "发货物流不能为空")
    private String logisticsCode;

    @NotEmpty(message = "商品不能为空")
    @Size(max = 20,  message = "发货商品数量不能超过20个")
    @Valid
    private List<Goods> goodsList;

    /**
     * 收件人
     */
    @NotEmpty(message = "收货人姓名不能为空")
    @Size(max = 20, message = "收件人姓名不能超过20个字符")
    private String sendName;

    /**
     * 联系手机
     */
    @NotEmpty(message = "收件人电话不能为空")
    private String sendPhone;

    /**
     * 发货地区
     */
    @NotEmpty(message = "收件人地区不能为空")
    private String sendArea;

    /**
     * 收货地址
     */
    @NotEmpty(message = "收件人地址不能为空")
    private String sendAddress;

    /**
     * 下单原因
     */
    @NotEmpty(message = "下单原因不能为空")
    private String reason;

    /**
     * 下单备注
     */
    @Size(max = 100, message = "备注不能超过100字符")
    private String remark;

    @Data
    public static class Goods {

        /**
         * 商品编码
         */
        @NotEmpty(message = "商品编码不能为空")
        private String sku;

        /**
         * 商品名称
         */
        @NotEmpty(message = "商品名称不能为空")
        private String goodsName;

        /**
         * 发货数量
         */
        @NotNull(message = "发货数量不能为空")
        @Min(value = 1, message = "发货数量最少1件")
        @Max(value = 1000, message = "发货数量不能超过1000件")
        private Integer nums;
    }


}
