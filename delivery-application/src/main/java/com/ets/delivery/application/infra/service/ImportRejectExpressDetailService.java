package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.consts.rejectExpress.RejectExpressRecordStatusEnum;
import com.ets.delivery.application.common.dto.rejectExpress.RejectExpressImportListDTO;
import com.ets.delivery.application.infra.entity.ImportRejectExpressDetail;
import com.ets.delivery.application.infra.mapper.ImportRejectExpressDetailMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 导入拒收新旧快递单号记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Service
@DS("db-issuer-admin")
public class ImportRejectExpressDetailService extends BaseService<ImportRejectExpressDetailMapper, ImportRejectExpressDetail> {

    public IPage<ImportRejectExpressDetail> getPage(RejectExpressImportListDTO listDTO) {
        Wrapper<ImportRejectExpressDetail> wrapper = Wrappers.<ImportRejectExpressDetail>lambdaQuery()
                .eq(ImportRejectExpressDetail::getBatchNo, listDTO.getBatchNo())
                .orderByAsc(ImportRejectExpressDetail::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }

    public ImportRejectExpressDetail getOneBySendBackExpressNumber(String sendBackExpressNumber) {
        Wrapper<ImportRejectExpressDetail> wrapper = Wrappers.<ImportRejectExpressDetail>lambdaQuery()
                .eq(ImportRejectExpressDetail::getSendbackExpressNumber, sendBackExpressNumber)
                .eq(ImportRejectExpressDetail::getRecordStatus, RejectExpressRecordStatusEnum.SUCCESS.getValue())
                .last("limit 1");
        return baseMapper.selectOne(wrapper);
    }

    public ImportRejectExpressDetail getOneByStorageExpressNumber(String storageExpressNumber) {
        Wrapper<ImportRejectExpressDetail> wrapper = Wrappers.<ImportRejectExpressDetail>lambdaQuery()
                .eq(ImportRejectExpressDetail::getStorageExpressNumber, storageExpressNumber)
                .eq(ImportRejectExpressDetail::getRecordStatus, RejectExpressRecordStatusEnum.SUCCESS.getValue())
                .last("limit 1");
        return baseMapper.selectOne(wrapper);
    }
}
