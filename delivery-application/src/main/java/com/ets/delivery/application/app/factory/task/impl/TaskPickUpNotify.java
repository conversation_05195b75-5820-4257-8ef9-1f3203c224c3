package com.ets.delivery.application.app.factory.task.impl;


import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.SignHelper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.common.bo.task.TaskPickUpNotifyContentBO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.dto.AppKeyParams;
import com.ets.delivery.application.infra.entity.Pickup;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.PickupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class TaskPickUpNotify extends TaskBase {

    @Autowired
    DeliveryConfig deliveryConfig;

    @Autowired
    private PickupService pickupService;

    @Override
    public void childExec(TaskRecord taskRecord) {
        TaskPickUpNotifyContentBO contentBO = JSON.parseObject(taskRecord.getNotifyContent(), TaskPickUpNotifyContentBO.class);

        Pickup pickup = pickupService.getOneByPickUpSn(taskRecord.getReferSn());
        if (ObjectUtils.isEmpty(pickup)) {
            ToolsHelper.throwException("上门取件单不存在");
        }

        Map<String, String> data = new HashMap<>();
        data.put("orderSn", contentBO.getOrderSn());
        data.put("status", contentBO.getStatus().toString());
        data.put("desc", contentBO.getDesc().replaceAll("[\\s*\\u00A0]", ""));
        data.put("expressCompany", contentBO.getExpressCompany());
        data.put("expressNumber", contentBO.getExpressNumber());

        try {
            // 生成签名
            String key = "key";
            Map<String, AppKeyParams> appKeyParams = deliveryConfig.getAppKeyParams();
            String secret = appKeyParams.get("10020").getAppSecret();
            log.info("【上门取件通知】sign str:{}", SignHelper.getSignStringKeepEmpty(data, key, secret));
            String sign = SignHelper.generateSignKeepEmpty(data, key, secret);
            String body = JSON.toJSONString(data);
            log.info("【上门取件通知】request url:{}, body:{}, sign:{}", pickup.getNotifyBackUrl(), body, sign);

            // 发起请求
            String result = HttpRequest.post(pickup.getNotifyBackUrl())
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .header("x-app-sign", sign)
                    .body(body)
                    .execute().body();

            log.info("【上门取件通知】response:{}", result);
            JsonResult<?> jsonResult = JSON.parseObject(result, JsonResult.class);
            jsonResult.checkError();
        } catch (HttpException e) {
            log.error("【上门取件通知】接口异常：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("通知接口请求失败");
        } catch (BizException e) {
            log.error("【上门取件通知】通知业务失败：{}", e.getErrorMsg());
            ToolsHelper.throwException("通知业务方失败：" + e.getErrorMsg());
        } catch (Throwable e) {
            log.error("【上门取件通知】{}", e.getLocalizedMessage());
            ToolsHelper.throwException("系统错误");
        }
    }
}
