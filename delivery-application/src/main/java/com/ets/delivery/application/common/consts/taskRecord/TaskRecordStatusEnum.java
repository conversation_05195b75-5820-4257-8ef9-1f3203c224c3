package com.ets.delivery.application.common.consts.taskRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum TaskRecordStatusEnum {
    TASK_STATUS_WAIT(0, "待处理"),
    TASK_STATUS_PROCESS(1, "处理中"),
    TASK_STATUS_FINISH(2, "处理完成"),
    TASK_STATUS_FAIL(3, "处理失败"),
    TASK_STATUS_STOP(4, "暂停处理");

    private final Integer code;
    private final String description;
    public static final Map<Integer, String> map;
    public static final List<Integer> list;

    static {
        TaskRecordStatusEnum[] enums = TaskRecordStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getCode(), enums[index].getDescription()),
                Map::putAll);
        list = Arrays.stream(enums).map(TaskRecordStatusEnum::getCode).collect(Collectors.toList());
    }

    public static String getDescByCode(int code) {
        for (TaskRecordStatusEnum node : TaskRecordStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }
        return "";
    }
}
