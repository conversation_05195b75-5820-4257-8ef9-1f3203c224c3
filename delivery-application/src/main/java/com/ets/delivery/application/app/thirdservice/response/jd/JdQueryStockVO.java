package com.ets.delivery.application.app.thirdservice.response.jd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class JdQueryStockVO extends JdErrorResponseVO {

    @JSONField(name = "jingdong_eclp_stock_queryStock_responce")
    private JdQueryStockResponseVO jdQueryStockResponse;

    @Data
    public static class JdQueryStockResponseVO {

        private String code;

        @JSONField(name = "querystock_result")
        private List<JdQueryStockResult> result;

        @Data
        public static class JdQueryStockResult {
            private String deptNo;
            private String deptName;
            private String warehouseNo;
            private String warehouseName;
            private String goodsNo;
            private String goodsName;
            private String sellerGoodsSign;
            private String stockStatus;
            private String stockType;
            private Integer totalNum;
            private Integer usableNum;
            private Integer recordCount;
            private String goodsLevel;
        }
    }

}
