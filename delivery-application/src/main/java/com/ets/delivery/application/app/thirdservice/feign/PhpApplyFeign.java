package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.PhpApplyFallbackFactory;
import com.ets.delivery.application.app.thirdservice.request.apply.ApplyOrderRiskNotifyDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        url = "http://etc-apply:80",
        name = "PhpApplyFeign",
        fallbackFactory = PhpApplyFallbackFactory.class
)
public interface PhpApplyFeign {

    @PostMapping("/notify/order/order-risk")
    String orderRiskNotify(@RequestBody ApplyOrderRiskNotifyDTO notifyDTO);
}
