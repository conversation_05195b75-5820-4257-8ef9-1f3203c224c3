package com.ets.delivery.application.common.bo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class GoodsLogBO {

    @NotNull(message = "商品id不能为空")
    private Integer supplyGoodsId;

    @NotBlank(message = "操作人不能为空")
    private String operator;

    @NotBlank(message = "操作类型不能为空")
    private String type;

    @NotBlank(message = "操作内容不能为空")
    private String operateContent;
}
