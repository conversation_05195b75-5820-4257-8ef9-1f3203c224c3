package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.ImportFileRecord;
import com.ets.delivery.application.infra.mapper.ImportFileRecordMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 文件上传导入记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Service
@DS("db-issuer-admin")
public class ImportFileRecordService extends BaseService<ImportFileRecordMapper, ImportFileRecord> {

    public ImportFileRecord getByBatchNo(String batchNo) {
        Wrapper<ImportFileRecord> wrapper = Wrappers.<ImportFileRecord>lambdaQuery()
                .eq(ImportFileRecord::getBatchNo, batchNo)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
