package com.ets.delivery.application.common.consts.postReviews;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum PostReviewStatusEnum {

    //后审状态[0-待审核 1-审核中 2-审核通过 3-审核异常]
    REVIEW_STATUS_DEFAULT(0, "待审核"),
    REVIEW_STATUS_PROCESSING(1, "审核中"),
    REVIEW_STATUS_PASS(2, "审核通过"),
    REVIEW_STATUS_EXCEPTION(3, "审核异常");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    PostReviewStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        PostReviewStatusEnum[] enums = PostReviewStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
