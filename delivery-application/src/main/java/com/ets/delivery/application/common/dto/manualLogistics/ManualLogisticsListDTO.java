package com.ets.delivery.application.common.dto.manualLogistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;

@Data
public class ManualLogisticsListDTO {

    private String originOrderSn;

    private String plateNo;

    private Integer deliveryStatus;

    private String orderSn;

    private String expressNumber;

    private Integer expressStatus;

    private String sku;

    private String reason;

    private String operator;

    private String sendPhone;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pushStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pushEndTime;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
