package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 发货单列表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_logistics")
public class Logistics extends BaseEntity<Logistics> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 发货单流水号
     */
    private String logisticsSn;

    /**
     * 订单流水号
     */
    private String orderSn;

    /**
     * 原始订单号（冗余）
     */
    private String originOrderSn;

    /**
     * 原始业务单号
     */
    private String originBusinessSn;

    /**
     * 来源方
     */
    private String appId;

    /**
     * 对接方id
     */
    private Integer issuerId;

    /**
     * 发卡方名称
     */
    private String issuerName;

    /**
     * 卡种
     */
    private Integer cardId;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 所属仓储
     */
    private String storageCode;

    /**
     * 指定发货物流编码
     */
    private String logisticsCode;
    /**
     * 供应商编号
     */
    private String supplyCode;

    /**
     * 货品编号
     */
    private String goodsCode;

    /**
     * 货品类型，主要为card,obu,card_obu
     */
    private String goodsType;

    /**
     * 发货数量
     */
    private Integer nums;

    /**
     * 收件人
     */
    private String sendName;

    /**
     * 联系手机
     */
    private String sendPhone;

    /**
     * 发货地区
     */
    private String sendArea;

    /**
     * 收货地址
     */
    private String sendAddress;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 快递状态[-1-取消 0-未发货 1-已发货 2-已揽收 3-运输中 4-已签收]
     */
    private Integer expressStatus;

    /**
     * 下单原因
     */
    private String reason;

    /**
     * 下单备注
     */
    private String remark;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 状态：1正常2取消3暂停
     */
    private Integer status;

    /**
     * 推送时间
     */
    private LocalDateTime pushTime;

    /**
     * 发货状态[0-待发货 1-发货中 2-已发货 3-发货取消 4-发货暂停]
     */
    private Integer deliveryStatus;

    /**
     * 发货备注
     */
    private String deliveryRemark;

    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 领取时间
     */
    private LocalDateTime drawTime;

    /**
     * 通知Url
     */
    private String notifyBackUrl;

    /**
     * 通知状态,0待通知，1通知中，2通知成功，3通知失败，4通知取消
     */
    private Integer notifyStatus;

    /**
     * 通知备注
     */
    private String notifyRemark;

    /**
     * 通知时间
     */
    private LocalDateTime notifyTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 统计发货量
     */
    @TableField(exist = false)
    private Double sumGoodsNums;
}
