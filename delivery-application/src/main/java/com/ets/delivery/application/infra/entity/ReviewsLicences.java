package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 审核营业执照数据
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_reviews_licences")
public class ReviewsLicences extends BaseEntity<ReviewsLicences> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审核单sn
     */
    private String reviewSn;

    /**
     * 营业执照水印图片url
     */
    private String wmImgUrl;

    /**
     * 注册号
     */
    private String registerNo;

    /**
     * 法定代表人
     */
    private String legalRepresentative;

    /**
     * 注册资本
     */
    private String registeredCapital;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 地址
     */
    private String companyAddress;

    /**
     * 营业期限
     */
    private String operatingPeriod;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 主体类型
     */
    private String companyType;

    /**
     * 成立日期
     */
    private String buildDate;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
