package com.ets.delivery.application.common.consts.setting;

import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
public enum SettingCategoryEnum {

    REVIEW("review", "审核资料"),
    // 申办审核
    REJECT_HUMAN("reject_human", "人工驳回"),
    REJECT_AUTO("reject_auto", "自动驳回"),
    REJECT_HUMAN_ID_CARD("reject_human_idcard", "身份证驳回"),
    REJECT_HUMAN_VEHICLE("reject_human_vehicle", "行驶证驳回"),
    REJECT_HUMAN_VEHICLE_BODY("reject_human_vehicle_body", "车身照驳回"),
    REJECT_HUMAN_LICENSE_ROAD("reject_human_license_road", "道路运输证驳回"),
    // 售后审核
    AFTER_SALES_REVIEW_REJECT_VEHICLE_REUPLOAD("aftersales_reject_vehicle_reupload", "行驶证-重新上传"),
    AFTER_SALES_REVIEW_REJECT_VEHICLE_CHANGE("aftersales_reject_vehicle_change", "行驶证-换车换牌"),
    AFTER_SALES_REVIEW_REJECT_FRONT_CAR_IMG("aftersales_reject_front_car_img", "车头照"),
    AFTER_SALES_REVIEW_REJECT_CAR_INNER_IMG("aftersales_reject_car_inner_img", "车内照"),
    AFTER_SALES_REVIEW_REJECT_OTHER("aftersales_reject_other", "其他"),

    // 发货地址映射
    ADDRESS_STORAGE_MAP("address_storage_map", "发货地址映射-仓库"),
    ADDRESS_STORAGE_MAP_LOGISTICS("address_storage_map_logistics", "发货地址映射-物流公司"),
    // 风控审核
    RISK_REVIEW_REJECT_CANCEL("risk_review_reject_cancel", "驳回并取消"),
    RISK_REVIEW_REJECT_REUPLOAD("risk_review_reject_reupload", "驳回并重新上传"),
    RISK_REVIEW_REJECT_PREVIEW_SUPPLEMENT_MATERIAL("risk_review_reject_preview_supplement", "补传资料-初审"),
    RISK_REVIEW_REJECT_RECHECK_SUPPLEMENT_MATERIAL("risk_review_reject_recheck_supplement", "补传资料-复审"),
    ;

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;
    public static final List<String> afterSalesList;
    public static final List<String> riskReviewList;

    SettingCategoryEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        SettingCategoryEnum[] enums = SettingCategoryEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Arrays.stream(enums).map(SettingCategoryEnum::getValue).collect(Collectors.toList());
        // 售后审核类型
        afterSalesList = Arrays.stream(enums).map(SettingCategoryEnum::getValue)
                .filter(eValue -> eValue.startsWith("aftersales")).collect(Collectors.toList());
        // 风控审核类型
        riskReviewList = Arrays.stream(enums).map(SettingCategoryEnum::getValue)
                .filter(eValue -> eValue.startsWith("risk_review")).collect(Collectors.toList());
    }
}
