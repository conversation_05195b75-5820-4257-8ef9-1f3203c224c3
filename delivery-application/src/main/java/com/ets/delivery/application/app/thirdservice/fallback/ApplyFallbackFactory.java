package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.ApplyFeign;
import com.ets.delivery.application.app.thirdservice.request.apply.ApplyPackageListDTO;
import com.ets.delivery.application.app.thirdservice.request.apply.ProductOrderExternalCreateDTO;
import com.ets.delivery.application.common.vo.productOrder.ProductOrderExternalCreateVO;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import jakarta.validation.Valid;

@Component
public class ApplyFallbackFactory implements FallbackFactory<ApplyFeign> {
    @Override
    public ApplyFeign create(Throwable throwable) {
        return new ApplyFeign() {
            @Override
            public JsonResult<?> getProductPackageList(ApplyPackageListDTO listDTO) {
                return JsonResult.error("apply服务调用失败" + throwable.getMessage());
            }

            @Override
            public JsonResult<ProductOrderExternalCreateVO> createOrderFromExternal(@Valid ProductOrderExternalCreateDTO dto) {
                return JsonResult.error("apply服务调用失败" + throwable.getMessage());
            }
        };
    }
}
