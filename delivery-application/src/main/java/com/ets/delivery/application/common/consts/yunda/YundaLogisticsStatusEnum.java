package com.ets.delivery.application.common.consts.yunda;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum YundaLogisticsStatusEnum {

    GOT("GOT", "已揽件"),
    TRANSIT("TRANSIT", "运输中"),
    SIGNED("SIGNED", "已签收"),
    RETURN("RETURN", "退回件"),
    SIGNFAIL("SIGNFAIL", "异常签收");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    YundaLogisticsStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        YundaLogisticsStatusEnum[] enums = YundaLogisticsStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
