package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 销售出库单商品详细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_ex_warehouse_detail")
public class ExWarehouseDetail extends BaseEntity<ExWarehouseDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 出库单号，对应了发货单物流单号
     */
    private String isvUuid;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 商品金额
     */
    private BigDecimal price;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
