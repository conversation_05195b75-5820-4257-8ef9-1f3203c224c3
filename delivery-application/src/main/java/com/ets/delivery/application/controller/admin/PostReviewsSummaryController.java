package com.ets.delivery.application.controller.admin;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.PostReviewSummaryBusiness;
import com.ets.delivery.application.common.dto.postReviews.PostReviewBeReviewDTO;
import com.ets.delivery.application.common.dto.postReviews.PostReviewWaitingReviewDTO;
import com.ets.delivery.application.common.vo.postReviews.PostReviewBeReviewVO;
import com.ets.delivery.application.common.vo.postReviews.PostReviewWaitingReviewVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/summary")
public class PostReviewsSummaryController {

    @Autowired
    private PostReviewSummaryBusiness business;

    @PostMapping("/waitToReview")
    public JsonResult<PostReviewWaitingReviewVO> waitToReview() {
        return JsonResult.ok(business.waitToReview());
    }

    @PostMapping("/userReviews")
    public JsonResult<PostReviewBeReviewVO> userReviews(@Validated @RequestBody PostReviewBeReviewDTO dto) {
        return JsonResult.ok(business.userReviews(dto));
    }

}
