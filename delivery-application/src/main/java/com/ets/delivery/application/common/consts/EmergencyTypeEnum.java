package com.ets.delivery.application.common.consts;

import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
public enum EmergencyTypeEnum {

    EMERGENCY(3, "紧急"),
    FIRST(2, "优先"),
    NORMAL(1, "正常"),
    NO_NEED(0, "无");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    public static final List<Integer> list;

    EmergencyTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        EmergencyTypeEnum[] enums = EmergencyTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Arrays.stream(enums).map(EmergencyTypeEnum::getValue).collect(Collectors.toList());
    }
}
