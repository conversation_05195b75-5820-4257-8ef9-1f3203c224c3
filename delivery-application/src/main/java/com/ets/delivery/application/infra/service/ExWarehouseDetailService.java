package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.ExWarehouseDetail;
import com.ets.delivery.application.infra.mapper.ExWarehouseDetailMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 销售出库单商品详细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Service
@DS("db-issuer-admin")
public class ExWarehouseDetailService extends BaseService<ExWarehouseDetailMapper, ExWarehouseDetail> {

    public ExWarehouseDetail getOneByIsvUuidAndGoodsNo(String isvUuid, String goodsNo) {
        Wrapper<ExWarehouseDetail> wrapper = Wrappers.<ExWarehouseDetail>lambdaQuery()
                .eq(ExWarehouseDetail::getIsvUuid, isvUuid)
                .eq(ExWarehouseDetail::getGoodsNo, goodsNo)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
