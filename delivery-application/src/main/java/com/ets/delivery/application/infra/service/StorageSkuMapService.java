package com.ets.delivery.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.common.dto.manage.ManageStorageSkuMapDTO;
import com.ets.delivery.application.common.vo.SelectOptionsVO;
import com.ets.delivery.application.infra.entity.StorageSkuMap;
import com.ets.delivery.application.infra.mapper.StorageSkuMapMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 仓储映射表 业务处理类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Service
@Slf4j
public class StorageSkuMapService extends BaseService<StorageSkuMapMapper, StorageSkuMap> {
    /*
     * 通过仓库编码+SKU查找记录
     */
    public List<StorageSkuMap> getListByStorageCodeSku(String storageCode, List<String> sku){
        Wrapper<StorageSkuMap> wrapper = Wrappers.<StorageSkuMap>lambdaQuery()
                .eq(StorageSkuMap::getStorageCode, storageCode)
                .in(StorageSkuMap::getSku, sku)
                .eq(StorageSkuMap::getStatus, 1);
        return this.baseMapper.selectList(wrapper);
    }

    /*
     * 通过仓库编码+仓库SKU查找记录
     */
    public List<StorageSkuMap> getListByStorageCodeStorageSku(String storageCode, List<String> storageSku) {
        Wrapper<StorageSkuMap> wrapper = Wrappers.<StorageSkuMap>lambdaQuery()
                .eq(StringUtils.isNotEmpty(storageCode), StorageSkuMap::getStorageCode, storageCode)
                .in(StorageSkuMap::getStorageSku, storageSku)
                .eq(StorageSkuMap::getStatus, 1);
        return this.baseMapper.selectList(wrapper);
    }

    public IPage<StorageSkuMap> getDefaultPage(ManageStorageSkuMapDTO listDTO) {
        Wrapper<StorageSkuMap> wrapper = Wrappers.<StorageSkuMap>lambdaQuery()
            .eq(listDTO.getSku() != null, StorageSkuMap::getSku, listDTO.getSku())
            .eq(listDTO.getStatus() > 0, StorageSkuMap::getStatus, listDTO.getStatus())
            .eq(StringUtils.isNotEmpty(listDTO.getStorageCode()), StorageSkuMap::getStorageCode, listDTO.getStorageCode())
            .eq(StringUtils.isNotEmpty(listDTO.getStorageSku()), StorageSkuMap::getStorageSku, listDTO.getStorageSku())
            .like(StringUtils.isNotEmpty(listDTO.getGoodsName()), StorageSkuMap::getGoodsName, listDTO.getGoodsName())
            .orderByDesc(StorageSkuMap::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }

    /*
     * 通过仓库编码+SKU查找记录
     */
    public StorageSkuMap getOneByStorageCodeSku(String storageCode, String sku){
        Wrapper<StorageSkuMap> wrapper = Wrappers.<StorageSkuMap>lambdaQuery()
            .eq(StorageSkuMap::getStorageCode, storageCode)
            .eq(StorageSkuMap::getSku, sku)
            .eq(StorageSkuMap::getStatus, 1)
            .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public StorageSkuMap getOneByStorageSku(String storageCode, String storageSku) {
        Wrapper<StorageSkuMap> wrapper = Wrappers.<StorageSkuMap>lambdaQuery()
                .eq(StorageSkuMap::getStorageCode, storageCode)
                .eq(StorageSkuMap::getStorageSku, storageSku)
                .eq(StorageSkuMap::getStatus, 1)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public StorageSkuMap getBySkuSn(String storageCode, String skuSn) {

        LambdaQueryWrapper<StorageSkuMap> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StorageSkuMap::getSku, skuSn)
                .eq(StorageSkuMap::getStorageCode, storageCode);

        return getOneByWrapper(wrapper);
    }

    public StorageSkuMap getBySku(String storageCode, String skuSn) {

        StorageSkuMap storageSkuMap = getOneByStorageCodeSku(storageCode, skuSn);
        if (storageSkuMap == null) {
            ToolsHelper.throwException("库存商品不存在：" + skuSn);
        }

        return storageSkuMap;
    }

    public StorageSkuMap getByStorageSkuAndSku(String storageSku, String skuSn) {

        LambdaQueryWrapper<StorageSkuMap> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StorageSkuMap::getStorageSku, storageSku)
                .eq(StorageSkuMap::getSku, skuSn)
                .eq(StorageSkuMap::getStatus, 1);

        return getOneByWrapper(wrapper);
    }

    public List<StorageSkuMap> search(String goodsName, String storageCode) {

        LambdaQueryWrapper<StorageSkuMap> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotEmpty(goodsName), StorageSkuMap::getGoodsName, goodsName)
                .eq(StorageSkuMap::getStorageCode, storageCode)
                .eq(StorageSkuMap::getStatus, 1);

        return getListByWrapper(wrapper);
    }

    public List<SelectOptionsVO> getOptionList() {

        QueryWrapper<StorageSkuMap> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT goods_name,storage_sku")
                .eq("status", 1);

        List<StorageSkuMap> list = this.getBaseMapper().selectList(wrapper);

        List<SelectOptionsVO> options = new ArrayList<>();
        for (StorageSkuMap storageSkuMap : list) {
            SelectOptionsVO vo = new SelectOptionsVO(
                    storageSkuMap.getStorageSku(),
                    storageSkuMap.getGoodsName()
            );

            options.add(vo);
        }

        return options;
    }

    public List<StorageSkuMap> getListByStorageSku(String storageCode, String storageSku) {

        LambdaQueryWrapper<StorageSkuMap> wrapper = Wrappers.<StorageSkuMap>lambdaQuery()
                .eq(StringUtils.isNotEmpty(storageCode), StorageSkuMap::getStorageCode, storageCode)
                .eq(StorageSkuMap::getStorageSku, storageSku)
                .eq(StorageSkuMap::getStatus, 1);

        return getListByWrapper(wrapper);
    }
}
