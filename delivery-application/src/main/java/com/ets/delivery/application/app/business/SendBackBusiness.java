package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.ets.common.util.NumberUtil;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.common.bo.SendBackLogBO;
import com.ets.delivery.application.common.bo.sendback.LogisticsSendBackCreateBO;
import com.ets.delivery.application.common.bo.sendback.LogisticsSendBackPageBO;
import com.ets.delivery.application.common.bo.task.TaskExpressSubscribeBO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeOrderTypeEnum;
import com.ets.delivery.application.common.consts.sendback.*;
import com.ets.delivery.application.common.consts.storageRecord.GoodsTypeEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeDTO;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeDirectlyDTO;
import com.ets.delivery.application.common.dto.logisticsSendBack.*;
import com.ets.delivery.application.common.vo.logisticsSendBack.*;
import com.ets.delivery.feign.request.express.ExpressBatchQueryDTO;
import com.ets.delivery.feign.request.express.SendBackUpdateExpressDTO;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.common.vo.SendBackInfoVO;
import com.ets.delivery.application.common.vo.storage.StorageCheckResultVO;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.*;
import com.ets.delivery.feign.request.logistics.SendBackCreateByGoodsDTO;
import com.ets.delivery.feign.response.express.ExpressBatchQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SendBackBusiness {

    @Autowired
    private LogisticsSendBackService sendBackService;

    @Autowired
    private LogisticsSendbackLogService sendBackLogService;

    @Qualifier("redisPermanentTemplate")
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Autowired
    private SupplyGoodsService supplyGoodsService;

    @Autowired
    private StorageSkuMapService storageSkuMapService;

    @Autowired
    private StorageRecordService storageRecordService;

    @Autowired
    private TaskRecordBusiness taskRecordBusiness;

    @Autowired
    private ExpressBusiness expressBusiness;

    @Autowired
    DeliveryConfig deliveryConfig;

    public SendBackInfoVO getOrderInfoByLogisticsSn(String sendBackSn) {
        LogisticsSendBack orderInfo = sendBackService.getInfoBySn(sendBackSn);
        if (ObjectUtil.isNull(orderInfo)) {
            return null;
        }
        return BeanUtil.copyProperties(orderInfo, SendBackInfoVO.class);
    }

    public LogisticsSendBack create(LogisticsSendBackCreateBO createBO) {
        LogisticsSendBack sendBack = BeanUtil.copyProperties(createBO, LogisticsSendBack.class);
        String sendBackSn = "LS" + ToolsHelper.genNum(redisTemplate,"etc_logistics_sendback",ACTIVE,8);
        sendBack.setSendbackSn(sendBackSn);
        sendBack.setCreatedAt(LocalDateTime.now());
        sendBack.setUpdatedAt(LocalDateTime.now());
        sendBackService.save(sendBack);
        return sendBack;
    }

    public void cancelSendBack(String orderSn) {

        LogisticsSendBack sendBack = getUnfinishedOrder(orderSn);
        if (ObjectUtils.isEmpty(sendBack)) {
            return;
        }

        doCancel(sendBack.getSendbackSn());

        // 记录日志
        SendBackLogBO logBO = new SendBackLogBO();
        logBO.setSendbackId(sendBack.getId());
        logBO.setOperator("system");
        logBO.setType(SendBackLogTypeEnum.TYPE_CANCEL.getValue());
        logBO.setOperateContent("上门取件取消寄回件记录");
        sendBackLogService.addLog(logBO);
    }

    public void doCancel(String sendBackSn) {

        LogisticsSendBack sendBack = sendBackService.getInfoBySn(sendBackSn);
        if (ObjectUtil.isNull(sendBack)) {
            return;
        }

        if (sendBack.getStatus().equals(SendBackStatusEnum.STATUS_CANCEL.getValue())) {
            return;
        }

        sendBackService.cancel(sendBack.getSendbackSn());
    }

    public LogisticsSendBack getUnfinishedOrder(String orderSn) {
        return sendBackService.getUnfinishedOrder(orderSn);
    }

    public LogisticsSendBack updateOriginExpressNumber(LogisticsSendBack sendBack, String expressNumber, String originExpressNumber, boolean checkExist) {
        // 更新快递单号
        if (!checkExist || StringUtils.isEmpty(sendBack.getOriginExpressNumber())) {
            sendBack.setExpressNumber(expressNumber);
            sendBack.setOriginExpressNumber(originExpressNumber);
            sendBack.setUpdatedAt(LocalDateTime.now());
            sendBackService.updateById(sendBack);
        }

        // 记录寄回件日志
        SendBackLogBO logBO = new SendBackLogBO();
        logBO.setSendbackId(sendBack.getId());
        logBO.setType(SendBackLogTypeEnum.TYPE_MODIFY.getValue());
        logBO.setOperator("system");
        logBO.setOperateContent("原发货单快递拒收，" + originExpressNumber + "换单为新快递单号：" + expressNumber);
        sendBackLogService.addLog(logBO);

        return sendBack;
    }

    public String checkStorageSign(LogisticsSendBack sendBack, String storageCode) {

        if (! sendBack.getStatus().equals(SendBackStatusEnum.STATUS_NORMAL.getValue())) {
            return "寄回件订单非正常状态，不可进行该操作";
        }

        if (sendBack.getReviceStatus().equals(SendBackReceiveStatusEnum.RECEIVE_STATUS_NORMAL.getValue())) {
            return "寄回件订单已签收检查成功，请不要重复操作";
        }

        // 仓储校验
        if (! sendBack.getStorageCode().equals(storageCode)) {
            return "仓储信息不一致, 寄回件的仓库为：" + sendBack.getStorageCode();
        }

        return "";
    }

    public void checkStorageRecordGoods(LogisticsSendBack sendBack, StorageRecord storageRecord) {

        // 获取寄回件商品信息
        if (StringUtils.isEmpty(sendBack.getGoodsCode())) {
            ToolsHelper.throwException("寄回件单商品数据为空");
        }

        if (! NumberUtil.isPositive(storageRecord.getGoodsType())) {
            ToolsHelper.throwException("入库单商品类型为空");
        }

        if (sendBack.getOrderType().equals("goods")) {

            checkBySkuMerge(sendBack.getSkuInfoList(), storageRecord, sendBack.getGoodsCode());

        } else {
            checkByOrderType(sendBack.getOrderType(), sendBack.getIssuerId(), storageRecord);
        }
    }

    // 以sendBack的sku信息为基准，storageRecord的goodsType做为etc商品的判断依据，storageRecord的skuInfo作为其他商品的判断依据
    public void checkBySkuMerge(List<SendBackCreateByGoodsDTO.GoodsSku> sendBackSkuInfo, StorageRecord storageRecord, String deliveryGoodsCode) {

        JSONObject storageSkuObj = storageRecord.getSkuInfoObj();
        Integer storageGoodsType = storageRecord.getGoodsType();

        boolean etcIsUnit = false;
        String etcGoodsType = "";
        if (! storageGoodsType.equals(GoodsTypeEnum.GOODS.getValue())) {
            etcIsUnit = storageGoodsType.equals(GoodsTypeEnum.UNIT.getValue());
            etcGoodsType = GoodsTypeEnum.getTypeByValue(storageGoodsType);
        }

        if (sendBackSkuInfo == null) {
            ToolsHelper.throwException("商品信息缺失");
        }

        for (SendBackCreateByGoodsDTO.GoodsSku goodsSku : sendBackSkuInfo) {

            if (StringUtils.isNotEmpty(goodsSku.getEtcType())) {

                if (goodsSku.getIsUnit() && ! etcIsUnit) {
                    unMatch(goodsSku.getEtcType(), storageGoodsType, true);
                }

                if (! goodsSku.getEtcType().equals(etcGoodsType)) {
                    unMatch(goodsSku.getEtcType(), storageGoodsType, false);
                }
            } else {
                // 赠品由商品服务发起时，去掉对应的sku

                // 上线前产生的ETC设备历史数据兼容
                /* 不再兼容
                if (! storageGoodsType.equals(GoodsTypeEnum.GOODS.getValue())) {
                    checkBySupplyGoods(deliveryGoodsCode, storageRecord);
                    continue;
                }*/
                if (storageSkuObj == null) {
                    ToolsHelper.throwException("商品信息缺失");
                }

                // 其他商品sku对比
                if (! storageSkuObj.containsKey(goodsSku.getSkuSn())) {
                    ToolsHelper.throwException("寄回的商品缺失或损坏");
                }

                if (! goodsSku.getCount().equals(storageSkuObj.getInteger(goodsSku.getSkuSn()))) {
                    ToolsHelper.throwException("寄回的商品数量不对");
                }
            }
        }
    }

    public void unMatch(String etcGoodsType, Integer storageGoodsType, Boolean isUnit) {

        String storageGoodsName = GoodsTypeEnum.getDescByCode(storageGoodsType);

        switch (etcGoodsType) {
            case "card":
                ToolsHelper.throwException("寄回件要求为：单卡，入库件为：" + storageGoodsName);
                break;
            case "obu":
                ToolsHelper.throwException("寄回件要求为：单OBU，入库件为：" + storageGoodsName);
                break;
            case "card_obu":
                // 是否单片式
                if (isUnit ) {
                    ToolsHelper.throwException("寄回件要求为：单片式设备，入库件为：" + storageGoodsName);
                } else {
                    ToolsHelper.throwException("寄回件要求为：ETC卡+OBU，入库件为：" + storageGoodsName);
                }

                break;
            default:
                ToolsHelper.throwException("寄回件单商品类型未能识别：" + etcGoodsType);
                break;
        }
    }

    public void checkBySkuInfo(LogisticsSendBack sendBack, StorageRecord storageRecord) {

        if (StringUtils.isEmpty(sendBack.getGoodsSkuInfo()) || StringUtils.isEmpty(storageRecord.getSkuInfo())) {
            ToolsHelper.throwException("商品信息缺失");
        }

        sendBack.getSkuInfoList().forEach(goodsSku -> {
            if (! storageRecord.getSkuInfoObj().containsKey(goodsSku.getSkuSn())) {
                ToolsHelper.throwException("寄回的商品缺失或损坏");
            }

            if (! goodsSku.getCount().equals(storageRecord.getSkuInfoObj().getInteger(goodsSku.getSkuSn()))) {
                ToolsHelper.throwException("寄回的商品数量不对");
            }
        });
    }

    public void checkByOrderType(String orderType, Integer issuerId, StorageRecord storageRecord) {

        String storageGoodsName = GoodsTypeEnum.getDescByCode(storageRecord.getGoodsType());

        // 至少是以下一种
        String goodsTypeString = GoodsTypeEnum.typeMap.getOrDefault(storageRecord.getGoodsType(), "");

        switch (orderType) {
            case "after_sales_maintain_exchange":
            case "after_sales_exchange":
                if (StringUtils.isEmpty(goodsTypeString)) {
                    ToolsHelper.throwException("要求寄回以下一种：单卡、单OBU、单片式设备、ETC卡+OBU; 入库件为：" + storageGoodsName);
                }
                break;
            case "after_sales_recall_exchange":
            case "after_sales_return":
            case "after_sales_reject":
            case "upgrade":
                if (! goodsTypeString.equals("card_obu")) {
                    ToolsHelper.throwException("要求寄回 单片式设备 或 ETC卡+OBU; 入库件为：" + storageGoodsName);
                }
                break;
            case "after_sales_cancel":
                if (! storageRecord.getGoodsType().equals(GoodsTypeEnum.CARD_OBU.getValue())) {
                    ToolsHelper.throwException("要求寄回 ETC卡+OBU; 入库件为：" + storageGoodsName);
                }
                if (! Arrays.asList(1, 28).contains(issuerId)) {
                    ToolsHelper.throwException("仅限江苏和内蒙发卡方");
                }
                break;
            case "reapply":
                if (! storageRecord.getGoodsType().equals(GoodsTypeEnum.OBU.getValue())) {
                    ToolsHelper.throwException("要求寄回 单OBU; 入库件为：" + storageGoodsName);
                }
                if (issuerId != 1) {
                    ToolsHelper.throwException("仅限江苏发卡方");
                }
                break;
            case "after_sales_recovery":
                // 回收任何物品都按成功处理
                break;
            default:
                ToolsHelper.throwException("不支持的售后类型");
                break;
        }
    }

    public void checkBySupplyGoods(String supplyGoodsCode, StorageRecord storageRecord) {

        SupplyGoods goods = supplyGoodsService.getOneByGoodsCode(supplyGoodsCode);

        if (goods == null) {
            ToolsHelper.throwException("寄回件单商品数据为空");
        }

        String storageGoodsName = GoodsTypeEnum.getDescByCode(storageRecord.getGoodsType());

        switch (goods.getGoodsType()) {
            case "card":
                if (! storageRecord.getGoodsType().equals(GoodsTypeEnum.CARD.getValue())) {
                    ToolsHelper.throwException("寄回件要求为：单卡，入库件为：" + storageGoodsName);
                }
                break;
            case "obu":
                if (! storageRecord.getGoodsType().equals(GoodsTypeEnum.OBU.getValue())) {
                    ToolsHelper.throwException("寄回件要求为：单OBU，入库件为：" + storageGoodsName);
                }
                break;
            case "card_obu":
                // 是否单片式
                boolean isUnit = goods.getDeviceType().equals(2);

                if (isUnit ) {
                    if (! storageRecord.getGoodsType().equals(GoodsTypeEnum.UNIT.getValue())) {
                        ToolsHelper.throwException("寄回件要求为：单片式设备，入库件为：" + storageGoodsName);
                    }
                } else {
                    if (! storageRecord.getGoodsType().equals(GoodsTypeEnum.CARD_OBU.getValue())) {
                        ToolsHelper.throwException("寄回件要求为：ETC卡+OBU，入库件为：" + storageGoodsName);
                    }
                }

                break;
            default:
                ToolsHelper.throwException("寄回件单商品类型未能识别：" + goods.getGoodsType());
                break;
        }
    }

    public List<SendBackGoodsVO> getGoodsInfo(LogisticsSendBack sendBack) {
        // 获取寄回件商品信息
        if (StringUtils.isEmpty(sendBack.getGoodsCode())) {
            return null;
        }

        StorageSkuMap storageSku = storageSkuMapService.getOneByStorageSku(sendBack.getStorageCode(), sendBack.getGoodsCode());

        if (storageSku == null) {
            return null;
        }

        SendBackGoodsVO vo = new SendBackGoodsVO();
        vo.setCount(sendBack.getNums());
        if (Arrays.asList(
                "after_sales_maintain_exchange", "after_sales_exchange"
        ).contains(sendBack.getOrderType())) {
            vo.setSkuName("单卡、单OBU、单片式设备、ETC卡+OBU");
        } else {
            vo.setSkuName(storageSku.getGoodsName());
        }

        List<SendBackGoodsVO> list = new ArrayList<>();
        list.add(vo);

        return list;
    }

    public SendBackCheckInfoVO getStorageCheckInfo(String expressNumber, String storageCode) {

        SendBackCheckInfoVO vo = new SendBackCheckInfoVO();

        LogisticsSendBack sendBack = sendBackService.getByExpressNumber(expressNumber);
        if (sendBack == null) {
            vo.setErrorMsg("未找到该快递单号对应的寄回件");
            return vo;
        }

        vo.setErrorMsg(checkStorageSign(sendBack, storageCode));
        if (StringUtils.isNotEmpty(vo.getErrorMsg())) {
            return vo;
        }

        vo.setGoodsInfo(getGoodsInfo(sendBack));

        return vo;
    }

    public LogisticsSendBack getByExpressNumber(String expressNumber) {

        if (StringUtils.isEmpty(expressNumber)) {
            ToolsHelper.throwException("快递单号为空");
        }

        LogisticsSendBack sendBack = sendBackService.getByExpressNumber(expressNumber);
        if (sendBack == null) {
            ToolsHelper.throwException("未找到该快递单号对应的寄回件");
        }

        return sendBack;
    }

    public LogisticsSendBack getBySendBackSn(String sendBackSn) {

        LogisticsSendBack sendBack = sendBackService.getInfoBySn(sendBackSn);

        if (sendBack == null) {
            ToolsHelper.throwException("寄回件单不存在：" + sendBackSn);
        }

        return sendBack;
    }

    public StorageCheckResultVO checkAndUpdate(LogisticsSendBack sendBack, StorageRecord storageRecord) {

        if (sendBack == null) {
            ToolsHelper.throwException("寄回件不存在");
        }

        StorageCheckResultVO vo = new StorageCheckResultVO();
        vo.setIsSuccess(false);
        vo.setIssuerName(SendBackIssuerEnum.getDescByCode(sendBack.getIssuerId()));
        vo.setOrderTypeStr(SendBackOrderTypeEnum.map.getOrDefault(sendBack.getOrderType(), ""));

        if (sendBack.getReviceStatus().equals(SendBackReceiveStatusEnum.RECEIVE_STATUS_NORMAL.getValue())) {
            vo.setRemark("寄回件已绑定入库单成功，请不要重复提交");
            return vo;
        }

        Integer receiveStatus = SendBackReceiveStatusEnum.RECEIVE_STATUS_NORMAL.getValue();
        String remark = "检查正常";
        boolean isSuccess = true;
        try {
            checkStorageRecordGoods(sendBack, storageRecord);
            // 校验成功
        } catch (BizException e) {
            // 校验失败
            receiveStatus = SendBackReceiveStatusEnum.RECEIVE_STATUS_ABNORMAL.getValue();
            remark = "检查异常 " + e.getErrorMsg();
            isSuccess = false;
        }
        vo.setIsSuccess(isSuccess);
        vo.setRemark(remark);

        // 更新寄回件
        sendBackService.updateReceiveInfo(sendBack.getSendbackSn(), receiveStatus, remark, LocalDateTime.now());

        // 更新入库单
        storageRecordService.updateReceiveInfo(storageRecord.getRecordSn(), isSuccess, remark);

        // 通知业务方
        if (isSuccess) {
            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferSn(sendBack.getSendbackSn());
            taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_SENDBACK_CHECK.getType());
            TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_SENDBACK_CHECK).addAndPush(taskRecordDTO);
        }

        return vo;
    }

    public void retryNotify(String sendBackSn) {

        TaskRecord task = taskRecordBusiness.getByReferSn(sendBackSn, TaskRecordReferTypeEnum.TASK_LOGISTICS_SENDBACK_CHECK.getType(), false);

        if (task == null) {
            LogisticsSendBack sendBack = getBySendBackSn(sendBackSn);
            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferSn(sendBack.getSendbackSn());
            taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_SENDBACK_CHECK.getType());
            TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_SENDBACK_CHECK).addAndPush(taskRecordDTO);
        } else {
            taskRecordBusiness.executeTask(task);
        }
    }

    public void updateExpress(SendBackUpdateExpressDTO dto) {

        LogisticsSendBack sendBack = getBySendBackSn(dto.getSendbackSn());

        if (Arrays.asList(
                SendBackReceiveStatusEnum.RECEIVE_STATUS_ABNORMAL.getValue(),
                SendBackReceiveStatusEnum.RECEIVE_STATUS_NORMAL.getValue()
            ).contains(sendBack.getReviceStatus())) {
            ToolsHelper.throwException("当前寄回件状态不支持再次修改寄回信息");
        }

        sendBack.setExpressNumber(dto.getExpressNumber());
        sendBack.setExpressCorp(dto.getExpressCorp());

        sendBackService.updateById(sendBack);
    }

    public void subscribeBySendBack(String sendBackSn) {

        LogisticsSendBack sendBack = getBySendBackSn(sendBackSn);

        ExpressSubscribeDirectlyDTO subscribeDTO = new ExpressSubscribeDirectlyDTO();
        subscribeDTO.setExpressNumber(sendBack.getExpressNumber());
        subscribeDTO.setMobile(sendBack.getSendPhone());
        expressBusiness.subscribeExpressDirectly(subscribeDTO);
    }

    public void checkReceiveOvertime(Integer overtimeHour) {
        // 查询一个月内未签收的寄回件
        List<LogisticsSendBack> sendBackList = sendBackService.getWaitRecord(LocalDateTime.now().minusMonths(1));

        if (ObjectUtils.isEmpty(sendBackList)) {
            return;
        }
        log.info("待检查寄回件数量：" + sendBackList.size());
        List<String> expressNumList = sendBackList.stream().map(LogisticsSendBack::getExpressNumber).collect(Collectors.toList());

        // 批量查询物流轨迹
        ExpressBatchQueryDTO batchQueryDTO = new ExpressBatchQueryDTO();
        batchQueryDTO.setExpressNumberList(expressNumList);
        List<ExpressBatchQueryVO> expressBatchQueryVOList = expressBusiness.batchQuery(batchQueryDTO);

        if (ObjectUtils.isEmpty(expressBatchQueryVOList)) {
            return;
        }
        log.info("待检查物流轨迹数量：" + expressBatchQueryVOList.size());

        Map<String, ExpressBatchQueryVO> stateMap = expressBatchQueryVOList.stream()
                .collect(Collectors.toMap(ExpressBatchQueryVO::getExpressNumber, Function.identity(), (v1, v2) -> v2));

        sendBackList.forEach(sendBack -> {
            ExpressBatchQueryVO expressBatchQueryVO = stateMap.get(sendBack.getExpressNumber());
            if (expressBatchQueryVO == null) {
                log.info("【检查寄回件超时未签收】未查询到物流轨迹：" + sendBack.getExpressNumber());
                return;
            }

            // 签收超过24小时
            if (expressBatchQueryVO.getState().equals(ExpressStateEnum.RECEIVED.getValue()) &&
                    ObjectUtils.isNotEmpty(expressBatchQueryVO.getReceivedTime()) &&
                    expressBatchQueryVO.getReceivedTime().isBefore(LocalDateTime.now().minusHours(overtimeHour))
            ) {
                String msg = "寄回件超过" + overtimeHour + "小时未签收，物流签收时间：" + expressBatchQueryVO.getReceivedTime();
                sendBackService.updateReceiveInfo(sendBack.getSendbackSn(),
                        SendBackReceiveStatusEnum.RECEIVE_STATUS_OVERTIME.getValue(),
                        msg, null);

                SendBackLogBO logBO = new SendBackLogBO();
                logBO.setSendbackId(sendBack.getId());
                logBO.setType(SendBackLogTypeEnum.TYPE_MODIFY.getValue());
                logBO.setOperator("system");
                logBO.setOperateContent(msg);
                sendBackLogService.addLog(logBO);
            }
        });
    }

    public IPage<SendBackListVO> getList(SendBackListDTO listDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        LogisticsSendBackPageBO sendBackPageBO = BeanUtil.copyProperties(listDTO, LogisticsSendBackPageBO.class,
                "createStartTime", "createEndTime", "receiveStartTime", "receiveEndTime");

        // 创建时间处理
        if (ObjectUtil.isNotEmpty(listDTO.getCreateStartTime())) {
            sendBackPageBO.setCreateStartTime(listDTO.getCreateStartTime().atStartOfDay());
        }
        if (ObjectUtil.isNotEmpty(listDTO.getCreateEndTime())) {
            sendBackPageBO.setCreateEndTime(listDTO.getCreateEndTime().atTime(23, 59, 59));
        }

        // 签收时间处理
        if (ObjectUtil.isNotEmpty(listDTO.getReceiveStartTime())) {
            sendBackPageBO.setReceiveStartTime(listDTO.getReceiveStartTime().atStartOfDay());
        }
        if (ObjectUtil.isNotEmpty(listDTO.getReceiveEndTime())) {
            sendBackPageBO.setReceiveEndTime(listDTO.getReceiveEndTime().atTime(LocalTime.MAX));
        }

        // 仓储
        if (ObjectUtils.isNotEmpty(user) && StringUtils.isNotEmpty(user.getStorageCode())) {
            String[] storageCodeSplit = user.getStorageCode().split(",");
            sendBackPageBO.setStorageCodeList(Arrays.asList(storageCodeSplit));// 选中的仓库没有权限
            if (StringUtils.isNotEmpty(listDTO.getStorageCode()))
                if (!sendBackPageBO.getStorageCodeList().contains(listDTO.getStorageCode())) {
                    return new Page<>();
                } else {
                    sendBackPageBO.setStorageCodeList(Collections.singletonList(listDTO.getStorageCode()));
                }
        }
        return sendBackService.getPage(sendBackPageBO).convert(sendBack -> {
            SendBackListVO listVO = BeanUtil.copyProperties(sendBack, SendBackListVO.class);
            listVO.setIssuerIdStr(deliveryConfig.getCnNameByIssuerId(sendBack.getIssuerId()));
            // 查询商品名称
            SupplyGoods goods = supplyGoodsService.getOneByGoodsCode(sendBack.getGoodsCode());
            if (ObjectUtils.isNotEmpty(goods)) {
                listVO.setGoodsName(goods.getGoodsName());
            }
            // 查询快递状态
            Express express = expressBusiness.getExpressByNumberAndPhone(sendBack.getExpressNumber(), sendBack.getRevicePhone());
            if (ObjectUtils.isNotEmpty(express)) {
                listVO.setExpressStateStr(ExpressStateEnum.map.getOrDefault(express.getState(), "--"));
            }
            return listVO;
        });
    }

    public SendBackDetailVO getDetail(SendBackDetailDTO detailDTO) {
        LogisticsSendBack sendBack = sendBackService.getById(detailDTO.getId());
        if (ObjectUtils.isEmpty(sendBack)) {
            ToolsHelper.throwException("寄回件记录不存在");
        }

        SendBackDetailVO detailVO = new SendBackDetailVO();
        SendBackDetailVO.SendInfo sendInfo = new SendBackDetailVO.SendInfo();
        SendBackDetailVO.GoodsInfo goodsInfo = new SendBackDetailVO.GoodsInfo();
        SendBackDetailVO.ReceiveInfo receiveInfo = new SendBackDetailVO.ReceiveInfo();

        BeanUtil.copyProperties(sendBack, sendInfo);
        BeanUtil.copyProperties(sendBack, goodsInfo);
        goodsInfo.setIssuerIdStr(deliveryConfig.getCnNameByIssuerId(sendBack.getIssuerId()));
        // 查询商品名称
        SupplyGoods goods = supplyGoodsService.getOneByGoodsCode(sendBack.getGoodsCode());
        if (ObjectUtils.isNotEmpty(goods)) {
            goodsInfo.setGoodsName(goods.getGoodsName());
        }
        BeanUtil.copyProperties(sendBack, receiveInfo);

        detailVO.setSendInfo(sendInfo);
        detailVO.setGoodsInfo(goodsInfo);
        detailVO.setReceiveInfo(receiveInfo);
        return detailVO;
    }

    public IPage<SendBackLogVO> getLog(SendBackLogDTO logDTO) {
        IPage<LogisticsSendbackLog> page = sendBackLogService.getPage(logDTO.getId(), logDTO.getPageNum(), logDTO.getPageSize());
        return page.convert(log -> BeanUtil.copyProperties(log, SendBackLogVO.class));
    }

    public void check(SendBackCheckDTO checkDTO) {
        LogisticsSendBack sendBack = sendBackService.getById(checkDTO.getId());
        if (ObjectUtils.isEmpty(sendBack)) {
            ToolsHelper.throwException("寄回件记录不存在");
        }

        if (!Arrays.asList(SendBackReceiveStatusEnum.RECEIVE_STATUS_WAIT.getValue(),
                SendBackReceiveStatusEnum.RECEIVE_STATUS_OVERTIME.getValue())
                .contains(sendBack.getReviceStatus())) {
            ToolsHelper.throwException("该订单已被处理");
        }

        // 查找入库记录
        StorageRecord storageRecord = storageRecordService.getByExpressNumber(sendBack.getExpressNumber());
        if (ObjectUtils.isNotEmpty(storageRecord)) {
            checkAndUpdate(sendBack, storageRecord);
        }
    }

    public void done(SendBackDoneDTO doneDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        LogisticsSendBack sendBack = sendBackService.getById(doneDTO.getId());
        if (ObjectUtils.isEmpty(sendBack)) {
            ToolsHelper.throwException("寄回件记录不存在");
        }

        if (Arrays.asList(SendBackReceiveStatusEnum.RECEIVE_STATUS_WAIT.getValue(),
                        SendBackReceiveStatusEnum.RECEIVE_STATUS_NORMAL.getValue())
                .contains(sendBack.getReviceStatus())) {
            ToolsHelper.throwException("该订单未入库或已被处理");
        }

        // 更新签收状态
        Integer receiveStatus = doneDTO.getCheckStatus().equals(1) ?
                SendBackReceiveStatusEnum.RECEIVE_STATUS_NORMAL.getValue() :
                SendBackReceiveStatusEnum.RECEIVE_STATUS_ABNORMAL.getValue();

        LocalDateTime receiveTime = doneDTO.getCheckStatus().equals(1) ? LocalDateTime.now() : null;

        sendBackService.updateReceiveInfo(sendBack.getSendbackSn(), receiveStatus, doneDTO.getReceiveRemark(), receiveTime);

        // 记录日志
        String msg = "订单已被" + user.getRealName() + "检查确认为：" + SendBackReceiveStatusEnum.map.getOrDefault(receiveStatus, "--");
        SendBackLogBO logBO = new SendBackLogBO();
        logBO.setSendbackId(sendBack.getId());
        logBO.setType(SendBackLogTypeEnum.TYPE_CHECK.getValue());
        logBO.setOperator("system");
        logBO.setOperateContent(msg);
        sendBackLogService.addLog(logBO);

        // 发起检查通知 处理正常才发起通知
        if (receiveStatus.equals(SendBackReceiveStatusEnum.RECEIVE_STATUS_NORMAL.getValue())) {
            retryNotify(sendBack.getSendbackSn());
        }
    }

    public void notifyResult(SendBackNotifyDTO notifyDTO) {
        LogisticsSendBack sendBack = sendBackService.getById(notifyDTO.getId());
        if (ObjectUtils.isEmpty(sendBack)) {
            ToolsHelper.throwException("寄回件记录不存在");
        }

        retryNotify(sendBack.getSendbackSn());
    }
}
