package com.ets.delivery.application.common.consts.pickUp;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.IntStream;
import java.util.LinkedHashMap;

@Getter
@AllArgsConstructor
public enum PickUpDetailStatusEnum {

    STATUS_NORMAL(1, "正常"),
    STATUS_DELETE(-1, "删除");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        PickUpDetailStatusEnum[] enums = PickUpDetailStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
