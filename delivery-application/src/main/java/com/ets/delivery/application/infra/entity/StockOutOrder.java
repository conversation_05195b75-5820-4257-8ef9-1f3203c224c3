package com.ets.delivery.application.infra.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import com.ets.delivery.application.common.bo.stock.StockOutExtraBO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 出库单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_stock_out_order")
public class StockOutOrder extends BaseEntity<StockOutOrder> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 出库单号
     */
    private String stockOutSn;

    /**
     * 仓库编号
     */
    private String storageCode;

    /**
     * 出库类型
     */
    private Integer type;

    /**
     * 商品属性
     */
    private Integer goodsQuality;

    /**
     * 出库状态
     */
    private Integer status;

    /**
     * 实际出库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime outTime;

    /**
     * 申请出库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 图片，逗号分隔
     */
    private String images;

    /**
     * 发货类型
     */
    private Integer deliveryType;

    /**
     * 收件人
     */
    private String receiveName;

    /**
     * 收件手机号
     */
    private String receivePhone;

    /**
     * 收件地区
     */
    private String receiveArea;

    /**
     * 收件地址
     */
    private String receiveAddress;

    /**
     * 运单号
     */
    private String expressCode;

    private String extra;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    public List<String> getImageList() {

        if (StringUtils.isEmpty(images)) {
            return null;
        }

        return Arrays.asList(images.split(","));
    }

    /**
     * 商品信息
     */
    @TableField(exist = false)
    private List<StockGoodsInfo> goodsInfo;

    public StockOutExtraBO getExtraBO() {

        if (StringUtils.isNotEmpty(extra)) {

            return JSON.parseObject(extra, StockOutExtraBO.class);
        } else {
            return new StockOutExtraBO();
        }
    }

}
