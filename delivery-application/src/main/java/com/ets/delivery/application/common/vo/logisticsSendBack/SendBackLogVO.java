package com.ets.delivery.application.common.vo.logisticsSendBack;

import com.ets.delivery.application.common.consts.sendback.SendBackLogTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SendBackLogVO {

    private Integer id;
    private String operateContent;
    private String operator;
    private String type;
    private String typeStr;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    public String getTypeStr() {
        return SendBackLogTypeEnum.map.getOrDefault(type, "未知");
    }
}
