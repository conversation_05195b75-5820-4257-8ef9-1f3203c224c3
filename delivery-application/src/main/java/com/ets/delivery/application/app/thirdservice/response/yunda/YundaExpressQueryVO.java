package com.ets.delivery.application.app.thirdservice.response.yunda;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class YundaExpressQueryVO {
    Boolean result;
    String code;
    String message;
    ResultData data;

    @Data
    public static class ResultData {
        Boolean result;
        String time;
        String mailno;
        String remark;
        String status;
        List<Step> steps;

        @Data
        public static class Step {
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
            LocalDateTime time;
            String action;
            Integer station;
            String stationName;
            Integer stationType;
            Integer next;
            String nextName;
            Integer nextType;
            String description;
            String phone;
            String signer;
        }
    }
}
