package com.ets.delivery.application.common.dto.storageMap;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class GetStorageSkuBySkuSnDTO {

    @NotBlank
    private String storageCode = "Yunda";

    @NotNull
    @NotEmpty
    private List<String> skuSnList;
}
