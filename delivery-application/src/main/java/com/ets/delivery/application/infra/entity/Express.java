package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 物流单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Express extends BaseEntity<Express> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 物流商编码
     */
    private String expressCode;

    /**
     * 物流单号
     */
    private String expressSn;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司编码
     */
    private String expressCompany;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 物流单状态0：未订阅 ，1：已订阅，2：订阅失败
     */
    private Integer subscribeStatus;

    /**
     * 快递单当前状态，包括 0 在途，1 揽收，2 疑难，3 签收，4 退签，5 派件，6 退回，7 转单，10 待清关，11 清关中，12 已清关，13 清关异常，14 收件人拒签等 13 个状态
     */
    private Integer state;

    /**
     * 快递最新地址
     */
    private String lastArea;

    /**
     * 订阅失败次数
     */
    private Integer subscribeFailedTime;

    /**
     * 快递最新更新时间
     */
    private LocalDateTime lastExpressTime;

    /**
     * 快递最新信息
     */
    private String lastContext;

    /**
     * 物流最新状态
     */
    private String lastStatus;

    /**
     * 快递最初地址
     */
    private String firstArea;

    /**
     * 快递最初更新时间
     */
    private LocalDateTime firstExpressTime;

    /**
     * 快递最初信息
     */
    private String firstContext;

    /**
     * 物流最初状态
     */
    private String firstStatus;

    /**
     * 签收时间
     */
    private LocalDateTime receivedTime;

    /**
     * 接受物流的详细信息
     */
    private String data;

    /**
     * 是否已寄回
     */
    private Integer isBack;

    /**
     * 寄回时间
     */
    private LocalDateTime backTime;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;


}
