package com.ets.delivery.application.common.dto.setting;

import com.ets.delivery.application.common.consts.setting.SettingCategoryEnum;
import com.ets.delivery.application.common.consts.setting.SettingGuideEnum;
import com.ets.delivery.application.common.consts.setting.SettingKeyEnum;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class SettingAddDTO {

    /**
     * 类型[0-通用 1-客车 2-货车]
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 大类别
     */
    @NotBlank(message = "category不能为空")
    private String category;

    /**
     * 键值key
     */
    @NotBlank(message = "key不能为空")
    private String key;

    /**
     * 渠道
     */
    @NotBlank(message = "value不能为空")
    private String value;

    /**
     * 操作参数
     */
    private String params = "";

    /**
     * 引导
     */
    private String guide = "default";

    /**
     * 排序
     */
    private Integer sort = 0;

    public boolean checkCategory() {
        return SettingCategoryEnum.list.contains(this.category);
    }

    public boolean checkKey() {
        return SettingKeyEnum.list.contains(this.key);
    }

    public boolean checkGuide() {
        return SettingGuideEnum.list.contains(this.guide);
    }
}
