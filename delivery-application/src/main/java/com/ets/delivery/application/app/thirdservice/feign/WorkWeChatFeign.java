package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.WorkWeChatFallbackFactory;
import com.ets.delivery.application.app.thirdservice.response.workWeChat.WorkWeChatSendVO;
import com.ets.delivery.application.common.config.FeignSupportConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;


@FeignClient(
        url = "https://qyapi.weixin.qq.com",
        name = "WorkWeChatFeign",
        fallbackFactory = WorkWeChatFallbackFactory.class,
        configuration = FeignSupportConfig.class
)
public interface WorkWeChatFeign {

    @PostMapping(value = "/cgi-bin/webhook/send", consumes = MediaType.APPLICATION_JSON_VALUE)
    WorkWeChatSendVO send(@RequestBody String json, @RequestParam String key);

    @PostMapping(value = "/cgi-bin/webhook/upload_media", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    String uploadMedia(@RequestPart(value = "file") MultipartFile file, @RequestParam String key, @RequestParam String type);
}
