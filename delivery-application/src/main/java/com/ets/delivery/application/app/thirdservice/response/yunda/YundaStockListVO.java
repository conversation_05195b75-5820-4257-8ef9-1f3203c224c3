package com.ets.delivery.application.app.thirdservice.response.yunda;

import lombok.Data;

import java.util.List;

@Data
public class YundaStockListVO {
    private String code;
    private Boolean success;
    private String msg;
    private List<StockListData> data;

    @Data
    public static class StockListData {
        /**
         * 商品编码
         */
        private String sku;
        /**
         * 仓库编码
         */
        private String warehouseCode;
        /**
         * 客户编码
         */
        private String owner;
        /**
         * 日期
         */
        private String sysdate;
        /**
         * 库存类型
         */
        private String invstat;
        /**
         * 期初库存
         */
        private Integer startqty;
        /**
         * 期末库存
         */
        private Integer endqty;
        /**
         * 入库总数量
         */
        private Integer inqty;
        /**
         * 出库总数量
         */
        private Integer outqty;
    }
}
