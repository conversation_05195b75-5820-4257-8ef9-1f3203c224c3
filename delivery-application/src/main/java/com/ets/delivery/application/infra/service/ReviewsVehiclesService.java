package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.ReviewsVehicles;
import com.ets.delivery.application.infra.mapper.ReviewsVehiclesMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 审核行驶证数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
@DS("db-issuer-admin-proxy")
public class ReviewsVehiclesService extends BaseService<ReviewsVehiclesMapper, ReviewsVehicles> {

    public ReviewsVehicles getOneByReviewSn(String reviewSn) {
        Wrapper<ReviewsVehicles> wrapper = Wrappers.<ReviewsVehicles>lambdaQuery()
                .eq(ReviewsVehicles::getReviewSn, reviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
