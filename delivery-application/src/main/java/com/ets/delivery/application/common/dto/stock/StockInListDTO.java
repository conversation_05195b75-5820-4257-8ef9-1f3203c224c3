package com.ets.delivery.application.common.dto.stock;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

@Data
public class StockInListDTO {

    /**
     * 入库单号
     */
    private String stockInSn;

    /**
     * 入库状态
     */
    private Integer status;

    /**
     * 入库类型
     */
    private Integer type;

    /**
     * 商品属性
     */
    private Integer goodsQuality;

    /**
     * 物流公司的商品编号
     */
    private String deliveryGoodsCode;

    /**
     * 申请入库时间
     */
    private String applyTimeBegin;

    private String applyTimeEnd;

    /**
     * 实际入库时间
     */
    private String inTimeBegin;

    private String inTimeEnd;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;

}
