package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 审核人员后审统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_post_review_user_summary")
public class PostReviewUserSummary extends BaseEntity<PostReviewUserSummary> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 审核日期
     */
    private LocalDate reviewDate;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
