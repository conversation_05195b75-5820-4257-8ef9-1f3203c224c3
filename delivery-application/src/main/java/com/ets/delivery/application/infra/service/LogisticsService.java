package com.ets.delivery.application.infra.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.bo.logistics.LogisticsOneBO;
import com.ets.delivery.application.common.bo.logistics.LogisticsPageBO;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsExpressStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOrderTypeEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsStatusEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.dto.logistics.LogisticsAcceptDTO;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.mapper.LogisticsMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 发货单列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Service
@DS("db-issuer-admin-proxy")
public class LogisticsService extends BaseService<LogisticsMapper, Logistics> {

    @Autowired
    LogisticsMapper logisticsMapper;

    public Logistics getByLogisticsSn(String logisticsSn) {
        Wrapper<Logistics> wrapper = Wrappers.<Logistics>lambdaQuery()
                .eq(Logistics::getLogisticsSn, logisticsSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public Logistics getLatestOneByOrderSn(String orderSn) {
        Wrapper<Logistics> wrapper = Wrappers.<Logistics>lambdaQuery()
                .eq(Logistics::getOrderSn, orderSn)
                .orderByDesc(Logistics::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public Logistics getByExpressNumber(String expressNumber) {
        Wrapper<Logistics> wrapper = Wrappers.<Logistics>lambdaQuery()
                .eq(Logistics::getExpressNumber, expressNumber)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public Logistics getLogisticsSumByGoodsCode(String goodsCode, LocalDateTime startDate, LocalDateTime endDate) {
        QueryWrapper<Logistics> wrapper = new QueryWrapper<>();
        wrapper.select("sum(nums) as sumGoodsNums")
                .lambda()
                .eq(Logistics::getGoodsCode, goodsCode)
                .eq(Logistics::getStatus, LogisticsStatusEnum.STATUS_NORMAL.getValue())
                .eq(Logistics::getDeliveryStatus, LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue())
                .between(Logistics::getDeliveryTime, startDate, endDate);
        return this.baseMapper.selectOne(wrapper);
    }

    public List<Logistics> getLogisticsSumByGoodsCodeList(String storageCode, List<String> goodsCodeList, LocalDateTime startDate, LocalDateTime endDate) {
        QueryWrapper<Logistics> wrapper = new QueryWrapper<>();
        wrapper.select("goods_code, sum(nums) as sumGoodsNums")
                .lambda()
                .in(Logistics::getGoodsCode, goodsCodeList)
                .eq(Logistics::getStorageCode, storageCode)
                .eq(Logistics::getStatus, LogisticsStatusEnum.STATUS_NORMAL.getValue())
                .eq(Logistics::getDeliveryStatus, LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue())
                .between(Logistics::getDeliveryTime, startDate, endDate)
                .groupBy(Logistics::getGoodsCode);
        return this.baseMapper.selectList(wrapper);
    }

    public List<Logistics> getLogisticsSnListByDeliveryTime(String storageCode, LocalDateTime startDate, LocalDateTime endDate) {
        Wrapper<Logistics> wrapper = Wrappers.<Logistics>lambdaQuery()
                .select(Logistics::getLogisticsSn)
                .eq(Logistics::getGoodsCode, "")
                .eq(Logistics::getStorageCode, storageCode)
                .eq(Logistics::getStatus, LogisticsStatusEnum.STATUS_NORMAL.getValue())
                .eq(Logistics::getDeliveryStatus, LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue())
                .between(Logistics::getDeliveryTime, startDate, endDate);
        return this.baseMapper.selectList(wrapper);
    }

    /*
     * 创建发货单
     */
    public  Logistics createLogistics(String logisticsSn,LogisticsAcceptDTO logisticsAcceptDTO){
        Logistics logistics = BeanUtil.copyProperties(logisticsAcceptDTO, Logistics.class);
        logistics.setLogisticsSn(logisticsSn);
        logistics.setDrawTime(LocalDateTime.now());
        logistics.setCreatedAt(LocalDateTime.now());
        logistics.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(logistics);
        return logistics;
    }

    public IPage<Logistics> getPage(LogisticsPageBO pageBO) {
        Wrapper<Logistics> wrapper = Wrappers.<Logistics>query()
                .eq(ObjectUtils.isNotEmpty(pageBO.getIssuerId()), "l.issuer_id", pageBO.getIssuerId())
                .eq(ObjectUtils.isNotEmpty(pageBO.getOrderSource()), "l.order_source", pageBO.getOrderSource())
                .eq(ObjectUtils.isNotEmpty(pageBO.getOrderType()), "l.order_type", pageBO.getOrderType())
                .eq(ObjectUtils.isNotEmpty(pageBO.getOriginOrderSn()), "l.origin_order_sn", pageBO.getOriginOrderSn())
                .eq(ObjectUtils.isNotEmpty(pageBO.getPlateNo()), "l.plate_no", pageBO.getPlateNo())

                .and(ObjectUtils.isNotEmpty(pageBO.getOrderSn()),
                        qr ->
                        qr.eq("l.order_sn", pageBO.getOrderSn())
                        .or()
                        .eq("l.origin_business_sn", pageBO.getOrderSn())
                )

                .eq(ObjectUtils.isNotEmpty(pageBO.getSendPhone()), "l.send_phone", pageBO.getSendPhone())
                .eq(ObjectUtils.isNotEmpty(pageBO.getExpressNumber()), "l.express_number", pageBO.getExpressNumber())
                .eq(ObjectUtils.isNotEmpty(pageBO.getReason()), "l.reason", pageBO.getReason())
                .eq(ObjectUtils.isNotEmpty(pageBO.getOperator()), "l.operator", pageBO.getOperator())
                .in(ObjectUtils.isNotEmpty(pageBO.getOperatorList()), "l.operator", pageBO.getOperatorList())
                .eq(ObjectUtils.isNotEmpty(pageBO.getStatus()), "l.status", pageBO.getStatus())
                .eq(ObjectUtils.isNotEmpty(pageBO.getDeliveryStatus()), "l.delivery_status", pageBO.getDeliveryStatus())
                .in(ObjectUtils.isNotEmpty(pageBO.getDeliveryStatusList()), "l.delivery_status", pageBO.getDeliveryStatusList())
                .eq(ObjectUtils.isNotEmpty(pageBO.getNotifyStatus()), "l.notify_status", pageBO.getNotifyStatus())
                .eq(ObjectUtils.isNotEmpty(pageBO.getExpressStatus()), "l.express_status", pageBO.getExpressStatus())
                .in(ObjectUtils.isNotEmpty(pageBO.getLogisticsSnList()), "l.logistics_sn", pageBO.getLogisticsSnList())
                .notIn(ObjectUtils.isNotEmpty(pageBO.getNotLogisticsSnList()), "l.logistics_sn", pageBO.getNotLogisticsSnList())
                .in(ObjectUtils.isNotEmpty(pageBO.getStorageCodeList()), "l.storage_code", pageBO.getStorageCodeList())
                .in(ObjectUtils.isNotEmpty(pageBO.getStorageSkuList()), "ls.storage_sku", pageBO.getStorageSkuList())
                .ge(ObjectUtils.isNotEmpty(pageBO.getCreateStartTime()), "l.created_at", pageBO.getCreateStartTime())
                .le(ObjectUtils.isNotEmpty(pageBO.getCreateEndTime()), "l.created_at", pageBO.getCreateEndTime())
                .ge(ObjectUtils.isNotEmpty(pageBO.getDeliveryStartTime()), "l.delivery_time", pageBO.getDeliveryStartTime())
                .le(ObjectUtils.isNotEmpty(pageBO.getDeliveryEndTime()), "l.delivery_time", pageBO.getDeliveryEndTime())
                .ge(ObjectUtils.isNotEmpty(pageBO.getPushStartTime()), "l.push_time", pageBO.getPushStartTime())
                .le(ObjectUtils.isNotEmpty(pageBO.getPushEndTime()), "l.push_time", pageBO.getPushEndTime())
                .orderByDesc("l.created_at");

        // 需要查询sku才做连表查询
        if (ObjectUtils.isNotEmpty(pageBO.getStorageSkuList())) {
            return logisticsMapper.getLogisticJoinSkuPage(new Page<>(pageBO.getPageNum(), pageBO.getPageSize()), wrapper);
        } else {
            return logisticsMapper.getLogisticPage(new Page<>(pageBO.getPageNum(), pageBO.getPageSize()), wrapper);
        }
    }

    public Logistics getOneByCondition(LogisticsOneBO oneBO) {
        Wrapper<Logistics> wrapper = Wrappers.<Logistics>lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(oneBO.getId()), Logistics::getId, oneBO.getId())
                .eq(ObjectUtils.isNotEmpty(oneBO.getPlateNo()), Logistics::getPlateNo, oneBO.getPlateNo())
                .eq(oneBO.getOperator() != null, Logistics::getOperator, oneBO.getOperator())
                .eq(ObjectUtils.isNotEmpty(oneBO.getOriginOrderSn()), Logistics::getOriginOrderSn, oneBO.getOriginOrderSn())
                .eq(ObjectUtils.isNotEmpty(oneBO.getStatus()), Logistics::getStatus, oneBO.getStatus())
                .in(ObjectUtils.isNotEmpty(oneBO.getDeliveryStatusList()), Logistics::getDeliveryStatus, oneBO.getDeliveryStatusList())
                .ge(ObjectUtils.isNotEmpty(oneBO.getPushStartTime()), Logistics::getPushTime, oneBO.getPushStartTime())
                .le(ObjectUtils.isNotEmpty(oneBO.getPushEndTime()), Logistics::getCreatedAt, oneBO.getPushEndTime())
                .orderByDesc(Logistics::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public List<Logistics> getManualLogisticsSaveList() {
        Wrapper<Logistics> wrapper = Wrappers.<Logistics>lambdaQuery()
                .eq(Logistics::getDeliveryStatus, LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue())
                .eq(Logistics::getStatus, LogisticsStatusEnum.STATUS_NORMAL.getValue())
                .eq(Logistics::getOrderType, LogisticsOrderTypeEnum.MANUAL.getValue())
                .last("limit 1000");
        return this.baseMapper.selectList(wrapper);
    }

    public List<Logistics> getOverTimeNotSignList() {
        Wrapper<Logistics> wrapper = Wrappers.<Logistics>lambdaQuery()
                .eq(Logistics::getDeliveryStatus, LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue())
                .eq(Logistics::getStatus, LogisticsStatusEnum.STATUS_NORMAL.getValue())
                .eq(Logistics::getStorageCode, StorageCodeEnum.YUNDA.getValue())
                .ne(Logistics::getExpressStatus, LogisticsExpressStatusEnum.SIGNED.getValue())
                .gt(Logistics::getDeliveryTime, LocalDateTime.now().minusDays(90))
                .lt(Logistics::getDeliveryTime, LocalDateTime.now().minusDays(10))
                .last("limit 3000");
        return this.baseMapper.selectList(wrapper);
    }

    public List<Logistics> getStockOutLogistics(String deliveryRemark, Integer status, LocalDateTime startTime) {
        Wrapper<Logistics> wrapper = Wrappers.<Logistics>lambdaQuery()
                .eq(Logistics::getDeliveryRemark, deliveryRemark)
                .eq(Logistics::getStatus, status)
                .ge(Logistics::getCreatedAt, startTime);
        return this.baseMapper.selectList(wrapper);
    }
}
