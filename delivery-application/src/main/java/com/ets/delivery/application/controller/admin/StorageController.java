package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.ManageBusiness;
import com.ets.delivery.application.common.dto.manage.ManageStorageSkuMapDTO;
import com.ets.delivery.application.common.dto.manage.ManageStorageSkuMapEditDTO;
import com.ets.delivery.application.common.dto.manage.ManageStorageSkuMapIdDTO;
import com.ets.delivery.application.common.vo.SelectOptionsVO;
import com.ets.delivery.application.common.vo.manage.StorageSkuMapListVO;
import com.ets.delivery.application.infra.entity.StorageSkuMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/storage")
public class StorageController {
    @Autowired
    ManageBusiness manageBusiness;

    @RequestMapping("/getStorageSkuMapList")
    public JsonResult<IPage<StorageSkuMapListVO>> getStorageSkuMapList(@RequestBody @Validated ManageStorageSkuMapDTO listDTO) {
        return JsonResult.ok(manageBusiness.getStorageSkuMapList(listDTO));
    }
    @RequestMapping("/editStorageSkuMap")
    public JsonResult<Boolean> editStorageSkuMap(@RequestBody @Validated ManageStorageSkuMapEditDTO manageStorageSkuMapEditDTO){
        return JsonResult.ok(manageBusiness.editStorageSkuMapList(manageStorageSkuMapEditDTO));
    }
    /*
     * 通过id获取详情
     */
    @RequestMapping("/getStorageSkuMapById")
    public JsonResult<StorageSkuMap> getStorageSkuMapById(@RequestBody @Validated ManageStorageSkuMapIdDTO manageStorageSkuMapIdDTO){
        return JsonResult.ok(manageBusiness.getStorageSkuMapById(manageStorageSkuMapIdDTO.getId()));
    }

    @PostMapping("/getSelectOptions")
    @ResponseBody
    public JsonResult<Map<String, List<SelectOptionsVO>>> getSelectOptions(){
        return JsonResult.ok(manageBusiness.getSelectOptions());
    }
}
