package com.ets.delivery.application.common.consts.logistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum LogisticsNotifyStatusEnum {

    NOTIFY_STATUS_WAIT(0, "待通知"),
    NOTIFY_STATUS_PROCESSING(1, "通知中"),
    NOTIFY_STATUS_SUCCESS(2, "通知成功"),
    NOTIFY_STATUS_FAIL(3, "通知失败"),
    NOTIFY_STATUS_CANCEL(4, "通知取消");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        LogisticsNotifyStatusEnum[] enums = LogisticsNotifyStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
