package com.ets.delivery.application.app.thirdservice.response.jd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class JdExpressTraceGetVO extends JdErrorResponseVO {

    @JSONField(name = "jingdong_ldop_receive_trace_get_responce")
    private JdTraceGetResponseVO jdExpressTraceGetResponse;

    @Data
    public static class JdTraceGetResponseVO {

        private String code;

        @JSONField(name = "querytrace_result")
        private QueryTraceResult result;

        @Data
        public static class QueryTraceResult {

            private Number code;

            private String messsage;

            private List<Trace> data;

            @Data
            public static class Trace {
                private String opeTitle;
                private String opeRemark;
                private String opeName;
                private String opeTime;
                private String waybillCode;
                private String courier;
                private String courierTel;
            }
        }
    }
}
