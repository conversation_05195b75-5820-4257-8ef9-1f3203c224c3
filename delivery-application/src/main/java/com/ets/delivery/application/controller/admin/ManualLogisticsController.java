package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.ManualLogisticsBusiness;
import com.ets.delivery.application.app.business.ManualLogisticsImportBusiness;
import com.ets.delivery.application.common.dto.manualLogistics.*;
import com.ets.delivery.application.common.vo.manualLogistics.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/admin/manualLogistics")
public class ManualLogisticsController {

    @Autowired
    private ManualLogisticsBusiness manualLogisticsBusiness;

    @Autowired
    private ManualLogisticsImportBusiness importBusiness;

    @RequestMapping("/getList")
    public JsonResult<IPage<ManualLogisticsListVO>> getList(@RequestBody @Valid ManualLogisticsListDTO listDTO) {
        IPage<ManualLogisticsListVO> list = manualLogisticsBusiness.getList(listDTO);
        return JsonResult.ok(list);
    }

    @RequestMapping("/getDetail")
    public JsonResult<ManualLogisticsDetailVO> getDetail(@RequestBody @Valid ManualLogisticsDetailDTO detailDTO) {
        return JsonResult.ok(manualLogisticsBusiness.getDetail(detailDTO));
    }

    @RequestMapping("/getLog")
    public JsonResult<IPage<ManualLogisticsLogListVO>> getLog(@RequestBody @Valid ManualLogisticsLogListDTO logListDTO) {
        return JsonResult.ok(manualLogisticsBusiness.getLog(logListDTO));
    }

    @RequestMapping("/getOriginLogisticsInfo")
    public JsonResult<ManualLogisticsOriginInfoVO> getOriginLogisticsInfo(@RequestBody @Valid ManualLogisticsOriginInfoDTO originInfoDTO) {
        return JsonResult.ok(manualLogisticsBusiness.getOriginLogisticsInfo(originInfoDTO));
    }

    @RequestMapping("/getGoodsInfo")
    public JsonResult<ManualLogisticsGoodsInfoVO> getGoodsInfo(@RequestBody @Valid ManualLogisticsGoodsInfoDTO goodsInfoDTO) {
        return JsonResult.ok(manualLogisticsBusiness.getGoodsInfo(goodsInfoDTO));
    }

    @RequestMapping("/save")
    public JsonResult<ManualLogisticsSaveVO> save(@RequestBody @Valid ManualLogisticsSaveDTO saveDTO) {
        return JsonResult.ok(manualLogisticsBusiness.save(saveDTO));
    }

    @RequestMapping("/edit")
    public JsonResult<ManualLogisticsEditVO> edit(@RequestBody @Valid ManualLogisticsEditDTO editDTO) {
        return JsonResult.ok(manualLogisticsBusiness.edit(editDTO));
    }

    @RequestMapping("/push")
    public JsonResult<ManualLogisticsPushVO> push(@RequestBody @Valid ManualLogisticsPushDTO pushDTO) {
        return JsonResult.ok(manualLogisticsBusiness.push(pushDTO));
    }

    @RequestMapping("/cancel")
    public JsonResult<?> cancel(@RequestBody @Valid ManualLogisticsCancelDTO cancelDTO) {
        manualLogisticsBusiness.cancel(cancelDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/importFile")
    public JsonResult<ManualLogisticsImportVO> importFile(MultipartFile file) {
        return JsonResult.ok(importBusiness.logisticsImport(file));
    }

    @RequestMapping("/getImportDataList")
    public JsonResult<IPage<ManualLogisticsImportListVO>> getImportDataList(@RequestBody @Valid ManualLogisticsImportListDTO importListDTO) {
        return JsonResult.ok(manualLogisticsBusiness.getImportDataList(importListDTO));
    }

    @RequestMapping("/batchSave")
    public JsonResult<?> batchSave(@RequestBody @Valid ManualLogisticsBatchSaveDTO batchSaveDTO) {
        manualLogisticsBusiness.batchSave(batchSaveDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/batchPush")
    public JsonResult<?> batchPush(@RequestBody ManualLogisticsBatchPushDTO batchPush) {
        if (!batchPush.checkParams()) {
            ToolsHelper.throwException("批次号或id不能为空");
        }
        manualLogisticsBusiness.batchPush(batchPush);
        return JsonResult.ok();
    }

    @RequestMapping("/deleteImportRecord")
    public JsonResult<?> deleteImportRecord(@RequestBody @Valid ManualLogisticsImportDetailDeleteDTO deleteDTO) {
        manualLogisticsBusiness.deleteImportRecord(deleteDTO);
        return JsonResult.ok();
    }
}
