package com.ets.delivery.application.common.utils;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

public class ExcelColumnMergeStrategy extends AbstractMergeStrategy {
    // 合并列
    private final int[] mergeColumnIndex;
    // 合并参考列
    private final int referMergeColIndex;

    public ExcelColumnMergeStrategy(int[] mergeColumnIndex, int referMergeColIndex) {
        this.mergeColumnIndex = mergeColumnIndex;
        this.referMergeColIndex = referMergeColIndex;
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer integer) {
        // 当前列包含在合并列中 循环每列 参考列的当前行与上一行相同则合并
        for (int columnIndex : mergeColumnIndex) {
            if (columnIndex == cell.getColumnIndex()) {
                Cell referCell = sheet.getRow(cell.getRowIndex()).getCell(referMergeColIndex);
                Cell preCell = sheet.getRow(cell.getRowIndex() - 1).getCell(referMergeColIndex);
                Object referData = referCell.getCellType() == CellType.STRING ? referCell.getStringCellValue() : referCell.getNumericCellValue();
                Object preData = preCell.getCellType() == CellType.STRING ? preCell.getStringCellValue() : preCell.getNumericCellValue();
                if (referData.equals(preData)) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(cell.getRowIndex() - 1, cell.getRowIndex(), cell.getColumnIndex(), cell.getColumnIndex());
                    sheet.addMergedRegionUnsafe(cellRangeAddress);
                }
            }
        }
    }
}
