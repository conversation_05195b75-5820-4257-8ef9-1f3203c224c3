package com.ets.delivery.application.common.consts.yunda;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum YundaProcessExtendPropsCodeEnum {

    NORMAL(0, "正常"),
    ADDRESS_NOT_REACHABLE(1, "地址不送达"),
    BALANCE_NOT_ENOUGH(2, "面单余额不足");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        YundaProcessExtendPropsCodeEnum[] enums = YundaProcessExtendPropsCodeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
