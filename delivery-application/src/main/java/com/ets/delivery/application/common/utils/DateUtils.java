package com.ets.delivery.application.common.utils;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class DateUtils {

    public static Map<String, String> dateTimeMap() {
        Map<String, String> dateMap = new HashMap<>();
        Calendar ca = Calendar.getInstance();
        ca.setTime(new Date());
        ca.add(Calendar.DATE, -1);
        Date s = ca.getTime();
        SimpleDateFormat startFormat = getStartFormat();
        dateMap.put("startTime", startFormat.format(s));
        SimpleDateFormat endFormat = getEndFormat();
        dateMap.put("endTime", endFormat.format(s));
        return dateMap;
    }

    public static SimpleDateFormat getStartFormat() {
        return new SimpleDateFormat("yyyy-MM-dd 00:00:00");
    }

    public static SimpleDateFormat getEndFormat() {
        return new SimpleDateFormat("yyyy-MM-dd 23:59:59");
    }

    public static DateTimeFormatter getTimeFormatter() {
        return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    }
}
