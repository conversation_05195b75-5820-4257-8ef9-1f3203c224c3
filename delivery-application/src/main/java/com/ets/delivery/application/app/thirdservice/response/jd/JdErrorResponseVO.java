package com.ets.delivery.application.app.thirdservice.response.jd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class JdErrorResponseVO {

    @JSONField(name = "error_response")
    private ErrorResponse errorResponse;

    @Data
    public static class ErrorResponse {

        private String code;

        @JSONField(name = "zh_desc")
        private String zhDesc;

        @JSONField(name = "en_desc")
        private String enDesc;
    }
}
