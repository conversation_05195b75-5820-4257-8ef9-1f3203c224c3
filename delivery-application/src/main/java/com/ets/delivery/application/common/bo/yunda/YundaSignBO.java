package com.ets.delivery.application.common.bo.yunda;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class YundaSignBO {
    // method=taobao.qimen.deliveryorder.confirm&timestamp=2024-04-08%2009:00:00&format=xml&app_key=21177927&v=2.0&sign=C5930583FFF8F06F12F28F455F73C80E&sign_method=md5&customerId=YWKH000275
    private String method;
    private String timestamp;
    private String format;
    private String appKey;
    private String v;
    private String sign;
    private String signMethod;
    private String customerId;
    private String body;
}
