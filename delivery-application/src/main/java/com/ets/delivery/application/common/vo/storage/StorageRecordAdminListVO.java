package com.ets.delivery.application.common.vo.storage;

import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.storageRecord.GoodsTypeEnum;
import com.ets.delivery.application.common.consts.storageRecord.StorageRecordStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class StorageRecordAdminListVO {

    private Integer id;
    /**
     * 入库件流水号
     */
    private String recordSn;

    /**
     * 仓储
     */
    private String storageCode;

    private String storageCodeStr;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 0-默认, 1-ETC卡+OBU, 2-ETC单卡, 3-ETC单OBU, 4-ETC设备破损, 5-非高灯设备或非设备, 6-单片式设备
     */
    private Integer goodsType;

    private String goodsTypeStr;

    /**
     * 货物图片
     */
    private String goodsImages;

    /**
     * 寄回数量
     */
    private Integer nums;

    /**
     * 状态：1-待确认 2-匹配成功 3-匹配失败
     */
    private Integer status;

    private String statusStr;

    /**
     * 接收备注
     */
    private String reviceRemark;

    /**
     * 操作人员
     */
    private String operator;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime reviceTime;

    public String getStorageCodeStr() {
        return StorageCodeEnum.map.getOrDefault(storageCode, "-");
    }

    public String getGoodsTypeStr() {
        return GoodsTypeEnum.map.getOrDefault(goodsType, "-");
    }

    public String getStatusStr() {
        return StorageRecordStatusEnum.map.getOrDefault(status, "未知");
    }
}
