package com.ets.delivery.application.infra.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.util.List;

import com.ets.delivery.application.common.bo.stock.StockGoodsNumberBO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 库存商品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_stock_goods_info")
public class StockGoodsInfo extends BaseEntity<StockGoodsInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 1入库，2出库
     */
    private Integer type;

    /**
     * 关联单号
     */
    private String stockSn;

    /**
     * sku编号
     */
    private String skuSn;

    /**
     * 物流公司的商品编号
     */
    private String deliveryGoodsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 申请数量
     */
    private Integer applyCount;

    /**
     * 实际数量
     */
    private Integer realCount;

    private String numberInfo;

    /**
     * 状态
     */
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    public List<StockGoodsNumberBO> getNumberInfoList() {

        if (StringUtils.isEmpty(numberInfo)) {
            return null;
        }

        return JSON.parseArray(numberInfo, StockGoodsNumberBO.class);
    }


}
