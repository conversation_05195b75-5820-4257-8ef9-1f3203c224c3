package com.ets.delivery.application.app.factory.storage;

import com.ets.delivery.application.common.dto.logistics.LogisticsAddOrderDTO;
import com.ets.delivery.application.common.dto.logistics.LogisticsCancelOrderDTO;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderConfirmDTO;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderProcessDTO;
import com.ets.delivery.application.common.vo.InventoryQueryVO;
import com.ets.delivery.application.common.vo.WarehouseStockListVO;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.ExWarehouseDetail;

import java.util.List;

public interface IStorage {

    ExWarehouse initExWarehouse(LogisticsAddOrderDTO addOrderDTO);

    List<ExWarehouseDetail> initExWarehouseDetail(LogisticsAddOrderDTO addOrderDTO);

    String addOrder(ExWarehouse exWarehouse, List<ExWarehouseDetail> detailList);

    void cancelOrder(LogisticsCancelOrderDTO cancelOrderDTO);

    void orderConfirm(LogisticsOrderConfirmDTO confirmDTO);

    void erpOrderConfirm(LogisticsOrderConfirmDTO confirmDTO);

    ExWarehouse orderQuery(ExWarehouse exWarehouse);

    List<InventoryQueryVO> inventoryQuery(List<String> goodsCodeList);

    void orderProcess(LogisticsOrderProcessDTO orderProcessDTO);

    List<WarehouseStockListVO> stockList(String startDate, String endDate);
}
