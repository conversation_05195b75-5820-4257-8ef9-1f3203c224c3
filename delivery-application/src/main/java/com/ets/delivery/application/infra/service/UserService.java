package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.infra.entity.User;
import com.ets.delivery.application.infra.mapper.UserMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
@Service
@DS("db-issuer-admin")
public class UserService extends BaseService<UserMapper, User> {

    public User findByToken(String token) {
        Wrapper<User> wrapper = Wrappers.<User>lambdaQuery()
                .eq(User::getAdminToken, token)
                .eq(User::getStatus, 10)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public User getByUserName(String userName) {

        return getOneByColumn(userName, User::getUsername);
    }

    public User getByMinaToken(String token) {

        return getOneByColumn(token, User::getToken);
    }

    @Override
    public int updateByWrapper(LambdaUpdateWrapper<User> lambdaUpdateWrapper) {
        lambdaUpdateWrapper.set(User::getUpdatedAt, ToolsHelper.getTime());

        return super.updateByWrapper(lambdaUpdateWrapper);
    }

    public void updateMinaToken(Integer uid, String token) {

        LambdaUpdateWrapper<User> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(User::getId, uid)
                .set(User::getToken, token);

        updateByWrapper(wrapper);
    }
}
