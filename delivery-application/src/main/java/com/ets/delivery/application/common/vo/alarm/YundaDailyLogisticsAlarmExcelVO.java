package com.ets.delivery.application.common.vo.alarm;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@HeadRowHeight(20)
@ContentRowHeight(15)
@ColumnWidth(25)
public class YundaDailyLogisticsAlarmExcelVO {

    @ExcelProperty(value = "我司商品编码")
    private String goodsUnionCode;

    @ExcelProperty(value = "韵达仓商品编码")
    private String goodsSku;

    @ExcelProperty(value = "商品名称")
    private String goodsName;

    @ExcelProperty(value = "我司昨日出库量")
    private Integer yesterdayLogisticsNum;

    @ExcelProperty(value = "韵达昨日出库量")
    private Integer yundaYesterdayOutStockNum;

    @ExcelProperty(value = "出库盈亏数")
    private Integer profitAndLoss;
}
