package com.ets.delivery.application.common.vo.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class StockOutListVO {

    /**
     * 出库单号
     */
    private String stockOutSn;

    /**
     * 实际出库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String outTime;

    /**
     * 申请出库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String applyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /*  以下字段需处理********************************/

    /**
     * 库存编号
     */
    private String storageName;

    /**
     * 出库类型
     */
    private String typeStr;

    /**
     * 商品属性
     */
    private String goodsQualityStr;

    /**
     * 出库状态
     */
    private String statusStr;

    private Integer status;

    /**
     * 是否允许取消
     */
    private boolean allowCancel;

    private boolean allowEdit;

    /**
     * 商品信息
     */
    private String goodsNameInfo = "";

    private String goodsApplyCount = "";

    private String goodsRealCount = "";

}
