package com.ets.delivery.application.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Strings;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class Md5SignUtil {

    public static String paramSort(Map<String, Object> paramMap) {
        // 合并集合内容
        return paramMap.entrySet().stream()
                // 过滤掉sign(不会传进来，以防万一就在这里过滤一下) 和 空值
                .filter(Objects::nonNull)
                // 将key进行排序
                .sorted(Map.Entry.comparingByKey())
                // 将key和value使用"="连接
                .map(s -> s.getKey() + "=" + s.getValue())
                // 使用"&"进行分隔
                .collect(Collectors.joining("&"));
    }

    public static String encode(String plaintext) {
        try {
            return DigestUtils.md5Hex(plaintext.getBytes("UTF-8"));
        } catch (UnsupportedEncodingException var2) {
            throw new RuntimeException(var2);
        }
    }

    public static String getSign(String params, String secret) {

        String json = JSON.toJSONString(JSON.parse(params));
        Map<String, Object> map = JSONObject.parseObject(json, new TypeReference<Map<String, Object>>() {
        });
        // 过滤掉空值
        map = map.entrySet().stream().filter(
                s -> {
                    Object value = s.getValue();
                    if (value instanceof String) {
                        String v = String.valueOf(value);
                        return !Strings.isNullOrEmpty(v);
                    }
                    return value != null;
                }
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        String s = paramSort(map).concat("&key=").concat(secret);

        String encode;
        try {
            encode = URLEncoder.encode(s, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("");
        }

        return encode(encode).toUpperCase();
    }

}
