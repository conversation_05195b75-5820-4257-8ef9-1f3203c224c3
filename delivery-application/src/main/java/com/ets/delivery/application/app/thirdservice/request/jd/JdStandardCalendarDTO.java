package com.ets.delivery.application.app.thirdservice.request.jd;

import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class JdStandardCalendarDTO {

    /**
     * 寄件人所属省
     */
    String senderProvince;

    /**
     * 寄件人所属市
     */
    String senderCity;

    /**
     * 寄件人所属区
     */
    String senderDistrict;

    /**
     * 寄件人详细地址
     */
    String senderDetailAddress;

    /**
     * 收件人所属省
     */
    String receiverProvince;

    /**
     * 收件人所属市
     */
    String receiverCity;

    /**
     * 收件人所属区
     */
    String receiverDistrict;

    /**
     * 收件人详细地址
     */
    String receiverDetailAddress;

    /**
     * 物流产品code，特惠送：ed-m-0001；特快送：ed-m-0002
     */
    String productCode = "ed-m-0001";

    /**
     * 预约日历起始时间
     */
    String queryStartDate;

    /**
     * 预约日历天数，默认为3天，最大为4天
     */
    Integer queryDays = 3;
}
