package com.ets.delivery.application.common.vo.storage;

import com.ets.delivery.application.common.consts.storageRecord.StorageRecordLogTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class StorageRecordAdminLogVO {

    private Integer id;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作类型
     */
    private String type;

    private String typeStr;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    public String getTypeStr() {
        return StorageRecordLogTypeEnum.map.getOrDefault(type, "未知");
    }
}
