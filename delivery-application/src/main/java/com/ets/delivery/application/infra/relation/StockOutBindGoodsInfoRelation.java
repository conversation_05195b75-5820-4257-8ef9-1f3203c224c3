package com.ets.delivery.application.infra.relation;


import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.ets.common.base.BaseEntityListRelation;
import com.ets.delivery.application.infra.entity.StockGoodsInfo;
import com.ets.delivery.application.infra.entity.StockOutOrder;

import java.util.List;
import java.util.function.BiConsumer;

public class StockOutBindGoodsInfoRelation extends BaseEntityListRelation<StockOutOrder, StockGoodsInfo> {

    @Override
    public BiConsumer<StockOutOrder, List<StockGoodsInfo>> getEntityListColumn() {
        return StockOutOrder::setGoodsInfo;
    }

    @Override
    public SFunction<StockOutOrder, Object> getMasterColumn() {
        return StockOutOrder::getStockOutSn;
    }

    @Override
    public SFunction<StockGoodsInfo, Object> getAffiliatedColumn() {
        return StockGoodsInfo::getStockSn;
    }
}
