package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 供应商货品列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_supply_goods")
public class SupplyGoods extends BaseEntity<SupplyGoods> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属仓储代号
     */
    private String storageCode;

    /**
     * 供应商代码
     */
    private String supplyCode;

    /**
     * 供应商名称
     */
    private String supplyName;

    /**
     * 商品统一编码
     */
    private String goodsUnionCode;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 货品代码
     */
    private String goodsCode;

    /**
     * 货品类型：主要为card,obu,card_obu
     */
    private String goodsType;

    /**
     * 设备厂商[0-未知 1-埃特斯 2-金溢 3-聚力 4-万集 5-成谷 6-云星宇]
     */
    private Integer manufacturer;

    /**
     * 设备类型[0-普通设备 1-可充电设备 2-单片式设备]
     */
    private Integer deviceType;

    /**
     * 货品库存
     */
    private Integer stock;

    /**
     * 货品单位
     */
    private String unit;

    /**
     * 状态,0不可使用，1可使用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
