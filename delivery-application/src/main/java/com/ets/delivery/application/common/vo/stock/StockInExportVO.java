package com.ets.delivery.application.common.vo.stock;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@HeadRowHeight(20)
@ContentRowHeight(15)
@ColumnWidth(25)
public class StockInExportVO {

    @ExcelProperty("入库单号")
    private String stockInSn;

    /**
     * 实际入库时间
     */
    @ExcelProperty("实际入库时间")
    private String inTime;

    /**
     * 申请入库时间
     */
    @ExcelProperty("申请入库时间")
    private String applyTime;

    /**
     * 仓库
     */
    @ExcelProperty("仓库")
    private String storageName;

    /**
     * 入库类型
     */
    @ExcelProperty("入库类型")
    private String typeStr;

    /**
     * 商品属性
     */
    @ExcelProperty("商品属性")
    private String goodsQualityStr;

    /**
     * 入库状态
     */
    @ExcelProperty("入库状态")
    private String statusStr;

    @ExcelIgnore
    private Integer status;

    /**
     * 商品信息
     */
    @ExcelProperty("商品信息")
    private String goodsNameInfo = "";

    @ExcelProperty("申请入库数量")
    private String goodsApplyCount = "";

    @ExcelProperty("实际入库数量")
    private String goodsRealCount = "";

    /**
     * 操作人
     */
    @ExcelProperty("操作人")
    private String operator;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    @ExcelIgnore
    private Boolean allowCancel;

    @ExcelIgnore
    private Boolean allowEdit;

}
