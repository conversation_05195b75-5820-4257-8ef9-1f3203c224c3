package com.ets.delivery.application.app.thirdservice.response.yunda;

import com.ets.common.ToolsHelper;
import lombok.Data;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Data
@XmlRootElement(name = "response")
public class StockResponseVO implements Serializable {
    String flag = "success";
    String code = "200";
    String message = "成功";

    public StockResponseVO checkError() {

        if (! code.equals("200")) {
            ToolsHelper.throwException(message);
        }

        return this;
    }
}