package com.ets.delivery.application.app.factory.task.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.common.bo.task.TaskExpressSubscribeBO;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeDTO;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.entity.LogisticsSendBack;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.ExWarehouseService;
import com.ets.delivery.application.infra.service.LogisticsSendBackService;
import com.ets.delivery.application.infra.service.LogisticsService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TaskExpressSubscribe extends TaskBase {

    @Autowired
    private ExpressBusiness expressBusiness;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private ExWarehouseService exWarehouseService;

    @Autowired
    private LogisticsSendBackService logisticsSendBackService;

    @Override
    public void childExec(TaskRecord taskRecord) {
        TaskExpressSubscribeBO subscribeBO = JSON.parseObject(taskRecord.getNotifyContent(), TaskExpressSubscribeBO.class);

        if (StringUtils.isEmpty(subscribeBO.getOrderSn()) || StringUtils.isEmpty(subscribeBO.getExpressNumber())) {
            ToolsHelper.throwException("订阅参数缺失，无法订阅", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        if (subscribeBO.getExpressNumber().length() > 30) {
            ToolsHelper.throwException("快递单号错误，无法订阅", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        ExpressSubscribeDTO subscribeDTO = null;
        switch (subscribeBO.getOrderType()) {
            case 1:
                // 发货单
                LambdaQueryWrapper<Logistics> logisticsWrapper = new LambdaQueryWrapper<>();
                logisticsWrapper.eq(Logistics::getLogisticsSn, subscribeBO.getOrderSn());
                logisticsWrapper.eq(Logistics::getExpressNumber, subscribeBO.getExpressNumber());

                Logistics logistics = logisticsService.getOneByWrapper(logisticsWrapper);
                if (logistics == null) {
                    ToolsHelper.throwException("找不到对应发货单");
                }

                // normal仓用快递100
                subscribeDTO = new ExpressSubscribeDTO();
                if (logistics.getStorageCode().equals(StorageCodeEnum.NORMAL.getValue())) {
                    subscribeDTO.setExpressCode(ExpressCodeEnum.KD100.getValue());
                    subscribeDTO.setOrderSn(logistics.getLogisticsSn());
                    subscribeDTO.setExpressNumber(logistics.getExpressNumber());
                    subscribeDTO.setName(logistics.getSendName());
                    subscribeDTO.setAddress(logistics.getSendAddress());
                    subscribeDTO.setMobile(logistics.getSendPhone());
                    subscribeDTO.setProvince("无");
                    subscribeDTO.setCity("无");
                    subscribeDTO.setArea("无");
                    break;
                }

                // 出库单
                LambdaQueryWrapper<ExWarehouse> exWarehouseWrapper = new LambdaQueryWrapper<>();
                exWarehouseWrapper.eq(ExWarehouse::getIsvUuid, logistics.getLogisticsSn());
                exWarehouseWrapper.eq(ExWarehouse::getWayBill, logistics.getExpressNumber());

                ExWarehouse exWarehouse = exWarehouseService.getOneByWrapper(exWarehouseWrapper);
                if (exWarehouse == null) {
                    ToolsHelper.throwException("找不到对应出库单");
                }

                // 组装参数
                subscribeDTO.setOrderSn(logistics.getLogisticsSn());
                subscribeDTO.setExpressNumber(logistics.getExpressNumber());
                subscribeDTO.setName(exWarehouse.getConsigneeName());
                subscribeDTO.setProvince(ObjectUtils.isNotEmpty(exWarehouse.getConsigneeProvince()) ? exWarehouse.getConsigneeProvince() : "无");
                subscribeDTO.setCity(ObjectUtils.isNotEmpty(exWarehouse.getConsigneeCity()) ? exWarehouse.getConsigneeCity() : "无");
                subscribeDTO.setArea(ObjectUtils.isNotEmpty(exWarehouse.getConsigneeArea()) ? exWarehouse.getConsigneeArea() : "无");
                subscribeDTO.setAddress(exWarehouse.getConsigneeAddress());
                subscribeDTO.setMobile(exWarehouse.getConsigneeMobile());
                subscribeDTO.setExpressCode(exWarehouse.getStorageCode());

                if (subscribeDTO.getExpressCode().equals(ExpressCodeEnum.JD_CLOUD.getValue())) {
                    subscribeDTO.setExpressCompany("jd");
                }

                break;
            case 2:
                // 寄回件
                LambdaQueryWrapper<LogisticsSendBack> sendBackWrapper = new LambdaQueryWrapper<>();
                sendBackWrapper.eq(LogisticsSendBack::getSendbackSn, subscribeBO.getOrderSn());
                sendBackWrapper.and(i -> i.eq(LogisticsSendBack::getExpressNumber, subscribeBO.getExpressNumber()).or()
                        .eq(LogisticsSendBack::getOriginExpressNumber, subscribeBO.getExpressNumber()));

                LogisticsSendBack sendBack = logisticsSendBackService.getOneByWrapper(sendBackWrapper);
                if (sendBack == null) {
                    ToolsHelper.throwException("找不到对应寄回件");
                }

                // 组装参数
                subscribeDTO = new ExpressSubscribeDTO();
                subscribeDTO.setExpressCode(ExpressCodeEnum.KD100.getValue());
                subscribeDTO.setOrderSn(sendBack.getSendbackSn());
                subscribeDTO.setExpressNumber(sendBack.getExpressNumber());
                subscribeDTO.setName(sendBack.getReviceName());
                subscribeDTO.setAddress(sendBack.getReviceAddress());
                subscribeDTO.setMobile(sendBack.getRevicePhone());
                subscribeDTO.setProvince("无");
                subscribeDTO.setCity("无");
                subscribeDTO.setArea("无");

                break;
            default:
                ToolsHelper.throwException("订单类型错误，无法订阅", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        try {
            if (ObjectUtils.isNotEmpty(subscribeDTO)) {
                expressBusiness.subscribe(subscribeDTO);
            }
        } catch (Throwable e) {
            if (e.getMessage().contains("快递单号已存在")) {
                ToolsHelper.throwException("任务执行处理无报错", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
            }
            throw e;
        }
    }
}
