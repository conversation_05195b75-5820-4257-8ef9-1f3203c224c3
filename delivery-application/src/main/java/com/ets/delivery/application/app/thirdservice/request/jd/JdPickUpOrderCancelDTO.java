package com.ets.delivery.application.app.thirdservice.request.jd;

import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class JdPickUpOrderCancelDTO {
    /**
     * 取件单号
     */
    String pickupCode;

    /**
     * 终止原因，填写：“客户取消服务单，终止取件”
     */
    String endReasonName = "客户取消服务单，终止取件";

    /**
     * 终止原因code，填写 19
     */
    Integer endReason = 19;

    /**
     * 系统来源，JOS
     */
    String source = "JOS";

    /**
     * 商家编码/青龙业主号/配送编码/月结编码。与京东物流签约后生成
     */
    String customerCode;
}
