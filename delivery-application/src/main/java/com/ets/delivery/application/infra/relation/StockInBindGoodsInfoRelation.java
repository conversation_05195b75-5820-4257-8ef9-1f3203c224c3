package com.ets.delivery.application.infra.relation;


import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.ets.common.base.BaseEntityListRelation;
import com.ets.delivery.application.infra.entity.StockGoodsInfo;
import com.ets.delivery.application.infra.entity.StockInOrder;

import java.util.List;
import java.util.function.BiConsumer;

public class StockInBindGoodsInfoRelation extends BaseEntityListRelation<StockInOrder, StockGoodsInfo> {

    @Override
    public BiConsumer<StockInOrder, List<StockGoodsInfo>> getEntityListColumn() {
        return StockInOrder::setGoodsInfo;
    }

    @Override
    public SFunction<StockInOrder, Object> getMasterColumn() {
        return StockInOrder::getStockInSn;
    }

    @Override
    public SFunction<StockGoodsInfo, Object> getAffiliatedColumn() {
        return StockGoodsInfo::getStockSn;
    }
}
