package com.ets.delivery.application.common.vo.aftersalesreivews;

import com.ets.delivery.application.common.consts.aftersalesreviews.AftersalesReviewsOrderTypeEnum;
import com.ets.delivery.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AdminAfterSalesReviewsListVO {

    private Integer id;

    /**
     * 审核单号
     */
    private String reviewSn;

    /**
     * 业务单号
     */
    private String orderSn;

    /**
     * 业务类型
     */
    private String orderType;

    private String orderTypeStr;

    /**
     * 发卡方id
     */
    private Integer issuerId;

    private String issuerName;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applyTime;

    /**
     * 审核状态[0-待审核 1-审核通过 2-审核驳回 3-已取消]
     */
    private Integer reviewStatus;

    private String reviewStatusStr;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime reviewTime;

    /**
     * 是否推送自动审核[0-人工审核 1-自动审核]
     */
    private Integer autoAudit;

    /**
     * 操作人
     */
    private String operator;

    public String getOrderTypeStr() {
        return AftersalesReviewsOrderTypeEnum.map.getOrDefault(orderType, "-");
    }

    public String getReviewStatusStr() {
        return AftersalesReviewsStatusEnum.map.getOrDefault(reviewStatus, "-");
    }
}
