package com.ets.delivery.application.common.vo.postReviews;

import com.ets.delivery.application.common.consts.postReviews.PostReviewExceptionStatusEnum;
import com.ets.delivery.application.common.vo.ReviewListVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PostReviewAbnormalListVO extends ReviewListVO {

    private Integer postReviewId;

    private Integer exceptionStatus;

    private String exceptionStatusStr;

    private String postReviewRemark;

    public String getExceptionStatusStr() {
        return PostReviewExceptionStatusEnum.map.getOrDefault(this.exceptionStatus, "未知");
    }
}
