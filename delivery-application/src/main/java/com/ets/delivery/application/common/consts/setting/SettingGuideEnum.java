package com.ets.delivery.application.common.consts.setting;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.LinkedHashMap;

@Getter
@AllArgsConstructor
public enum SettingGuideEnum {

    GUIDE_DEFAULT("default", "默认"),
    GUIDE_RE_LOGIN_WANGLU("re-login-wanglu", "网路智联-重新登录"),
    GUIDE_RESET_AUTH_WAGNLU("reset-auth-wanglu", "网路智联-重置授权"),
    GUIDE_AUTH_WANGLU("auth-wanglu", "网路智联-授权"),
    GUIDE_PLATE_UNIQUE("plate-unique", "车牌校验"),
    GUIDE_NEED_RECALL("need-recall", "需要召回");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;

    static {
        SettingGuideEnum[] enums = SettingGuideEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Arrays.stream(enums).map(SettingGuideEnum::getValue).collect(Collectors.toList());
    }
}
