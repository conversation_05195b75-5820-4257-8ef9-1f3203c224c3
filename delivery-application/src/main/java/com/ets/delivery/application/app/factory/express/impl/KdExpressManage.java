package com.ets.delivery.application.app.factory.express.impl;

import cn.hutool.core.convert.ConverterRegistry;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.thirdservice.business.KdBusiness;
import com.ets.delivery.application.app.thirdservice.response.kd.KdExpressData;
import com.ets.delivery.application.app.thirdservice.response.kd.KdExpressQueryVO;
import com.ets.delivery.application.common.bo.express.ExpressBO;
import com.ets.delivery.application.common.bo.express.ExpressLogBO;
import com.ets.delivery.application.common.consts.ErrCodeConstant;
import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsExpressStatusEnum;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeDTO;
import com.ets.delivery.application.app.thirdservice.request.kd.KdExpressNotifyDTO;
import com.ets.delivery.application.app.thirdservice.response.kd.KdAutoNumberVO;
import com.ets.delivery.application.app.thirdservice.response.kd.KdSubscribeVO;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.Express;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.service.ExWarehouseService;
import com.ets.delivery.application.infra.service.ExpressLogService;
import com.ets.delivery.application.infra.service.ExpressService;
import com.ets.delivery.application.infra.service.LogisticsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class KdExpressManage extends ExpressBase {

    @Autowired
    private ExWarehouseService exWarehouseService;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private ExpressLogService expressLogService;

    @Autowired
    private KdBusiness kdBusiness;

    @Autowired
    private LogisticsService logisticsService;

    @Override
    public void subscribe(ExpressSubscribeDTO subscribeDTO) {
        // 智能单号识别
        if (ObjectUtil.isEmpty(subscribeDTO.getExpressCompany())) {
            List<KdAutoNumberVO> autoNumberVOList = kdBusiness.autoNumber(subscribeDTO.getExpressNumber());
            if (ObjectUtil.isEmpty(autoNumberVOList)) {
                log.error("【快递100】【物流轨迹订阅失败】快递单号：{} 智能单号识别失败", subscribeDTO.getExpressNumber());
                ToolsHelper.throwException("智能单号识别快递公司失败", ErrCodeConstant.CODE_EXPRESS_NUMBER_WRONG.getCode());
            }
            subscribeDTO.setExpressCompany(autoNumberVOList.get(0).getComCode());
            // 更新物流记录
            Express express = expressService.getOneByExpressNumber(subscribeDTO.getExpressNumber());
            express.setExpressCompany(subscribeDTO.getExpressCompany());
            expressService.updateById(express);
        }

        ExpressBO expressBO = new ExpressBO();
        expressBO.setExpressNumber(subscribeDTO.getExpressNumber());
        expressBO.setExpressCompany(subscribeDTO.getExpressCompany());
        expressBO.setPhone(subscribeDTO.getMobile());

        KdSubscribeVO subscribeVO = kdBusiness.subscribe(expressBO);
        if (ObjectUtil.isNull(subscribeVO)) {
            log.error("【快递100】【物流轨迹订阅失败】快递单号：{}", subscribeDTO.getExpressNumber());
            ToolsHelper.throwException("订阅失败，返回空");
        }

        if (!subscribeVO.getResult()) {
            // 501代表重复订阅
            if (!subscribeVO.getReturnCode().equals("501")) {
                log.error("【快递100】【物流轨迹订阅失败】快递单号：{} 物流订阅失败：{}", subscribeDTO.getExpressNumber(), subscribeVO.getMessage());
                ToolsHelper.throwException(subscribeVO.getMessage());
            }
        }
    }

    @Override
    public Express expressNotify(ExpressNotifyDTO notifyDTO) {
        KdExpressNotifyDTO kdExpressNotifyDTO = ConverterRegistry.getInstance().convert(KdExpressNotifyDTO.class, notifyDTO.getNotifyData());
        KdExpressNotifyDTO.ResultParam.LastResult lastResult = kdExpressNotifyDTO.getParam().getLastResult();
        List<KdExpressData> dataList = lastResult.getData();
        String expressNumber = lastResult.getNu();
        if (ObjectUtil.isEmpty(dataList)) {
            return null;
        }

        // 更新出库记录
        updateExWarehouse(expressNumber, lastResult.getState());

        // 更新物流轨迹
        return updateExpress(kdExpressNotifyDTO);
    }

    @Override
    public Express expressQuery(Express express) {
        if (StringUtils.isEmpty(express.getExpressCompany())) {
            List<KdAutoNumberVO> autoNumberVOList = kdBusiness.autoNumber(express.getExpressNumber());
            if (ObjectUtil.isEmpty(autoNumberVOList)) {
                log.error("【快递100】【物流轨迹查询】快递单号：{} 智能单号识别失败", express.getExpressNumber());
                return express;
            }
            express.setExpressCompany(autoNumberVOList.get(0).getComCode());
        }

        ExpressBO expressBO = new ExpressBO();
        expressBO.setExpressNumber(express.getExpressNumber());
        expressBO.setExpressCompany(express.getExpressCompany());
        expressBO.setPhone(express.getPhone());
        KdExpressQueryVO kdExpressQueryVO = kdBusiness.expressQuery(expressBO);
        if (kdExpressQueryVO.getResult() != null && !kdExpressQueryVO.getResult()) {
            log.error("【快递100】【物流轨迹查询】快递单号：{} 查询失败：{}", express.getExpressNumber(), kdExpressQueryVO.getMessage());
            return express;
        }

        // 更新发货单
        updateLogistics(express.getExpressNumber(), Integer.valueOf(kdExpressQueryVO.getState()));

        // 更新出库单
        updateExWarehouse(express.getExpressNumber(), Integer.valueOf(kdExpressQueryVO.getState()));

        // 更新物流轨迹
        return updateExpress(express, kdExpressQueryVO);
    }

    private void updateLogistics(String expressNumber, Integer state) {
        Logistics logistics = logisticsService.getByExpressNumber(expressNumber);
        if (ObjectUtils.isEmpty(logistics)) {
            return;
        }

        // 已取消
        if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue())) {
            return;
        }

        // 已签收
        if (logistics.getExpressStatus().equals(LogisticsExpressStatusEnum.SIGNED.getValue())) {
            return;
        }

        // 更新为已签收
        if (ExpressStateEnum.RECEIVED.getValue().equals(state)) {
            logistics.setExpressStatus(LogisticsExpressStatusEnum.SIGNED.getValue());
            logisticsService.updateById(logistics);
        }
    }

    private void updateExWarehouse(String expressNumber, Integer state) {
        ExWarehouse exWarehouse = exWarehouseService.getOneByWayBill(expressNumber);
        if (ObjectUtil.isNotNull(exWarehouse) &&
                !exWarehouse.getCurrentStatus().equals(10034) &&
                !exWarehouse.getCurrentStatus().equals(10035)) {
            exWarehouse.setUpdatedAt(LocalDateTime.now());
            if (ExpressStateEnum.RECEIVED.getValue().equals(state)) {
                // 已签收
                exWarehouse.setCurrentStatus(10034);
            } else if (ExpressStateEnum.RECEIVER_REJECT.getValue().equals(state)) {
                // 拒收
                exWarehouse.setCurrentStatus(10035);
            } else if (ExpressStateEnum.REJECTED.getValue().equals(state)) {
                // 退签
                exWarehouse.setCurrentStatus(10035);
            }
            exWarehouseService.updateById(exWarehouse);
        }
    }

    // 回调更新
    private Express updateExpress(KdExpressNotifyDTO kdExpressNotifyDTO) {
        Express express = expressService.getOneByExpressNumber(kdExpressNotifyDTO.getParam().getLastResult().getNu());
        if (ObjectUtil.isNotNull(express)) {
            KdExpressNotifyDTO.ResultParam.LastResult lastResult = kdExpressNotifyDTO.getParam().getLastResult();

            express.setSubscribeStatus(
                    ExpressSubscribeStatusEnum.statusValueMap.getOrDefault(
                            kdExpressNotifyDTO.getParam().getStatus(),
                            ExpressSubscribeStatusEnum.SUBSCRIBE_ABNORMAL.getValue()));

            if (ObjectUtil.isNotNull(kdExpressNotifyDTO.getParam().getAutoCheck()) &&
                    kdExpressNotifyDTO.getParam().getAutoCheck().equals(1)) {
                express.setExpressCompany(kdExpressNotifyDTO.getParam().getComNew());
            }
            express = handleExpress(express, lastResult.getState(), lastResult.getData());
        }
        return express;
    }

    // 查询更新
    private Express updateExpress(Express express, KdExpressQueryVO kdExpressQueryVO) {
        if (ObjectUtils.isNotEmpty(express)) {
            // 已签收 设置订阅结束
            if (ExpressStateEnum.RECEIVED.getValue().equals(Integer.valueOf(kdExpressQueryVO.getState()))) {
                express.setSubscribeStatus(ExpressSubscribeStatusEnum.SUBSCRIBE_SHUTDOWN.getValue());
            }
            express = handleExpress(express, Integer.valueOf(kdExpressQueryVO.getState()), kdExpressQueryVO.getData());
        }
        return express;
    }

    private Express handleExpress(Express express, Integer state, List<KdExpressData> dataList) {
        KdExpressData firstData = dataList.get(dataList.size() - 1);
        KdExpressData lastData = dataList.get(0);
        String data = JSON.toJSONString(dataList);

        express.setData(data);
        express.setState(state);

        // 最早状态
        if (StringUtils.isEmpty(express.getFirstContext())) {
            express.setFirstArea(firstData.getAreaName());
            express.setFirstStatus(firstData.getStatus());
            express.setFirstContext(firstData.getContext());
            express.setFirstExpressTime(firstData.getFtime());
        }

        // 最新状态
        express.setLastArea(lastData.getAreaName());
        express.setLastStatus(lastData.getStatus());
        express.setLastContext(lastData.getContext());
        express.setLastExpressTime(lastData.getFtime());

        // 签收时间
        if (ExpressStateEnum.RECEIVED.getValue().equals(state)) {
            express.setReceivedTime(lastData.getTime());
        }

        // 退回标记
        if (Arrays.asList(ExpressStateEnum.SEND_BACK.getValue(), ExpressStateEnum.RECEIVER_REJECT.getValue()).contains(state)) {
            express.setIsBack(1);
            express.setBackTime(lastData.getTime());
        }

        express.setUpdatedAt(LocalDateTime.now());
        expressService.updateById(express);

        // 记录日志
        ExpressLogBO logBO = new ExpressLogBO();
        logBO.setExpressSn(express.getExpressSn());
        logBO.setExpressNumber(express.getExpressNumber());
        logBO.setSubscribeStatus(express.getSubscribeStatus());
        logBO.setContent("物流轨迹修改:" + JSON.toJSONString(express));
        expressLogService.addLog(logBO);

        return express;
    }
}
