package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.KdFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(url = "${kd100.apiCompany}", name = "KdFeign", fallbackFactory = KdFallbackFactory.class)
public interface KdFeign {

    @PostMapping(
            value = "/autonumber/auto",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE
    )
    String autoNumber(@RequestParam String num, @RequestParam String key);
}
