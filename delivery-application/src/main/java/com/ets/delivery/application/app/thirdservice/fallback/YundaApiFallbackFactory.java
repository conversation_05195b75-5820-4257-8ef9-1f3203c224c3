package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.YundaApiFeign;
import org.springframework.cloud.openfeign.FallbackFactory;

import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

@Component
public class YundaApiFallbackFactory implements FallbackFactory<YundaApiFeign> {
    @Override
    public YundaApiFeign create(Throwable throwable) {
        return new YundaApiFeign() {
            @Override
            public String DeliveryOrder(String xml,
                                        String appKey,
                                        String customerId,
                                        String format,
                                        String method,
                                        String signMethod,
                                        String timestamp,
                                        String version,
                                        String sign,
                                        Integer channel) {
                return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                        "<response>\n" +
                        "    <flag>failure</flag>\n" +
                        "    <code>-1</code>\n" +
                        "    <message>请求YundaApi接口失败: " + throwable.getMessage() + "</message>\n" +
                        "</response>";
            }

            @Override
            public String StockList(MultiValueMap<String, Object> body) {
                return JsonResult.error("请求韵达stock-list接口失败：" + throwable.getLocalizedMessage()).toString();
            }


        };
    }
}
