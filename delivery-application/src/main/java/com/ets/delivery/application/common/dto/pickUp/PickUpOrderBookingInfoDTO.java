package com.ets.delivery.application.common.dto.pickUp;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;

@Data
public class PickUpOrderBookingInfoDTO {
    @NotEmpty(message = "寄件人地区不能为空")
    private String sendArea;

    @NotEmpty(message = "寄件人地址不能为空")
    private String sendAddress;

    @NotEmpty(message = "收件人地区不能为空")
    private String receiveArea;

    @NotEmpty(message = "收件人地址不能为空")
    private String receiveAddress;
}
