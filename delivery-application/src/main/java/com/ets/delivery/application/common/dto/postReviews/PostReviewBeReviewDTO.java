package com.ets.delivery.application.common.dto.postReviews;

import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

@Data
public class PostReviewBeReviewDTO {

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;

    /**
     * 搜索日期天数范围 最多60天
     */
    private Integer dayRange = 60;

    /**
     * 检查日期范围
     * @throws BizException 日期错误
     */
    public void checkDate() throws BizException {
        if (this.endDate.isBefore(this.startDate)) {
            ToolsHelper.throwException("结束日期不能比开始日期早");
        }

        if (this.startDate.until(this.endDate, ChronoUnit.DAYS) > this.dayRange) {
            ToolsHelper.throwException("日期范围不能超过60天");
        }
    }
}
