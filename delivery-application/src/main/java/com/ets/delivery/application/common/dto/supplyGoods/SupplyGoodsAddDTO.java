package com.ets.delivery.application.common.dto.supplyGoods;

import com.ets.delivery.application.common.consts.supplyGoods.GoodsDeviceTypeEnum;
import com.ets.delivery.application.common.consts.supplyGoods.GoodsManufacturerEnum;
import com.ets.delivery.application.common.consts.supplyGoods.GoodsTypeEnum;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class SupplyGoodsAddDTO {

    /**
     * 所属仓储代号
     */
    @NotBlank(message = "仓储编号不能为空")
    private String storageCode;

    /**
     * 供应商代码
     */
    @NotBlank(message = "供应商代码不能为空")
    private String supplyCode;

    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空")
    private String supplyName;

    /**
     * 货品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String goodsName;

    /**
     * 货品代码
     */
    @NotBlank(message = "商品编码不能为空")
    private String goodsCode;

    /**
     * 货品类型：主要为card,obu,card_obu
     */
    @NotBlank(message = "商品类型不能为空")
    private String goodsType;

    /**
     * 设备厂商[0-未知 1-埃特斯 2-金溢 3-聚力 4-万集 5-成谷 6-云星宇]
     */
    @NotNull(message = "设备厂商不能为空")
    private Integer manufacturer;

    /**
     * 设备类型[0-普通设备 1-可充电设备 2-单片式设备]
     */
    @NotNull(message = "设备类型不能为空")
    private Integer deviceType;

    /**
     * 货品库存
     */
    private Integer stock;

    /**
     * 货品单位
     */
    private String unit;

    /**
     * 备注
     */
    private String remark;

    public boolean checkGoodsType() {
        return GoodsTypeEnum.list.contains(this.goodsType);
    }

    public boolean checkManufacturer() {
        return GoodsManufacturerEnum.list.contains(this.manufacturer);
    }

    public boolean checkDeviceType() {
        return GoodsDeviceTypeEnum.list.contains(this.deviceType);
    }
}
