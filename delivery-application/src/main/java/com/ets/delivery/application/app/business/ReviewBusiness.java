package com.ets.delivery.application.app.business;

import com.alibaba.fastjson2.JSON;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.dto.reviews.RiskResultNotifyDTO;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import org.springframework.stereotype.Component;

@Component
public class ReviewBusiness {

    public void riskResultNotify(RiskResultNotifyDTO notifyDTO) {

        TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
        taskRecordDTO.setReferSn(notifyDTO.getRiskSn());
        taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_RISK_RESULT_NOTIFY.getType());
        taskRecordDTO.setNotifyContent(JSON.toJSONString(notifyDTO));
        TaskFactory.create(TaskRecordReferTypeEnum.TASK_RISK_RESULT_NOTIFY).addAndPush(taskRecordDTO);
    }
}
