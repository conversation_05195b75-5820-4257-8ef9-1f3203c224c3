package com.ets.delivery.application.common.vo.storageMap;

import com.ets.delivery.application.common.consts.storageMap.StorageMapAddressStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class StorageMapAddressListVO {

    private Integer id;
    /**
     * 配置名称
     */
    private String configName;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 状态[-1-删除 1-生效 2-未生效]
     */
    private Integer status;

    private String statusStr;

    /**
     * 是否默认规则
     */
    private Integer isDefault;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    public String getStatusStr() {
        return StorageMapAddressStatusEnum.map.getOrDefault(this.status, "未知");
    }
}
