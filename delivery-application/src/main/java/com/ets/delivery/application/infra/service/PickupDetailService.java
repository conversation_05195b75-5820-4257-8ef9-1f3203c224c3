package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.common.consts.pickUp.PickUpDetailStatusEnum;
import com.ets.delivery.application.infra.entity.PickupDetail;
import com.ets.delivery.application.infra.mapper.PickupDetailMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 上门取件订单商品详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
@Service
@DS("db-issuer-admin")
public class PickupDetailService extends BaseService<PickupDetailMapper, PickupDetail> {

    public PickupDetail getOneByPickUpSnAndGoodsCode(String pickUpSn, String goodsCode) {
        Wrapper<PickupDetail> wrapper = Wrappers.<PickupDetail>lambdaQuery()
                .eq(PickupDetail::getPickupSn, pickUpSn)
                .eq(PickupDetail::getGoodsCode, goodsCode)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public List<PickupDetail> getListByPickUpSn(String pickUpSn) {
        Wrapper<PickupDetail> wrapper = Wrappers.<PickupDetail>lambdaQuery()
                .eq(PickupDetail::getPickupSn, pickUpSn)
                .eq(PickupDetail::getStatus, PickUpDetailStatusEnum.STATUS_NORMAL.getValue());
        return this.baseMapper.selectList(wrapper);
    }

    public void updateStatusByPickUpSn(String pickupSn, Integer status) {
        Wrapper<PickupDetail> wrapper = Wrappers.<PickupDetail>lambdaUpdate()
                .eq(PickupDetail::getPickupSn, pickupSn)
                .set(PickupDetail::getStatus, status);
        this.baseMapper.update(null, wrapper);
    }
}
