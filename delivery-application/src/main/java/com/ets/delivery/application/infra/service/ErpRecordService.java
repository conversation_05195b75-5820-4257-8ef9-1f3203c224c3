package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.ErpRecord;
import com.ets.delivery.application.infra.mapper.ErpRecordMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * erp记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Service
@DS("db-issuer-admin")
public class ErpRecordService extends BaseService<ErpRecordMapper, ErpRecord> {

    public List<ErpRecord> getByDeliveryTimeRange(String orderSource, LocalDateTime startTime, LocalDateTime endTime) {
        Wrapper<ErpRecord> wrapper = Wrappers.<ErpRecord>lambdaQuery()
                .eq(ErpRecord::getErpOrderSource, orderSource)
                .ge(ErpRecord::getDeliveryTime, startTime)
                .le(ErpRecord::getDeliveryTime, endTime);
        return this.baseMapper.selectList(wrapper);
    }

    public ErpRecord getExists(String erpSn, String orderSource) {

        LambdaQueryWrapper<ErpRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpRecord::getErpSn, erpSn)
                .eq(ErpRecord::getErpOrderSource, orderSource);

        return getOneByWrapper(wrapper);
    }
}
