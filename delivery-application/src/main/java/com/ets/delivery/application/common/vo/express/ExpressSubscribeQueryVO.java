package com.ets.delivery.application.common.vo.express;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ExpressSubscribeQueryVO {

    private String number;
    private String corp;
    private Integer signed;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime receivedTime;

    private List<TraceData> list;

    @Data
    public static class TraceData {
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime time;
        private String status;
        private String tag;
    }
}
