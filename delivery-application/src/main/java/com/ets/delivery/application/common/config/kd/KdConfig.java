package com.ets.delivery.application.common.config.kd;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "kd100")
public class KdConfig {
    String appKey;
    String customer;
    String schema;
    String resultv2;
    String callbackUrl;
    String apiHost;
    String apiCompany;
}
