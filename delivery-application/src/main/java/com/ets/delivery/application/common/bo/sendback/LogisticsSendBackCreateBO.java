package com.ets.delivery.application.common.bo.sendback;

import lombok.Data;


@Data
public class LogisticsSendBackCreateBO {

    private Integer issuerId = 0;

    private String issuerName = "";

    /**
     * 订单流水号
     */
    private String orderSn;

    /**
     * app_id
     */
    private String appId = "10020";

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 所属仓储
     */
    private String storageCode;

    /**
     * 货品编号
     */
    private String goodsCode;

    /**
     * 寄回数量
     */
    private Integer nums;

    /**
     * [{"skuSn":"sku编号","count":数量,"skuName":"sku名称"}]
     */
    private String goodsSkuInfo;

    /**
     * 发件人
     */
    private String sendName;

    /**
     * 发件人联系手机
     */
    private String sendPhone;

    /**
     * 发件地区
     */
    private String sendArea;

    /**
     * 发件地址
     */
    private String sendAddress;

    /**
     * 接收人
     */
    private String reviceName;

    /**
     * 接收人联系手机
     */
    private String revicePhone;

    /**
     * 接收地区
     */
    private String reviceArea;

    /**
     * 接收地址
     */
    private String reviceAddress;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司
     */
    private String expressCorp;

    /**
     * 回调通知路径
     */
    private String notifyBackUrl;
}
