package com.ets.delivery.application.app.business.stock;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.ets.common.BeanHelper;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ListUtils;
import com.ets.common.util.NumberUtil;
import com.ets.delivery.application.app.business.CosBusiness;
import com.ets.delivery.application.common.bo.stock.StockGoodsNumberBO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.consts.stock.*;
import com.ets.delivery.application.common.consts.supplyGoods.GoodsManufacturerEnum;
import com.ets.delivery.application.common.dto.stock.StockGoodsInfoDTO;
import com.ets.delivery.application.common.dto.stock.StockGoodsNumberDTO;
import com.ets.delivery.application.common.vo.SelectOptionsVO;
import com.ets.delivery.application.common.vo.stock.*;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigInteger;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
public class AdminStockBusiness {

    @Autowired
    private CosBusiness cosBusiness;

    @Autowired
    private StockGoodsInfoService stockGoodsInfoService;

    @Autowired
    private SupplyGoodsService supplyGoodsService;

    @Autowired
    private StockLogService stockLogService;

    @Autowired
    private StorageSkuMapService storageSkuMapService;

    @Autowired
    private StorageService storageService;

    @Autowired
    private DeliveryConfig deliveryConfig;

    public List<SelectOptionsVO> getIssuerSelectOptions() {

        List<SelectOptionsVO> list = new ArrayList<>();
        deliveryConfig.getIssuerMap().forEach((issuerId, issuerMapDTO) -> {
            SelectOptionsVO vo = new SelectOptionsVO(String.valueOf(issuerId), issuerMapDTO.getCnName());
            list.add(vo);
        });

        return list;
    }

    /*
     *  获取库存管理下拉选择项
     */
    public Map<String, List<SelectOptionsVO>> getSelectOptions() {

        Map<String,List<SelectOptionsVO>> map = new HashMap<>();

        map.put("inType", StockInTypeEnum.getSelectOptions());
        map.put("outType", StockOutTypeEnum.getSelectOptions());
        map.put("goodsQuality", StockGoodsQualityEnum.getSelectOptions());
        map.put("inStatus", StockInStatusEnum.getSelectOptions());
        map.put("outStatus", StockOutStatusEnum.getSelectOptions());

        // 仓库
        map.put("storage", storageService.getSelectOptions());

        // 商品
        map.put("goods", supplyGoodsService.getOptionList());

        // 省份
        map.put("issuer", getIssuerSelectOptions());

        map.put("product", StockProductEnum.getSelectOptions());

        map.put("carType", StockCarTypeEnum.getSelectOptions());

        map.put("manufacturer", GoodsManufacturerEnum.getSelectOptions());

        map.put("deliveryType", StockDeliveryTypeEnum.getSelectOptions());

        return map;
    }

    public StockUploadVO uploadImg(MultipartFile file) {

        StockUploadVO vo = new StockUploadVO();

        try {
            // 文件名
            String filename = "/stock/" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "/" + SecureUtil.md5(UUID.randomUUID().toString());

            // 上传cos
            String url = cosBusiness.upload(file.getBytes(), filename);
            vo.setUrl(url);
            vo.setSignedUrl(cosBusiness.getAccessUrl(url));

        } catch (Exception e) {
            ToolsHelper.throwException("上传失败：" + e.getMessage());
        }

        return vo;
    }

    public void createGoodsInfo(StockGoodsInfoDTO dto, StockTypeEnum stockType, String stockSn, String storageCode) {

        StockGoodsInfo goodsInfo = new StockGoodsInfo();
        goodsInfo.setApplyCount(dto.getApplyCount());
        goodsInfo.setDeliveryGoodsCode(dto.getStorageSku());
        goodsInfo.setType(stockType.getCode());
        goodsInfo.setStockSn(stockSn);
        goodsInfo.setSkuSn(dto.getSkuSn());

        /*
        StorageSkuMap storageSkuMap = storageSkuMapService.getByStorageSkuAndSku(dto.getStorageSku(), dto.getSkuSn());
        if (storageSkuMap == null) {
            ToolsHelper.throwException("找不到仓库商品配置");
        }
        if (! storageCode.equals(storageSkuMap.getStorageCode())) {
            ToolsHelper.throwException("仓库和商品不匹配");
        }*/

        SupplyGoods supplyGoods = supplyGoodsService.getOneByGoodsCode(dto.getStorageSku());
        if (supplyGoods == null) {
            ToolsHelper.throwException("商品不存在或已下架");
        }
        // sku和商品名称设置
        goodsInfo.setGoodsName(supplyGoods.getGoodsName());

        goodsInfo.setNumberInfo(getNumberInfoJson(dto.getNumberInfoList()));

        stockGoodsInfoService.create(goodsInfo);
    }

    public String getNumberInfoJson(List<StockGoodsNumberDTO> numberInfoList) {

        if (numberInfoList == null) {
            return null;
        }

        List<StockGoodsNumberBO> numberBOList = new ArrayList<>();
        numberInfoList.forEach(numberInfo -> {

            StockGoodsNumberBO numberBO = BeanHelper.copy(StockGoodsNumberBO.class, numberInfo);

            BigInteger endNumber = (new BigInteger(numberInfo.getBeginNumber())).add(BigInteger.valueOf(numberInfo.getCount())).subtract(BigInteger.ONE);

            numberBO.setEndNumber(String.valueOf(endNumber));

            numberBOList.add(numberBO);
        });

        return JSON.toJSONString(numberBOList);
    }

    public void updateNumber(Integer id, List<StockGoodsNumberDTO> numberInfoList) {

        stockGoodsInfoService.updateNumber(id, getNumberInfoJson(numberInfoList));
    }

    public List<StockGoodsInfo> getGoodsInfoList(String stockSn, StockTypeEnum stockType) {

        return stockGoodsInfoService.getList(stockSn, stockType.getCode());
    }

    public List<StockGoodsInfoVO> getGoodsInfoVOList(String stockSn, StockTypeEnum stockType, StockGoodsUnionVO unionVO) {

        List<StockGoodsInfo> list = stockGoodsInfoService.getList(stockSn, stockType.getCode());

        if (list == null || list.isEmpty()) {
            return null;
        }

        List<StockGoodsInfoVO> voList = new ArrayList<>();

        Integer realCount = 0;
        for (StockGoodsInfo stockGoodsInfo : list) {
            StockGoodsInfoVO vo = BeanHelper.copy(StockGoodsInfoVO.class, stockGoodsInfo);
            vo.setCardNumberList(getNumberListByProduct(stockGoodsInfo.getNumberInfoList(), Arrays.asList(2,3)));
            vo.setObuNumberList(getNumberListByProduct(stockGoodsInfo.getNumberInfoList(), Arrays.asList(1)));
            vo.setRealCount((NumberUtil.isPositive(stockGoodsInfo.getRealCount()) ? String.valueOf(stockGoodsInfo.getRealCount()) : "-"));
            voList.add(vo);

            unionVO.setSkuCount(unionVO.getSkuCount() + 1);
            unionVO.setTotalApplyCount(unionVO.getTotalApplyCount() + stockGoodsInfo.getApplyCount());

            realCount = realCount + (NumberUtil.isPositive(stockGoodsInfo.getRealCount()) ? stockGoodsInfo.getRealCount() : 0);

        }

        unionVO.setTotalRealCount(realCount > 0 ? String.valueOf(realCount) : "-");

        return voList;
    }

    public List<String> getNumberListByProduct(List<StockGoodsNumberBO> list, List<Integer> product) {

        if (list == null) {
            return null;
        }

        List<String> result = new ArrayList<>();
        list.forEach(numberBO -> {
            if (product.contains(numberBO.getProduct())) {
                result.add(numberBO.getBeginNumber() + "-" + numberBO.getEndNumber());
            }
        });

        return result;
    }

    public StockGoodsUnionInfoVO getGoodsUnionInfo(List<StockGoodsInfo> list) {

        StockGoodsUnionInfoVO vo = new StockGoodsUnionInfoVO();

        if (list == null || list.isEmpty()) {
            return vo;
        }

        List<String> goodsNameList = new ArrayList<>();
        List<String> applyCountList = new ArrayList<>();
        List<String> realCountList = new ArrayList<>();
        for (StockGoodsInfo stockGoodsInfo : list) {
            goodsNameList.add(stockGoodsInfo.getGoodsName());
            applyCountList.add(String.valueOf(stockGoodsInfo.getApplyCount()));
            String realCount = NumberUtil.isPositive(stockGoodsInfo.getRealCount()) ? String.valueOf(stockGoodsInfo.getRealCount()) : "-";
            realCountList.add(realCount);
        }

        vo.setGoodsNameInfo(String.join(",", goodsNameList));
        vo.setGoodsApplyCount(String.join(",", applyCountList));
        vo.setGoodsRealCount(String.join(",", realCountList));

        return vo;
    }

    public List<String> getStockSnListByGoods(String deliveryGoodsCode, StockTypeEnum stockType) {

        List<StockGoodsInfo> list = stockGoodsInfoService.getStockSnListByGoods(deliveryGoodsCode, stockType.getCode(), 100);

        return ListUtils.getColumnList(list, StockGoodsInfo::getStockSn);
    }

    public List<SearchSupplyGoodsVO> getSupplyGoods(String goodsName, String storageCode) {
        // 根据商品名称搜索

        List<SupplyGoods> list = supplyGoodsService.search(goodsName, storageCode);
        if (list == null) {
            return null;
        }

        List<SearchSupplyGoodsVO> voList = new ArrayList<>();
        HashMap<String, String> exists = new HashMap<>();

        for (SupplyGoods supplyGoods: list) {

            if (exists.containsKey(supplyGoods.getGoodsCode())) {
                continue;
            }
            exists.put(supplyGoods.getGoodsCode(), "1");

            SearchSupplyGoodsVO vo = new SearchSupplyGoodsVO();
            vo.setGoodsName(supplyGoods.getGoodsName());
            vo.setSkuSn(supplyGoods.getGoodsUnionCode());
            vo.setStorageSku(supplyGoods.getGoodsCode());

            voList.add(vo);
        }

        return voList;
    }

    public List<SearchSupplyGoodsVO> getStorageGoods(String goodsName, String storageCode) {
        // 根据商品名称搜索

        List<StorageSkuMap> list = storageSkuMapService.search(goodsName, storageCode);
        if (list == null) {
            return null;
        }

        List<SearchSupplyGoodsVO> voList = new ArrayList<>();

        list.forEach(storageSkuMap -> {
            SearchSupplyGoodsVO vo = new SearchSupplyGoodsVO();
            vo.setGoodsName(storageSkuMap.getGoodsName());
            vo.setSkuSn(storageSkuMap.getSku());
            vo.setStorageSku(storageSkuMap.getStorageSku());
            vo.setStorageCode(storageSkuMap.getStorageCode());
            vo.setId(storageSkuMap.getId());

            voList.add(vo);
        });

        return voList;
    }


    public List<StockLogVO> getLogList(String stockSn, StockTypeEnum stockType) {

        List<StockLog> list =  stockLogService.getList(stockSn, stockType.getCode());

        if (list == null || list.isEmpty()) {
            return null;
        }

        List<StockLogVO> result = new ArrayList<>();

        list.forEach(stockLog -> {
            StockLogVO vo = BeanHelper.copy(StockLogVO.class, stockLog);
            if (stockType.equals(StockTypeEnum.IN)) {
                vo.setOperateTypeStr(StockInStatusEnum.getDescByCode(stockLog.getOperateType()));
            } else {
                vo.setOperateTypeStr(StockOutStatusEnum.getDescByCode(stockLog.getOperateType()));
            }

            result.add(vo);
        });

        return result;
    }

}
