package com.ets.delivery.application.common.consts.jd;

import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum JdOpeTitleEnum {

    EXPRESS_PICK("快递签收", ExpressStateEnum.ACCEPT.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "快递签收"),
    EXPRESS_SORTING_INSPECT("分拣中心验货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "分拣中心验货"),
    EXPRESS_SORTING_SORT("分拣中心分拣", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "分拣中心分拣"),
    EXPRESS_SORTING_DELIVER("分拣中心发货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "分拣中心发货"),
    EXPRESS_SORTING_RECEIVE("分拣中心收货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "分拣中心收货"),
    EXPRESS_SITE_RECEIVE("站点收货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "站点收货"),
    EXPRESS_SITE_INSPECT("站点验货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "站点验货"),
    EXPRESS_SITE_DELIVERY("站点装箱发货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "站点装箱发货"),
    EXPRESS_COURIER_RECEIVE("配送员收货", ExpressStateEnum.SENDING.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "配送员收货"),
    EXPRESS_IN_SITE("订单入站", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "订单入站"),
    EXPRESS_DELIVERED("妥投", ExpressStateEnum.RECEIVED.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBE_SHUTDOWN.getValue(), "妥投"),
    EXPRESS_NEGOTIATION_DELIVER_AGAIN("发起协商再投", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "发起协商再投"),
    EXPRESS_DELIVER_AGAIN("再投", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "再投"),
    EXPRESS_REJECT("拒收", ExpressStateEnum.RECEIVER_REJECT.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "拒收"),
    EXPRESS_EXCHANGE("换单打印", ExpressStateEnum.SEND_ANOTHER.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "换单打印"),
    EXPRESS_REVERSE_SORTING_DELIVERY("逆向分拣中心发货", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "逆向分拣中心发货"),
    EXPRESS_ARRIVAL("已到达卸货地", ExpressStateEnum.ON_THE_WAY.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBING.getValue(), "已到达卸货地"),
    EXPRESS_RETURN_FINISH("退货完成", ExpressStateEnum.REJECTED.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBE_SHUTDOWN.getValue(), "退货完成"),
    EXPRESS_CANCEL("取消下单", ExpressStateEnum.ON_TROUBLE.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBE_ABORT.getValue(), "取消下单"),
    EXPRESS_TERMINATION("终止揽收", ExpressStateEnum.ON_TROUBLE.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBE_ABORT.getValue(), "终止揽收"),
    EXPRESS_PICK_TERMINATION("取件终止", ExpressStateEnum.ON_TROUBLE.getValue(), ExpressSubscribeStatusEnum.SUBSCRIBE_ABORT.getValue(), "取件终止");


    private final String value;
    private final Integer state;
    private final Integer subscribeStatus;
    private final String desc;
    public static final Map<String, String> map;
    public static final Map<String, Integer> stateMap;
    public static final Map<String, Integer> subscribeStatusMap;
    public static final List<String> list;

    static {
        JdOpeTitleEnum[] enums = JdOpeTitleEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        stateMap = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getState()),
                Map::putAll);
        subscribeStatusMap = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getSubscribeStatus()),
                Map::putAll);
        list = Stream.of(enums)
                .map(JdOpeTitleEnum::getValue)
                .collect(Collectors.toList());
    }
}
