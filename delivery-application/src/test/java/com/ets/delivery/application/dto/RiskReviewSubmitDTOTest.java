package com.ets.delivery.application.dto;

import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewSubmitDTO;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RiskReviewSubmitDTO 测试类
 */
public class RiskReviewSubmitDTOTest {

    @Test
    public void testValidReviewStatus() {
        RiskReviewSubmitDTO dto = new RiskReviewSubmitDTO();
        
        // 测试有效状态
        dto.setRiskReviewStatus(RiskReviewStatusEnum.APPROVED.getValue());
        assertTrue(dto.isValidReviewStatus());
        
        dto.setRiskReviewStatus(RiskReviewStatusEnum.REJECTED_CANCEL.getValue());
        assertTrue(dto.isValidReviewStatus());
        
        // 测试无效状态
        dto.setRiskReviewStatus(999);
        assertFalse(dto.isValidReviewStatus());
        
        dto.setRiskReviewStatus(null);
        assertFalse(dto.isValidReviewStatus());
    }

    @Test
    public void testValidRejectReason() {
        RiskReviewSubmitDTO dto = new RiskReviewSubmitDTO();
        
        // 测试通过状态，不需要驳回原因
        dto.setRiskReviewStatus(RiskReviewStatusEnum.APPROVED.getValue());
        assertTrue(dto.isValidRejectReason());
        
        // 测试驳回状态，需要驳回原因
        dto.setRiskReviewStatus(RiskReviewStatusEnum.REJECTED_CANCEL.getValue());
        dto.setRejectReasonId(null);
        assertFalse(dto.isValidRejectReason());
        
        dto.setRejectReasonId(0);
        assertFalse(dto.isValidRejectReason());
        
        dto.setRejectReasonId(1);
        assertTrue(dto.isValidRejectReason());
        
        // 测试其他驳回状态
        dto.setRiskReviewStatus(RiskReviewStatusEnum.REJECTED_REUPLOAD.getValue());
        dto.setRejectReasonId(2);
        assertTrue(dto.isValidRejectReason());
        
        dto.setRiskReviewStatus(RiskReviewStatusEnum.SUPPLEMENT_MATERIAL.getValue());
        dto.setRejectReasonId(3);
        assertTrue(dto.isValidRejectReason());
    }

    @Test
    public void testDefaultValues() {
        RiskReviewSubmitDTO dto = new RiskReviewSubmitDTO();
        
        // 测试默认值
        assertFalse(dto.getNeedNext());
    }
}
