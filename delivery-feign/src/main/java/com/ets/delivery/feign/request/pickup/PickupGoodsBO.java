package com.ets.delivery.feign.request.pickup;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
public class PickupGoodsBO {

    @NotEmpty(message = "商品编码不能为空")
    private String goodsCode;

    private String goodsName;

    @NotNull(message = "商品数量不能为空")
    private Integer quantity = 1;

    private Integer deviceType = 0;
}
