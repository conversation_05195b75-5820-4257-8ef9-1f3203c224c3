package com.ets.delivery.feign.request.logistics;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;


@Data
public class SendBackCreateByGoodsDTO {

    @NotEmpty(message = "业务订单号不能为空")
    private String orderSn;

    /**
     * 订单类型
     */
    @NotBlank(message = "订单类型不能为空")
    private String orderType = "goods";

    /**
     * 订单来源
     */
    private String orderSource = "goods";

    /**
     * 所属仓储
     */
    @NotBlank(message = "仓库不能为空")
    private String storageCode;

    @NotNull(message = "sku信息不能为空")
    private List<GoodsSku> skuInfo;

    /**
     * 接收人
     */
    @NotBlank(message = "接收人不能为空")
    private String receiveName;

    /**
     * 接收人联系手机
     */
    @NotBlank(message = "接收人联系手机不能为空")
    private String receivePhone;

    /**
     * 接收地区
     */
    @NotBlank(message = "接收地区不能为空")
    private String receiveArea;

    /**
     * 接收地址
     */
    @NotBlank(message = "接收地址不能为空")
    private String receiveAddress;

    /**
     * 快递单号
     */
    @NotBlank(message = "快递单号不能为空")
    private String expressNumber;

    /**
     * 快递公司
     */
    @NotBlank(message = "快递公司不能为空")
    private String expressCorp;

    /**
     * 回调通知路径
     */
    private String notifyBackUrl;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 发件人联系手机
     */
    private String sendPhone;

    @Data
    public static class GoodsSku {

        // 废弃
        private String skuName;

        private String skuSn;

        private Integer count;
        // card,obu,card_obu
        private String etcType;
        // 是否单片式
        private Boolean isUnit;

    }
}
