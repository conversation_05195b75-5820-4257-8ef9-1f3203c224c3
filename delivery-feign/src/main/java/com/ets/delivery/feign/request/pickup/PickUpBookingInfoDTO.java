package com.ets.delivery.feign.request.pickup;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;

@Data
public class PickUpBookingInfoDTO {

    @NotEmpty(message = "省份不能为空")
    String province;

    @NotEmpty(message = "城市不能为空")
    String city;

    @NotEmpty(message = "地区不能为空")
    String district;

    @NotEmpty(message = "地址不能为空")
    String address;

    Double weight = 0.50;
}
