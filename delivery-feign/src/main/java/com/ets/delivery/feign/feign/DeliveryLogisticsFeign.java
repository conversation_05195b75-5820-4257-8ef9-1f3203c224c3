package com.ets.delivery.feign.feign;

import com.ets.common.JsonResult;
import com.ets.delivery.feign.fallback.DeliveryLogisticsFallbackFactory;
import com.ets.delivery.feign.request.express.SendBackUpdateExpressDTO;
import com.ets.delivery.feign.request.logistics.*;
import com.ets.delivery.feign.response.logistics.AcceptVO;
import com.ets.delivery.feign.response.logistics.AttemptCancelVO;
import com.ets.delivery.feign.response.logistics.LogisticsSendBackVO;
import com.ets.delivery.feign.response.logistics.LogisticsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.validation.Valid;

@FeignClient(
        url = "${microUrls.delivery:http://delivery-application:20130}",
        name = "DeliveryLogisticsFeign", contextId = "deliveryLogistics", fallbackFactory = DeliveryLogisticsFallbackFactory.class)
public interface DeliveryLogisticsFeign {

    @RequestMapping("/no-login/logistics/accept")
    JsonResult<AcceptVO> accept(@RequestBody @Validated LogisticsAcceptDTO logisticsAcceptDTO);

    @RequestMapping("/no-login/logistics/attemptCancel")
    JsonResult<AttemptCancelVO> attemptCancel(@RequestBody @Validated LogisticsAttemptCancelDTO logisticsAttemptCancelDTO);

    @RequestMapping("/no-login/logistics/findByOrderSn")
    JsonResult<LogisticsVO> findByOrderSn(@RequestBody @Valid LogisticsFindByOrderSnDTO findByOrderSnDTO);

    @RequestMapping("/no-login/logistics/cancelByOrderSn")
    JsonResult<?> cancelByOrderSn(@RequestBody @Valid LogisticsCancelByOrderSnDTO cancelByOrderSnDTO);

    @RequestMapping("/no-login/logisticsSendBack/createByGoods")
    JsonResult<LogisticsSendBackVO> createByGoods(@RequestBody @Valid SendBackCreateByGoodsDTO dto);

    @RequestMapping("/no-login/logisticsSendBack/updateExpress")
    JsonResult<Object> updateExpress(@RequestBody @Valid SendBackUpdateExpressDTO dto);
}
