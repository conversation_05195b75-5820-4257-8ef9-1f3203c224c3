package com.ets.delivery.feign.feign;

import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(
        url = "${microUrls.delivery:http://delivery-application:20130}",
        name = "deliverySendBackFeign", contextId = "deliverySendBackFeign", path = "/no-login/logisticsSendBack")
public interface SendBackFeign {

    @RequestMapping("/cancelSendBack")
    JsonResult<Object> cancelSendBack(@RequestParam(value = "orderSn") String orderSn);
}
