package com.ets.delivery.feign.request.pickup;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class PickUpCreateDTO {

    @NotEmpty(message = "业务订单号不能为空")
    String orderSn;

    @NotEmpty(message = "订单类型不能为空")
    String orderType;

    @NotEmpty(message = "订单来源不能为空")
    String orderSource;

    String plateNo;
    Integer plateColor;

    @NotNull(message = "商品不能为空")
    List<PickupGoodsBO> goodsList;

    @NotNull(message = "预约开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime pickUpStartTime;

    @NotNull(message = "预约结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime pickUpEndTime;

    @NotEmpty(message = "寄件人姓名不能为空")
    String sendName;

    @NotEmpty(message = "寄件人省市区不能为空")
    String sendArea;

    @NotEmpty(message = "寄件人地址不能为空")
    String sendAddress;

    @NotEmpty(message = "寄件人手机号码不能为空")
    String sendMobile;

    String notifyBackUrl;

    String sendbackNotifyUrl;

    Integer issuerId;

    String desp = "ETC设备，内含电池";
    Double weight = 0.50;
    String remark = "取件货物为整套ETC设备，需要验货";
    Double volume = 360.00;
}
