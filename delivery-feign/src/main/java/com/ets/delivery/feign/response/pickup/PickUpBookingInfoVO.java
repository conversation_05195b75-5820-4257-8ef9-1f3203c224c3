package com.ets.delivery.feign.response.pickup;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PickUpBookingInfoVO {

    BigDecimal pickUpFee;
    List<BookingCalendar> bookingCalendar;
    String storagePhone;

    @Data
    public static class BookingCalendar {
        String day;
        String pickUpStartTime;
        String pickUpDeadlineTime;
        String downGradeMark;
        List<TimeDetail> timeList;

        @Data
        public static class TimeDetail {
            String startTime;
            String endTime;
            String timeRange;
        }
    }
}
