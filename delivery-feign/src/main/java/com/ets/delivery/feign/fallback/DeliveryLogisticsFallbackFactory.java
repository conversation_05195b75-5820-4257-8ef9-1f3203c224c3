package com.ets.delivery.feign.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.feign.feign.DeliveryLogisticsFeign;
import com.ets.delivery.feign.request.express.SendBackUpdateExpressDTO;
import com.ets.delivery.feign.request.logistics.*;
import com.ets.delivery.feign.response.logistics.AcceptVO;
import com.ets.delivery.feign.response.logistics.AttemptCancelVO;
import com.ets.delivery.feign.response.logistics.LogisticsSendBackVO;
import com.ets.delivery.feign.response.logistics.LogisticsVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;


@Component
public class DeliveryLogisticsFallbackFactory implements FallbackFactory<DeliveryLogisticsFeign> {

    @Override
    public DeliveryLogisticsFeign create(Throwable throwable) {
        return new DeliveryLogisticsFeign() {
            @Override
            public JsonResult<AcceptVO> accept(LogisticsAcceptDTO logisticsAcceptDTO) {
                return JsonResult.error("调用DELIVERY服务【接收发货单】失败：" + logisticsAcceptDTO.toString());
            }

            @Override
            public JsonResult<AttemptCancelVO> attemptCancel(LogisticsAttemptCancelDTO logisticsAttemptCancelDTO) {
                return JsonResult.error("调用DELIVERY服务【取消发货单】失败：" + logisticsAttemptCancelDTO.toString());
            }

            @Override
            public JsonResult<LogisticsVO> findByOrderSn(@RequestBody @Valid LogisticsFindByOrderSnDTO findByOrderSnDTO){
                return JsonResult.error("调用DELIVERY服务【获取发货单】失败：" + findByOrderSnDTO.toString());
            }

            @Override
            public JsonResult<?> cancelByOrderSn(LogisticsCancelByOrderSnDTO cancelByOrderSnDTO) {
                return JsonResult.error("调用DELIVERY服务【取消发货单】失败：" + throwable.getLocalizedMessage());
            }

            @Override
            public JsonResult<LogisticsSendBackVO> createByGoods(@Valid SendBackCreateByGoodsDTO dto) {
                return JsonResult.error("调用DELIVERY服务【创建寄回件】失败：" + throwable.getMessage());
            }

            @Override
            public JsonResult<Object> updateExpress(@Valid SendBackUpdateExpressDTO dto) {
                return JsonResult.error("调用DELIVERY服务【创建寄回件】失败：" + throwable.getMessage());
            }
        };
    }
}
