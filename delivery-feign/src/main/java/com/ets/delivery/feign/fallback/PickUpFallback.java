package com.ets.delivery.feign.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.feign.feign.PickUpFeign;
import com.ets.delivery.feign.request.pickup.PickUpBookingInfoDTO;
import com.ets.delivery.feign.request.pickup.PickUpCancelDTO;
import com.ets.delivery.feign.request.pickup.PickUpCreateByGoodsDTO;
import com.ets.delivery.feign.request.pickup.PickUpCreateDTO;
import com.ets.delivery.feign.response.pickup.PickUpBookingInfoVO;
import com.ets.delivery.feign.response.pickup.PickUpCreateVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import jakarta.validation.Valid;

@Component
public class PickUpFallback implements FallbackFactory<PickUpFeign> {
    @Override
    public PickUpFeign create(Throwable throwable) {
        return new PickUpFeign() {
            @Override
            public JsonResult<PickUpBookingInfoVO> getBookingInfo(@Valid PickUpBookingInfoDTO bookingInfoDTO) {
                return JsonResult.error("调用DELIVERY服务失败：" + throwable.getMessage());
            }

            @Override
            public JsonResult<PickUpCreateVO> create(@Valid PickUpCreateDTO createDTO) {
                return JsonResult.error("调用DELIVERY服务失败：" + throwable.getMessage());
            }

            @Override
            public JsonResult<?> cancel(@Valid PickUpCancelDTO cancelDTO) {
                return JsonResult.error("调用DELIVERY服务失败：" + throwable.getMessage());
            }

            @Override
            public JsonResult<PickUpCreateVO> createByGoods(@Valid PickUpCreateByGoodsDTO dto) {
                return JsonResult.error("调用DELIVERY服务失败：" + throwable.getMessage());
            }
        };
    }
}
