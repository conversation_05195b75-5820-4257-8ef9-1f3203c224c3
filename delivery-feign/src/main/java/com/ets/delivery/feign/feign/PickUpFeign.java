package com.ets.delivery.feign.feign;

import com.ets.common.JsonResult;
import com.ets.delivery.feign.fallback.PickUpFallback;
import com.ets.delivery.feign.request.pickup.PickUpBookingInfoDTO;
import com.ets.delivery.feign.request.pickup.PickUpCancelDTO;
import com.ets.delivery.feign.request.pickup.PickUpCreateByGoodsDTO;
import com.ets.delivery.feign.request.pickup.PickUpCreateDTO;
import com.ets.delivery.feign.response.pickup.PickUpBookingInfoVO;
import com.ets.delivery.feign.response.pickup.PickUpCreateVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.validation.Valid;

@FeignClient(
        url = "${microUrls.delivery:http://delivery-application:20130}",
        name = "deliveryPickUpFeign", contextId = "deliveryPickUpFeign", path = "/no-login/pickUp", fallbackFactory = PickUpFallback.class)
public interface PickUpFeign {

    @RequestMapping("/getBookingInfo")
    JsonResult<PickUpBookingInfoVO> getBookingInfo(@RequestBody @Valid PickUpBookingInfoDTO bookingInfoDTO);

    @RequestMapping("/create")
    JsonResult<PickUpCreateVO> create(@RequestBody @Valid PickUpCreateDTO createDTO);

    @RequestMapping("/cancel")
    JsonResult<?> cancel(@RequestBody @Valid PickUpCancelDTO cancelDTO);

    @RequestMapping("/createByGoods")
    JsonResult<PickUpCreateVO> createByGoods(@RequestBody @Valid PickUpCreateByGoodsDTO dto);
}
