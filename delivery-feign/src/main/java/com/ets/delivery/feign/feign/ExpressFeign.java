package com.ets.delivery.feign.feign;

import com.ets.common.JsonResult;
import com.ets.delivery.feign.fallback.ExpressFallback;
import com.ets.delivery.feign.request.express.ExpressAbnormalStatusDTO;
import com.ets.delivery.feign.request.express.ExpressBatchQueryDTO;
import com.ets.delivery.feign.response.express.ExpressBatchQueryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.validation.Valid;
import java.util.List;

@FeignClient(
        url = "${microUrls.delivery:http://delivery-application:20130}",
        name = "DeliveryExpressFeign", contextId = "DeliveryExpressFeign", path = "/no-login/express", fallbackFactory = ExpressFallback.class)
public interface ExpressFeign {

    @RequestMapping("/batchQuery")
    JsonResult<List<ExpressBatchQueryVO>> batchQuery(@RequestBody @Valid ExpressBatchQueryDTO batchQueryDTO);

    @RequestMapping("/isAbnormalStatus")
    JsonResult<Boolean> isAbnormalStatus(@RequestBody @Valid ExpressAbnormalStatusDTO dto);
}
