package com.ets.delivery.feign.response.express;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ExpressBatchQueryVO {

    /**
     * 物流单号
     */
    private String expressSn;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司编码
     */
    private String expressCompany;

    /**
     * 快递单当前状态，包括 0 在途，1 揽收，2 疑难，3 签收，4 退签，5 派件，6 退回，7 转单，10 待清关，11 清关中，12 已清关，13 清关异常，14 收件人拒签等 13 个状态
     */
    private Integer state;

    private String stateStr;

    /**
     * 快递最新地址
     */
    private String lastArea;

    /**
     * 快递最新更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastExpressTime;

    /**
     * 快递最新信息
     */
    private String lastContext;

    /**
     * 物流最新状态
     */
    private String lastStatus;

    /**
     * 快递最初地址
     */
    private String firstArea;

    /**
     * 快递最初更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime firstExpressTime;

    /**
     * 快递最初信息
     */
    private String firstContext;

    /**
     * 物流最初状态
     */
    private String firstStatus;

    /**
     * 签收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime receivedTime;

    /**
     * 物流轨迹
     */
    private String data;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;
}
