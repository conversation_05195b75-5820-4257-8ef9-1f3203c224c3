package com.ets.delivery.feign.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.feign.feign.ExpressFeign;
import com.ets.delivery.feign.request.express.ExpressAbnormalStatusDTO;
import com.ets.delivery.feign.request.express.ExpressBatchQueryDTO;
import com.ets.delivery.feign.response.express.ExpressBatchQueryVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import jakarta.validation.Valid;
import java.util.List;

@Component
public class ExpressFallback implements FallbackFactory<ExpressFeign> {

    @Override
    public ExpressFeign create(Throwable throwable) {

        return new ExpressFeign() {
            @Override
            public JsonResult<List<ExpressBatchQueryVO>> batchQuery(@Valid ExpressBatchQueryDTO batchQueryDTO) {
                return JsonResult.error("调用DELIVERY服务【批量获取物流信息】失败：" + throwable.getMessage());
            }

            @Override
            public JsonResult<Boolean> isAbnormalStatus(@Valid ExpressAbnormalStatusDTO dto) {
                return JsonResult.error("调用DELIVERY服务【检查是否异常物流状态】失败：" + throwable.getMessage());
            }
        };
    }
}
