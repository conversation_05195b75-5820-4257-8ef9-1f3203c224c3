package com.ets.delivery.feign.request.logistics;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class LogisticsAcceptDTO {
    /*
     * 各方原始订单号
     */
    @NotNull(message = "原始订单号不能为空")
    private String orderSn;
    /*
     * 各方原始订单来源
     */
    private String orderSource = "";

    /**
     * 原始业务单号
     */
    private String originBusinessSn;

    /*
     * 冗余车牌号码
     */
    private String plateNo = "";

    /*
     * 冗余车牌号码
     */
    private Integer plateColor = 0;

    /*
     * 仓库代码
     */

    private String storageCode = "";

    /*
     * 地区
     */
    private String sendArea = "";

    /*
     * 收货人
     */
    @NotNull(message = "收货人不能为空")
    private String sendName;

    /*
     * 收货人手机号码
     */
    @NotNull(message = "收货人手机号码不能为空")
    private String sendPhone;

    /*
     * 收货人地址
     */
    @NotNull(message = "收货人地址不能为空")
    private String sendAddress;

    /*
     * 订单类型
     */
    private String orderType;

    /*
     * 回调通知路径
     */
    @NotNull(message = "回调通知路径不能为空")
    private String notifyBackUrl;

    /*
     * 快递公司编码
     */
    private String expressCorpCode;


    /*
     *  数组集合
     */
    @NotNull(message = "发货列表不能为空")
    private List<Vo> skuList;

    /**
     * 下单原因
     */
    private String reason;

    /**
     * 下单备注
     */
    private String remark;

    private Integer addressConfigId;


    @Data
    public static class Vo{
        /**
         * 商品编码 sku
         */
        @NotNull(message = "商品编码sku不能为空")
        private String sku;
        /**
         *  发货数量
         */
        @NotNull(message = "发货数量不能为空")
        private Integer nums;
    }
}
